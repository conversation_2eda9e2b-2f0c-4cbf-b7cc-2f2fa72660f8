推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 105.8818
  收益率: 0.5131
  距离: 26.5338
  内存使用: 0.5074
  能量使用: 0.7952
  推理时间: 2.3030秒

批次 2:
  奖励值: 110.4280
  收益率: 0.5369
  距离: 26.3293
  内存使用: 0.6137
  能量使用: 0.8011
  推理时间: 2.3536秒

批次 3:
  奖励值: 108.5361
  收益率: 0.5482
  距离: 31.4795
  内存使用: 0.5806
  能量使用: 0.8676
  推理时间: 2.3835秒

批次 4:
  奖励值: 106.2770
  收益率: 0.5283
  距离: 26.9957
  内存使用: 0.5918
  能量使用: 0.8584
  推理时间: 2.3554秒

批次 5:
  奖励值: 98.0594
  收益率: 0.5058
  距离: 28.8995
  内存使用: 0.5231
  能量使用: 0.8586
  推理时间: 2.2223秒

批次 6:
  奖励值: 102.3723
  收益率: 0.5304
  距离: 29.0092
  内存使用: 0.5916
  能量使用: 0.8545
  推理时间: 2.3277秒

批次 7:
  奖励值: 100.2835
  收益率: 0.5085
  距离: 26.4592
  内存使用: 0.5350
  能量使用: 0.8389
  推理时间: 2.2619秒

批次 8:
  奖励值: 100.3862
  收益率: 0.5131
  距离: 25.2757
  内存使用: 0.5926
  能量使用: 0.7592
  推理时间: 2.2708秒

批次 9:
  奖励值: 100.3155
  收益率: 0.5308
  距离: 28.8755
  内存使用: 0.5829
  能量使用: 0.9106
  推理时间: 2.3031秒

批次 10:
  奖励值: 103.7455
  收益率: 0.5087
  距离: 25.9000
  内存使用: 0.5869
  能量使用: 0.8221
  推理时间: 2.2628秒

批次 11:
  奖励值: 106.3041
  收益率: 0.5238
  距离: 29.9913
  内存使用: 0.5461
  能量使用: 0.8546
  推理时间: 2.5275秒

批次 12:
  奖励值: 96.7957
  收益率: 0.4952
  距离: 30.8690
  内存使用: 0.5421
  能量使用: 0.7975
  推理时间: 2.2735秒

批次 13:
  奖励值: 91.3884
  收益率: 0.4820
  距离: 27.2175
  内存使用: 0.4437
  能量使用: 0.7620
  推理时间: 2.1914秒

批次 14:
  奖励值: 100.7185
  收益率: 0.5011
  距离: 26.6198
  内存使用: 0.5949
  能量使用: 0.7483
  推理时间: 2.2209秒

批次 15:
  奖励值: 105.1705
  收益率: 0.5243
  距离: 27.4939
  内存使用: 0.5132
  能量使用: 0.8520
  推理时间: 2.3163秒

批次 16:
  奖励值: 97.7817
  收益率: 0.4969
  距离: 26.1175
  内存使用: 0.4823
  能量使用: 0.8368
  推理时间: 2.1418秒

批次 17:
  奖励值: 100.5284
  收益率: 0.5009
  距离: 26.6863
  内存使用: 0.5080
  能量使用: 0.7489
  推理时间: 2.1849秒

批次 18:
  奖励值: 93.1076
  收益率: 0.4897
  距离: 26.0832
  内存使用: 0.5039
  能量使用: 0.7976
  推理时间: 2.1093秒

批次 19:
  奖励值: 102.0359
  收益率: 0.5080
  距离: 30.8771
  内存使用: 0.6033
  能量使用: 0.8478
  推理时间: 2.3551秒

批次 20:
  奖励值: 96.6170
  收益率: 0.4902
  距离: 26.6695
  内存使用: 0.5246
  能量使用: 0.7719
  推理时间: 2.2580秒

批次 21:
  奖励值: 92.1658
  收益率: 0.4693
  距离: 27.0056
  内存使用: 0.4866
  能量使用: 0.7763
  推理时间: 2.1537秒

批次 22:
  奖励值: 98.1551
  收益率: 0.5111
  距离: 27.9197
  内存使用: 0.5545
  能量使用: 0.8155
  推理时间: 2.2659秒

批次 23:
  奖励值: 104.9446
  收益率: 0.5231
  距离: 28.6947
  内存使用: 0.5464
  能量使用: 0.8696
  推理时间: 2.3916秒

批次 24:
  奖励值: 88.8757
  收益率: 0.4711
  距离: 26.4052
  内存使用: 0.4370
  能量使用: 0.8064
  推理时间: 2.8519秒

批次 25:
  奖励值: 99.2403
  收益率: 0.4913
  距离: 25.9507
  内存使用: 0.5735
  能量使用: 0.7953
  推理时间: 2.2344秒

批次 26:
  奖励值: 96.1200
  收益率: 0.4827
  距离: 26.8477
  内存使用: 0.5240
  能量使用: 0.7882
  推理时间: 2.2283秒

批次 27:
  奖励值: 101.3929
  收益率: 0.5183
  距离: 30.2453
  内存使用: 0.6408
  能量使用: 0.9281
  推理时间: 2.4231秒

批次 28:
  奖励值: 97.8038
  收益率: 0.4919
  距离: 26.5274
  内存使用: 0.4715
  能量使用: 0.7934
  推理时间: 2.2057秒

批次 29:
  奖励值: 97.5751
  收益率: 0.4900
  距离: 25.0188
  内存使用: 0.4496
  能量使用: 0.7814
  推理时间: 2.1707秒

批次 30:
  奖励值: 101.1152
  收益率: 0.5124
  距离: 27.7519
  内存使用: 0.5165
  能量使用: 0.9012
  推理时间: 2.3660秒

批次 31:
  奖励值: 99.9189
  收益率: 0.5002
  距离: 26.0064
  内存使用: 0.5120
  能量使用: 0.8439
  推理时间: 2.3313秒

批次 32:
  奖励值: 103.7416
  收益率: 0.5198
  距离: 26.9159
  内存使用: 0.5878
  能量使用: 0.8132
  推理时间: 2.3819秒

批次 33:
  奖励值: 100.2541
  收益率: 0.5050
  距离: 28.8225
  内存使用: 0.4569
  能量使用: 0.8122
  推理时间: 2.3264秒

批次 34:
  奖励值: 102.6885
  收益率: 0.5295
  距离: 31.5935
  内存使用: 0.5909
  能量使用: 0.8387
  推理时间: 2.3653秒

批次 35:
  奖励值: 90.9944
  收益率: 0.4799
  距离: 28.8148
  内存使用: 0.4521
  能量使用: 0.7443
  推理时间: 2.2299秒

批次 36:
  奖励值: 99.3164
  收益率: 0.4917
  距离: 26.0719
  内存使用: 0.5002
  能量使用: 0.7952
  推理时间: 2.2639秒

批次 37:
  奖励值: 102.7474
  收益率: 0.5161
  距离: 26.4606
  内存使用: 0.5901
  能量使用: 0.8545
  推理时间: 2.4017秒

批次 38:
  奖励值: 103.7747
  收益率: 0.5292
  距离: 32.1215
  内存使用: 0.6211
  能量使用: 0.9079
  推理时间: 2.4844秒

批次 39:
  奖励值: 102.7909
  收益率: 0.5135
  距离: 29.4470
  内存使用: 0.5342
  能量使用: 0.8371
  推理时间: 2.4675秒

批次 40:
  奖励值: 86.5933
  收益率: 0.4629
  距离: 25.2332
  内存使用: 0.4518
  能量使用: 0.7285
  推理时间: 2.0831秒

批次 41:
  奖励值: 102.2379
  收益率: 0.5192
  距离: 29.1487
  内存使用: 0.5631
  能量使用: 0.8125
  推理时间: 2.3702秒

批次 42:
  奖励值: 108.8081
  收益率: 0.5430
  距离: 29.7195
  内存使用: 0.5326
  能量使用: 0.9316
  推理时间: 2.5515秒

批次 43:
  奖励值: 108.1473
  收益率: 0.5325
  距离: 27.8383
  内存使用: 0.5771
  能量使用: 0.8900
  推理时间: 2.5113秒

批次 44:
  奖励值: 98.6231
  收益率: 0.4967
  距离: 25.1318
  内存使用: 0.4757
  能量使用: 0.7462
  推理时间: 2.1852秒

批次 45:
  奖励值: 109.2620
  收益率: 0.5275
  距离: 30.9766
  内存使用: 0.6116
  能量使用: 0.8878
  推理时间: 2.6084秒

批次 46:
  奖励值: 96.3132
  收益率: 0.4857
  距离: 27.2600
  内存使用: 0.4830
  能量使用: 0.8306
  推理时间: 2.1438秒

批次 47:
  奖励值: 97.4702
  收益率: 0.4989
  距离: 25.5856
  内存使用: 0.4764
  能量使用: 0.7976
  推理时间: 2.1193秒

批次 48:
  奖励值: 91.5149
  收益率: 0.4675
  距离: 27.5429
  内存使用: 0.4482
  能量使用: 0.7792
  推理时间: 2.0141秒

批次 49:
  奖励值: 99.3365
  收益率: 0.4911
  距离: 26.4378
  内存使用: 0.5206
  能量使用: 0.7686
  推理时间: 2.1171秒

批次 50:
  奖励值: 99.2549
  收益率: 0.4851
  距离: 25.1978
  内存使用: 0.5344
  能量使用: 0.7579
  推理时间: 2.4319秒

批次 51:
  奖励值: 95.2581
  收益率: 0.4944
  距离: 27.8592
  内存使用: 0.4778
  能量使用: 0.7470
  推理时间: 2.2157秒

批次 52:
  奖励值: 100.1703
  收益率: 0.5065
  距离: 25.2538
  内存使用: 0.5503
  能量使用: 0.8552
  推理时间: 2.3227秒

批次 53:
  奖励值: 97.4216
  收益率: 0.4987
  距离: 27.9841
  内存使用: 0.5415
  能量使用: 0.8313
  推理时间: 2.2513秒

批次 54:
  奖励值: 99.9596
  收益率: 0.4896
  距离: 25.0075
  内存使用: 0.5082
  能量使用: 0.7651
  推理时间: 2.2812秒

批次 55:
  奖励值: 110.7075
  收益率: 0.5644
  距离: 32.3287
  内存使用: 0.5669
  能量使用: 0.8893
  推理时间: 2.5147秒

批次 56:
  奖励值: 101.5961
  收益率: 0.5116
  距离: 28.4553
  内存使用: 0.5133
  能量使用: 0.8393
  推理时间: 2.3138秒

批次 57:
  奖励值: 97.2443
  收益率: 0.4854
  距离: 24.2133
  内存使用: 0.5223
  能量使用: 0.7759
  推理时间: 2.3229秒

批次 58:
  奖励值: 102.9259
  收益率: 0.5104
  距离: 27.6142
  内存使用: 0.5080
  能量使用: 0.8261
  推理时间: 2.4271秒

批次 59:
  奖励值: 97.8635
  收益率: 0.4953
  距离: 25.8955
  内存使用: 0.5273
  能量使用: 0.8503
  推理时间: 2.2053秒

批次 60:
  奖励值: 99.1250
  收益率: 0.4850
  距离: 24.2669
  内存使用: 0.4655
  能量使用: 0.8175
  推理时间: 2.2534秒

批次 61:
  奖励值: 106.3358
  收益率: 0.5310
  距离: 27.3040
  内存使用: 0.5568
  能量使用: 0.8675
  推理时间: 2.4393秒

批次 62:
  奖励值: 102.6747
  收益率: 0.5355
  距离: 29.8235
  内存使用: 0.5132
  能量使用: 0.9138
  推理时间: 2.3521秒

批次 63:
  奖励值: 107.3213
  收益率: 0.5269
  距离: 32.5999
  内存使用: 0.6457
  能量使用: 0.9117
  推理时间: 2.5079秒

批次 64:
  奖励值: 108.5259
  收益率: 0.5392
  距离: 26.6007
  内存使用: 0.4974
  能量使用: 0.8500
  推理时间: 2.2271秒

批次 65:
  奖励值: 102.5180
  收益率: 0.5208
  距离: 30.9538
  内存使用: 0.5877
  能量使用: 0.8225
  推理时间: 2.3653秒

批次 66:
  奖励值: 87.7077
  收益率: 0.4450
  距离: 24.7845
  内存使用: 0.4112
  能量使用: 0.7889
  推理时间: 2.0644秒

批次 67:
  奖励值: 102.9333
  收益率: 0.5015
  距离: 23.5706
  内存使用: 0.5005
  能量使用: 0.8623
  推理时间: 2.3149秒

批次 68:
  奖励值: 97.1032
  收益率: 0.4802
  距离: 25.3442
  内存使用: 0.4997
  能量使用: 0.7497
  推理时间: 2.1825秒

批次 69:
  奖励值: 89.2265
  收益率: 0.4516
  距离: 21.7770
  内存使用: 0.4339
  能量使用: 0.7361
  推理时间: 1.9677秒

批次 70:
  奖励值: 109.2805
  收益率: 0.5386
  距离: 27.1803
  内存使用: 0.5709
  能量使用: 0.8878
  推理时间: 2.4754秒

批次 71:
  奖励值: 105.6301
  收益率: 0.5259
  距离: 28.6985
  内存使用: 0.5820
  能量使用: 0.8594
  推理时间: 2.4115秒

批次 72:
  奖励值: 95.6834
  收益率: 0.5175
  距离: 26.1480
  内存使用: 0.4949
  能量使用: 0.7902
  推理时间: 2.2703秒

批次 73:
  奖励值: 101.4130
  收益率: 0.5128
  距离: 27.7882
  内存使用: 0.5583
  能量使用: 0.8275
  推理时间: 2.3413秒

批次 74:
  奖励值: 103.4685
  收益率: 0.5152
  距离: 29.1259
  内存使用: 0.5806
  能量使用: 0.7565
  推理时间: 2.4818秒

批次 75:
  奖励值: 96.9858
  收益率: 0.4797
  距离: 24.7051
  内存使用: 0.5002
  能量使用: 0.8230
  推理时间: 2.2308秒

批次 76:
  奖励值: 103.0527
  收益率: 0.5159
  距离: 25.6440
  内存使用: 0.5604
  能量使用: 0.8902
  推理时间: 2.2515秒

批次 77:
  奖励值: 101.1994
  收益率: 0.5086
  距离: 26.0855
  内存使用: 0.5212
  能量使用: 0.7944
  推理时间: 2.3743秒

批次 78:
  奖励值: 108.1512
  收益率: 0.5226
  距离: 29.9591
  内存使用: 0.6060
  能量使用: 0.9324
  推理时间: 2.4401秒

批次 79:
  奖励值: 94.8543
  收益率: 0.4840
  距离: 23.8174
  内存使用: 0.4655
  能量使用: 0.8084
  推理时间: 2.2031秒

批次 80:
  奖励值: 94.0516
  收益率: 0.4986
  距离: 25.4155
  内存使用: 0.5001
  能量使用: 0.7812
  推理时间: 2.3015秒

批次 81:
  奖励值: 102.1273
  收益率: 0.5139
  距离: 31.1423
  内存使用: 0.5440
  能量使用: 0.8590
  推理时间: 2.3855秒

批次 82:
  奖励值: 94.5826
  收益率: 0.4795
  距离: 28.9660
  内存使用: 0.4750
  能量使用: 0.7938
  推理时间: 2.2517秒

批次 83:
  奖励值: 98.0920
  收益率: 0.4924
  距离: 28.9010
  内存使用: 0.4766
  能量使用: 0.8385
  推理时间: 2.9006秒

批次 84:
  奖励值: 89.8629
  收益率: 0.4511
  距离: 24.1184
  内存使用: 0.4172
  能量使用: 0.7387
  推理时间: 2.1386秒

批次 85:
  奖励值: 106.6412
  收益率: 0.5327
  距离: 26.7309
  内存使用: 0.5290
  能量使用: 0.8577
  推理时间: 2.4588秒

批次 86:
  奖励值: 93.1110
  收益率: 0.4726
  距离: 29.4591
  内存使用: 0.4741
  能量使用: 0.8448
  推理时间: 2.3856秒

批次 87:
  奖励值: 88.8780
  收益率: 0.4681
  距离: 26.3745
  内存使用: 0.5517
  能量使用: 0.7178
  推理时间: 2.1129秒

批次 88:
  奖励值: 92.8925
  收益率: 0.4865
  距离: 25.8131
  内存使用: 0.4648
  能量使用: 0.7371
  推理时间: 2.1779秒

批次 89:
  奖励值: 96.8039
  收益率: 0.4991
  距离: 27.5172
  内存使用: 0.4891
  能量使用: 0.7861
  推理时间: 2.3564秒

批次 90:
  奖励值: 99.2342
  收益率: 0.5047
  距离: 25.7662
  内存使用: 0.5400
  能量使用: 0.8253
  推理时间: 2.4127秒

批次 91:
  奖励值: 96.4944
  收益率: 0.4844
  距离: 26.2283
  内存使用: 0.5344
  能量使用: 0.8485
  推理时间: 2.3556秒

批次 92:
  奖励值: 96.4310
  收益率: 0.5139
  距离: 30.6169
  内存使用: 0.5276
  能量使用: 0.7936
  推理时间: 2.3555秒

批次 93:
  奖励值: 106.7191
  收益率: 0.5439
  距离: 29.1390
  内存使用: 0.5099
  能量使用: 0.9129
  推理时间: 2.6130秒

批次 94:
  奖励值: 96.9676
  收益率: 0.5142
  距离: 25.5313
  内存使用: 0.5146
  能量使用: 0.8190
  推理时间: 2.4252秒

批次 95:
  奖励值: 100.8059
  收益率: 0.5033
  距离: 26.8598
  内存使用: 0.5134
  能量使用: 0.8149
  推理时间: 2.3634秒

批次 96:
  奖励值: 90.8189
  收益率: 0.4726
  距离: 25.7238
  内存使用: 0.4449
  能量使用: 0.7935
  推理时间: 2.2096秒

批次 97:
  奖励值: 100.2480
  收益率: 0.5002
  距离: 28.3703
  内存使用: 0.5448
  能量使用: 0.7889
  推理时间: 2.3824秒

批次 98:
  奖励值: 91.9959
  收益率: 0.4909
  距离: 26.1184
  内存使用: 0.4855
  能量使用: 0.8044
  推理时间: 2.2812秒

批次 99:
  奖励值: 106.2897
  收益率: 0.5323
  距离: 29.6334
  内存使用: 0.5411
  能量使用: 0.8460
  推理时间: 2.6234秒

批次 100:
  奖励值: 99.5804
  收益率: 0.4937
  距离: 27.8944
  内存使用: 0.5058
  能量使用: 0.8390
  推理时间: 2.5505秒


==================== 总结 ====================
平均收益率: 0.5043
平均能量使用: 0.8213
平均推理时间: 2.3235秒
