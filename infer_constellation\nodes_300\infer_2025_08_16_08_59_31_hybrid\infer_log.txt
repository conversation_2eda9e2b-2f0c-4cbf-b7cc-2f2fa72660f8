推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 67.0989
  收益率: 0.5379
  距离: 17.1036
  内存使用: 0.1797
  能量使用: 0.5190
  推理时间: 1.5740秒

批次 2:
  奖励值: 56.3791
  收益率: 0.4765
  距离: 16.2376
  内存使用: 0.1474
  能量使用: 0.4567
  推理时间: 1.4285秒

批次 3:
  奖励值: 61.3959
  收益率: 0.5036
  距离: 14.7287
  内存使用: 0.1843
  能量使用: 0.4952
  推理时间: 1.4545秒

批次 4:
  奖励值: 58.2144
  收益率: 0.4818
  距离: 14.9593
  内存使用: 0.1473
  能量使用: 0.4646
  推理时间: 1.3755秒

批次 5:
  奖励值: 62.2520
  收益率: 0.4854
  距离: 17.4093
  内存使用: 0.1881
  能量使用: 0.4986
  推理时间: 1.4798秒

批次 6:
  奖励值: 63.0177
  收益率: 0.5050
  距离: 15.2747
  内存使用: 0.1827
  能量使用: 0.5072
  推理时间: 1.5493秒

批次 7:
  奖励值: 62.5684
  收益率: 0.5100
  距离: 17.1572
  内存使用: 0.1770
  能量使用: 0.5291
  推理时间: 1.3835秒

批次 8:
  奖励值: 61.4776
  收益率: 0.5185
  距离: 16.8113
  内存使用: 0.1957
  能量使用: 0.4379
  推理时间: 1.3296秒

批次 9:
  奖励值: 60.8845
  收益率: 0.4839
  距离: 13.1538
  内存使用: 0.1813
  能量使用: 0.4958
  推理时间: 1.2871秒

批次 10:
  奖励值: 55.0360
  收益率: 0.4650
  距离: 14.1486
  内存使用: 0.1985
  能量使用: 0.3809
  推理时间: 1.1995秒

批次 11:
  奖励值: 63.7463
  收益率: 0.5088
  距离: 16.1531
  内存使用: 0.1886
  能量使用: 0.5657
  推理时间: 1.3893秒

批次 12:
  奖励值: 65.8460
  收益率: 0.5412
  距离: 15.8754
  内存使用: 0.2554
  能量使用: 0.5773
  推理时间: 1.4355秒

批次 13:
  奖励值: 55.7084
  收益率: 0.4822
  距离: 15.2456
  内存使用: 0.1076
  能量使用: 0.4301
  推理时间: 1.1834秒

批次 14:
  奖励值: 60.3222
  收益率: 0.4916
  距离: 13.7684
  内存使用: 0.1760
  能量使用: 0.4111
  推理时间: 1.3463秒

批次 15:
  奖励值: 65.5164
  收益率: 0.5185
  距离: 13.7980
  内存使用: 0.2681
  能量使用: 0.5011
  推理时间: 1.4124秒

批次 16:
  奖励值: 57.1155
  收益率: 0.4787
  距离: 15.5261
  内存使用: 0.1344
  能量使用: 0.5084
  推理时间: 1.3176秒

批次 17:
  奖励值: 61.5414
  收益率: 0.5150
  距离: 16.7733
  内存使用: 0.2553
  能量使用: 0.4887
  推理时间: 1.4507秒

批次 18:
  奖励值: 54.4914
  收益率: 0.4758
  距离: 16.1967
  内存使用: 0.1401
  能量使用: 0.4804
  推理时间: 1.2418秒

批次 19:
  奖励值: 63.0935
  收益率: 0.5145
  距离: 15.2676
  内存使用: 0.2165
  能量使用: 0.5290
  推理时间: 1.3436秒

批次 20:
  奖励值: 55.4736
  收益率: 0.4897
  距离: 16.2157
  内存使用: 0.1812
  能量使用: 0.4558
  推理时间: 1.2735秒

批次 21:
  奖励值: 59.2181
  收益率: 0.4895
  距离: 15.8432
  内存使用: 0.1985
  能量使用: 0.5221
  推理时间: 1.3137秒

批次 22:
  奖励值: 61.8899
  收益率: 0.5175
  距离: 14.7448
  内存使用: 0.1863
  能量使用: 0.4892
  推理时间: 1.3584秒

批次 23:
  奖励值: 59.4762
  收益率: 0.5109
  距离: 16.3783
  内存使用: 0.1591
  能量使用: 0.5103
  推理时间: 1.3141秒

批次 24:
  奖励值: 62.3142
  收益率: 0.5007
  距离: 18.2785
  内存使用: 0.1762
  能量使用: 0.5168
  推理时间: 1.4934秒

批次 25:
  奖励值: 50.9333
  收益率: 0.4485
  距离: 15.9066
  内存使用: 0.1737
  能量使用: 0.4531
  推理时间: 1.1284秒

批次 26:
  奖励值: 54.1732
  收益率: 0.4470
  距离: 13.4909
  内存使用: 0.1710
  能量使用: 0.3916
  推理时间: 2.5194秒

批次 27:
  奖励值: 60.8741
  收益率: 0.4791
  距离: 14.7658
  内存使用: 0.1764
  能量使用: 0.5096
  推理时间: 1.3252秒

批次 28:
  奖励值: 62.8978
  收益率: 0.5057
  距离: 15.6540
  内存使用: 0.2199
  能量使用: 0.5306
  推理时间: 1.3638秒

批次 29:
  奖励值: 74.4847
  收益率: 0.5497
  距离: 18.1690
  内存使用: 0.2961
  能量使用: 0.5655
  推理时间: 1.5585秒

批次 30:
  奖励值: 62.6292
  收益率: 0.5170
  距离: 17.0251
  内存使用: 0.2425
  能量使用: 0.4740
  推理时间: 1.3642秒

批次 31:
  奖励值: 63.0138
  收益率: 0.5178
  距离: 15.4682
  内存使用: 0.1960
  能量使用: 0.5082
  推理时间: 1.3508秒

批次 32:
  奖励值: 68.6223
  收益率: 0.5431
  距离: 16.9331
  内存使用: 0.2302
  能量使用: 0.5920
  推理时间: 1.5315秒

批次 33:
  奖励值: 59.9444
  收益率: 0.5129
  距离: 15.1264
  内存使用: 0.1499
  能量使用: 0.4350
  推理时间: 1.3322秒

批次 34:
  奖励值: 56.4020
  收益率: 0.4965
  距离: 14.6935
  内存使用: 0.2103
  能量使用: 0.4922
  推理时间: 1.2343秒

批次 35:
  奖励值: 64.9190
  收益率: 0.5316
  距离: 15.7185
  内存使用: 0.2087
  能量使用: 0.4348
  推理时间: 1.3790秒

批次 36:
  奖励值: 64.9112
  收益率: 0.5401
  距离: 17.5364
  内存使用: 0.2715
  能量使用: 0.5698
  推理时间: 1.4537秒

批次 37:
  奖励值: 61.6988
  收益率: 0.5047
  距离: 15.0918
  内存使用: 0.2397
  能量使用: 0.4709
  推理时间: 1.3239秒

批次 38:
  奖励值: 61.6745
  收益率: 0.5047
  距离: 15.9762
  内存使用: 0.1821
  能量使用: 0.4927
  推理时间: 1.3358秒

批次 39:
  奖励值: 54.2797
  收益率: 0.4743
  距离: 14.6219
  内存使用: 0.1703
  能量使用: 0.4484
  推理时间: 1.2333秒

批次 40:
  奖励值: 72.3406
  收益率: 0.5734
  距离: 17.0010
  内存使用: 0.2127
  能量使用: 0.5051
  推理时间: 1.4899秒

批次 41:
  奖励值: 53.7241
  收益率: 0.4642
  距离: 15.0637
  内存使用: 0.0695
  能量使用: 0.4185
  推理时间: 1.1345秒

批次 42:
  奖励值: 63.3105
  收益率: 0.5144
  距离: 15.8659
  内存使用: 0.2214
  能量使用: 0.4917
  推理时间: 1.3521秒

批次 43:
  奖励值: 58.6581
  收益率: 0.4831
  距离: 16.2991
  内存使用: 0.1662
  能量使用: 0.4185
  推理时间: 1.2223秒

批次 44:
  奖励值: 59.9004
  收益率: 0.5037
  距离: 14.9601
  内存使用: 0.1404
  能量使用: 0.4987
  推理时间: 1.2665秒

批次 45:
  奖励值: 62.2079
  收益率: 0.5201
  距离: 14.6884
  内存使用: 0.2083
  能量使用: 0.4876
  推理时间: 1.3822秒

批次 46:
  奖励值: 61.7581
  收益率: 0.4996
  距离: 14.9201
  内存使用: 0.2193
  能量使用: 0.5444
  推理时间: 1.3459秒

批次 47:
  奖励值: 63.6075
  收益率: 0.5131
  距离: 17.1934
  内存使用: 0.2611
  能量使用: 0.5306
  推理时间: 1.3848秒

批次 48:
  奖励值: 60.3685
  收益率: 0.4917
  距离: 15.4477
  内存使用: 0.1620
  能量使用: 0.4532
  推理时间: 1.2529秒

批次 49:
  奖励值: 61.3148
  收益率: 0.5103
  距离: 15.7022
  内存使用: 0.1626
  能量使用: 0.5056
  推理时间: 1.2975秒

批次 50:
  奖励值: 61.9457
  收益率: 0.5158
  距离: 15.6346
  内存使用: 0.2260
  能量使用: 0.4479
  推理时间: 1.3252秒

批次 51:
  奖励值: 63.4274
  收益率: 0.5252
  距离: 16.2189
  内存使用: 0.2023
  能量使用: 0.5240
  推理时间: 1.3847秒

批次 52:
  奖励值: 63.4843
  收益率: 0.5251
  距离: 14.6954
  内存使用: 0.2609
  能量使用: 0.4750
  推理时间: 1.3502秒

批次 53:
  奖励值: 53.9221
  收益率: 0.4536
  距离: 15.8342
  内存使用: 0.1238
  能量使用: 0.3978
  推理时间: 1.1293秒

批次 54:
  奖励值: 50.1155
  收益率: 0.4411
  距离: 15.2409
  内存使用: 0.1102
  能量使用: 0.4179
  推理时间: 1.1228秒

批次 55:
  奖励值: 55.8314
  收益率: 0.4763
  距离: 16.1660
  内存使用: 0.1549
  能量使用: 0.4162
  推理时间: 1.2215秒

批次 56:
  奖励值: 54.8557
  收益率: 0.4642
  距离: 12.9611
  内存使用: 0.1422
  能量使用: 0.3854
  推理时间: 1.1557秒

批次 57:
  奖励值: 63.4968
  收益率: 0.4934
  距离: 14.9097
  内存使用: 0.2198
  能量使用: 0.4769
  推理时间: 1.3445秒

批次 58:
  奖励值: 63.7399
  收益率: 0.5089
  距离: 15.2050
  内存使用: 0.2164
  能量使用: 0.4987
  推理时间: 1.3346秒

批次 59:
  奖励值: 60.9237
  收益率: 0.5109
  距离: 18.0276
  内存使用: 0.2150
  能量使用: 0.5101
  推理时间: 1.3527秒

批次 60:
  奖励值: 61.4422
  收益率: 0.5055
  距离: 14.8007
  内存使用: 0.2426
  能量使用: 0.4723
  推理时间: 1.3160秒

批次 61:
  奖励值: 52.5775
  收益率: 0.4538
  距离: 13.0925
  内存使用: 0.1143
  能量使用: 0.4383
  推理时间: 1.1171秒

批次 62:
  奖励值: 58.9445
  收益率: 0.4948
  距离: 15.6068
  内存使用: 0.2032
  能量使用: 0.5009
  推理时间: 1.3039秒

批次 63:
  奖励值: 56.3797
  收益率: 0.4757
  距离: 14.0786
  内存使用: 0.1330
  能量使用: 0.4289
  推理时间: 1.1730秒

批次 64:
  奖励值: 59.0562
  收益率: 0.4901
  距离: 15.0184
  内存使用: 0.1910
  能量使用: 0.4781
  推理时间: 1.2935秒

批次 65:
  奖励值: 63.7721
  收益率: 0.5174
  距离: 17.1411
  内存使用: 0.2315
  能量使用: 0.4832
  推理时间: 1.3288秒

批次 66:
  奖励值: 64.3846
  收益率: 0.5084
  距离: 15.3174
  内存使用: 0.2058
  能量使用: 0.5076
  推理时间: 1.3661秒

批次 67:
  奖励值: 53.8752
  收益率: 0.4658
  距离: 15.4338
  内存使用: 0.1665
  能量使用: 0.4493
  推理时间: 1.2001秒

批次 68:
  奖励值: 62.3247
  收益率: 0.4937
  距离: 15.2857
  内存使用: 0.1937
  能量使用: 0.4712
  推理时间: 1.3236秒

批次 69:
  奖励值: 66.4987
  收益率: 0.5459
  距离: 18.3846
  内存使用: 0.2731
  能量使用: 0.5449
  推理时间: 1.4861秒

批次 70:
  奖励值: 61.8299
  收益率: 0.5204
  距离: 16.0176
  内存使用: 0.2326
  能量使用: 0.4901
  推理时间: 1.3589秒

批次 71:
  奖励值: 53.4290
  收益率: 0.4466
  距离: 14.5771
  内存使用: 0.1399
  能量使用: 0.4249
  推理时间: 1.3072秒

批次 72:
  奖励值: 63.9662
  收益率: 0.5145
  距离: 13.3927
  内存使用: 0.2089
  能量使用: 0.4659
  推理时间: 1.3577秒

批次 73:
  奖励值: 62.9205
  收益率: 0.5201
  距离: 15.7276
  内存使用: 0.1621
  能量使用: 0.4694
  推理时间: 1.3785秒

批次 74:
  奖励值: 61.3723
  收益率: 0.5182
  距离: 18.7570
  内存使用: 0.1809
  能量使用: 0.5175
  推理时间: 1.3399秒

批次 75:
  奖励值: 52.0598
  收益率: 0.4572
  距离: 13.9569
  内存使用: 0.1466
  能量使用: 0.3769
  推理时间: 1.1169秒

批次 76:
  奖励值: 65.0166
  收益率: 0.5209
  距离: 14.0730
  内存使用: 0.1974
  能量使用: 0.4963
  推理时间: 1.3939秒

批次 77:
  奖励值: 64.9560
  收益率: 0.5305
  距离: 15.5442
  内存使用: 0.2351
  能量使用: 0.5088
  推理时间: 1.3363秒

批次 78:
  奖励值: 64.6002
  收益率: 0.5239
  距离: 16.8628
  内存使用: 0.2501
  能量使用: 0.4868
  推理时间: 1.3667秒

批次 79:
  奖励值: 54.2177
  收益率: 0.4579
  距离: 13.7869
  内存使用: 0.1375
  能量使用: 0.4403
  推理时间: 1.1904秒

批次 80:
  奖励值: 64.9385
  收益率: 0.5205
  距离: 16.8489
  内存使用: 0.1985
  能量使用: 0.4755
  推理时间: 1.3830秒

批次 81:
  奖励值: 54.5089
  收益率: 0.4657
  距离: 12.6018
  内存使用: 0.1327
  能量使用: 0.4476
  推理时间: 1.1267秒

批次 82:
  奖励值: 64.2811
  收益率: 0.5070
  距离: 15.5389
  内存使用: 0.2068
  能量使用: 0.4921
  推理时间: 1.3516秒

批次 83:
  奖励值: 66.8921
  收益率: 0.5309
  距离: 16.8953
  内存使用: 0.2385
  能量使用: 0.4820
  推理时间: 1.4423秒

批次 84:
  奖励值: 63.2858
  收益率: 0.5250
  距离: 15.7609
  内存使用: 0.2074
  能量使用: 0.4601
  推理时间: 1.3584秒

批次 85:
  奖励值: 54.3716
  收益率: 0.4573
  距离: 14.9055
  内存使用: 0.1358
  能量使用: 0.4157
  推理时间: 1.1980秒

批次 86:
  奖励值: 57.1934
  收益率: 0.4892
  距离: 15.7694
  内存使用: 0.1950
  能量使用: 0.4488
  推理时间: 1.2721秒

批次 87:
  奖励值: 58.1076
  收益率: 0.4752
  距离: 15.1766
  内存使用: 0.1902
  能量使用: 0.4180
  推理时间: 1.2374秒

批次 88:
  奖励值: 60.3590
  收益率: 0.4968
  距离: 17.0311
  内存使用: 0.2037
  能量使用: 0.4472
  推理时间: 1.2791秒

批次 89:
  奖励值: 57.9683
  收益率: 0.4722
  距离: 14.0615
  内存使用: 0.1426
  能量使用: 0.4593
  推理时间: 1.2211秒

批次 90:
  奖励值: 66.8588
  收益率: 0.5409
  距离: 18.9166
  内存使用: 0.2756
  能量使用: 0.4901
  推理时间: 1.4150秒

批次 91:
  奖励值: 59.0014
  收益率: 0.4894
  距离: 15.5966
  内存使用: 0.1492
  能量使用: 0.4514
  推理时间: 1.2554秒

批次 92:
  奖励值: 61.3351
  收益率: 0.5266
  距离: 19.2048
  内存使用: 0.2110
  能量使用: 0.5140
  推理时间: 1.3794秒

批次 93:
  奖励值: 60.9257
  收益率: 0.5007
  距离: 14.1192
  内存使用: 0.1868
  能量使用: 0.4427
  推理时间: 1.2428秒

批次 94:
  奖励值: 61.2032
  收益率: 0.5091
  距离: 17.3506
  内存使用: 0.1790
  能量使用: 0.4639
  推理时间: 1.3802秒

批次 95:
  奖励值: 56.4682
  收益率: 0.5015
  距离: 16.5219
  内存使用: 0.1716
  能量使用: 0.5702
  推理时间: 1.3165秒

批次 96:
  奖励值: 64.5101
  收益率: 0.5197
  距离: 15.5118
  内存使用: 0.1953
  能量使用: 0.4727
  推理时间: 1.4415秒

批次 97:
  奖励值: 57.8325
  收益率: 0.4781
  距离: 14.9674
  内存使用: 0.1314
  能量使用: 0.4362
  推理时间: 1.2242秒

批次 98:
  奖励值: 65.9733
  收益率: 0.5521
  距离: 18.8882
  内存使用: 0.2568
  能量使用: 0.5085
  推理时间: 1.4401秒

批次 99:
  奖励值: 60.9825
  收益率: 0.5030
  距离: 13.4887
  内存使用: 0.1257
  能量使用: 0.4206
  推理时间: 1.3137秒

批次 100:
  奖励值: 64.2870
  收益率: 0.5294
  距离: 14.9605
  内存使用: 0.1807
  能量使用: 0.4849
  推理时间: 1.3417秒


==================== 总结 ====================
平均收益率: 0.5011
平均能量使用: 0.4780
平均推理时间: 1.3389秒
