推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 142.7364
  收益率: 0.1787
  距离: 32.5612
  内存使用: 0.7652
  能量使用: 1.0009
  推理时间: 2.8836秒

批次 2:
  奖励值: 118.2005
  收益率: 0.1496
  距离: 30.1692
  内存使用: 0.8999
  能量使用: 0.9298
  推理时间: 2.4898秒

批次 3:
  奖励值: 138.0676
  收益率: 0.1735
  距离: 35.4083
  内存使用: 0.6764
  能量使用: 0.9802
  推理时间: 2.7657秒

批次 4:
  奖励值: 146.9372
  收益率: 0.1821
  距离: 37.6935
  内存使用: 0.8681
  能量使用: 1.1640
  推理时间: 3.0262秒

批次 5:
  奖励值: 143.1304
  收益率: 0.1830
  距离: 36.6171
  内存使用: 0.7858
  能量使用: 1.1363
  推理时间: 2.8687秒

批次 6:
  奖励值: 144.8864
  收益率: 0.1817
  距离: 37.9808
  内存使用: 0.7474
  能量使用: 1.0990
  推理时间: 2.8486秒

批次 7:
  奖励值: 145.9971
  收益率: 0.1829
  距离: 38.6524
  内存使用: 0.8167
  能量使用: 1.1368
  推理时间: 2.9376秒

批次 8:
  奖励值: 130.7714
  收益率: 0.1634
  距离: 32.6249
  内存使用: 0.6425
  能量使用: 0.9898
  推理时间: 2.6049秒

批次 9:
  奖励值: 138.5007
  收益率: 0.1706
  距离: 33.2583
  内存使用: 0.6871
  能量使用: 1.0531
  推理时间: 2.7638秒

批次 10:
  奖励值: 128.9661
  收益率: 0.1633
  距离: 34.2032
  内存使用: 0.6460
  能量使用: 1.0185
  推理时间: 2.5612秒

批次 11:
  奖励值: 149.9444
  收益率: 0.1924
  距离: 38.9808
  内存使用: 0.8004
  能量使用: 1.1301
  推理时间: 2.9576秒

批次 12:
  奖励值: 121.5806
  收益率: 0.1525
  距离: 29.0506
  内存使用: 0.6477
  能量使用: 0.9406
  推理时间: 2.4537秒

批次 13:
  奖励值: 138.7126
  收益率: 0.1745
  距离: 36.1186
  内存使用: 0.7670
  能量使用: 1.0892
  推理时间: 2.8659秒

批次 14:
  奖励值: 155.5308
  收益率: 0.1956
  距离: 37.9911
  内存使用: 0.8947
  能量使用: 1.2524
  推理时间: 3.1432秒

批次 15:
  奖励值: 125.7478
  收益率: 0.1580
  距离: 32.7574
  内存使用: 0.6249
  能量使用: 0.9219
  推理时间: 2.5831秒

批次 16:
  奖励值: 129.6387
  收益率: 0.1627
  距离: 34.9695
  内存使用: 0.6954
  能量使用: 1.0577
  推理时间: 2.6281秒

批次 17:
  奖励值: 141.7241
  收益率: 0.1765
  距离: 33.5503
  内存使用: 0.6909
  能量使用: 1.1104
  推理时间: 2.7968秒

批次 18:
  奖励值: 142.3241
  收益率: 0.1805
  距离: 38.6075
  内存使用: 0.8064
  能量使用: 1.1380
  推理时间: 3.0089秒

批次 19:
  奖励值: 141.5397
  收益率: 0.1780
  距离: 33.9572
  内存使用: 0.6764
  能量使用: 1.0871
  推理时间: 2.9115秒

批次 20:
  奖励值: 143.2904
  收益率: 0.1822
  距离: 36.2731
  内存使用: 0.8167
  能量使用: 1.1293
  推理时间: 2.8612秒

批次 21:
  奖励值: 127.2007
  收益率: 0.1607
  距离: 29.1873
  内存使用: 0.6377
  能量使用: 0.9736
  推理时间: 2.6076秒

批次 22:
  奖励值: 134.7304
  收益率: 0.1742
  距离: 37.1368
  内存使用: 0.7110
  能量使用: 1.0914
  推理时间: 2.7503秒

批次 23:
  奖励值: 135.8467
  收益率: 0.1710
  距离: 37.7178
  内存使用: 0.7123
  能量使用: 1.0765
  推理时间: 2.6705秒

批次 24:
  奖励值: 134.9889
  收益率: 0.1681
  距离: 33.7609
  内存使用: 0.6691
  能量使用: 0.9922
  推理时间: 2.6437秒

批次 25:
  奖励值: 115.6766
  收益率: 0.1463
  距离: 31.0904
  内存使用: 0.6027
  能量使用: 0.9537
  推理时间: 2.3783秒

批次 26:
  奖励值: 134.4612
  收益率: 0.1724
  距离: 35.1348
  内存使用: 0.6885
  能量使用: 1.0543
  推理时间: 2.7239秒

批次 27:
  奖励值: 137.9281
  收益率: 0.1744
  距离: 37.7121
  内存使用: 0.8076
  能量使用: 1.0242
  推理时间: 2.7358秒

批次 28:
  奖励值: 125.9261
  收益率: 0.1555
  距离: 31.2634
  内存使用: 0.8998
  能量使用: 0.9846
  推理时间: 2.5690秒

批次 29:
  奖励值: 144.5470
  收益率: 0.1834
  距离: 37.8453
  内存使用: 0.7611
  能量使用: 1.0995
  推理时间: 2.8262秒

批次 30:
  奖励值: 144.3479
  收益率: 0.1817
  距离: 38.5424
  内存使用: 0.8681
  能量使用: 1.0937
  推理时间: 2.9795秒

批次 31:
  奖励值: 135.4076
  收益率: 0.1715
  距离: 35.0423
  内存使用: 0.7096
  能量使用: 1.0328
  推理时间: 2.6131秒

批次 32:
  奖励值: 146.7566
  收益率: 0.1843
  距离: 37.8662
  内存使用: 0.8072
  能量使用: 1.2134
  推理时间: 2.9902秒

批次 33:
  奖励值: 137.5595
  收益率: 0.1737
  距离: 36.0438
  内存使用: 0.7562
  能量使用: 1.0809
  推理时间: 2.8833秒

批次 34:
  奖励值: 152.2638
  收益率: 0.1892
  距离: 36.8581
  内存使用: 0.8545
  能量使用: 1.1599
  推理时间: 3.0874秒

批次 35:
  奖励值: 128.7046
  收益率: 0.1580
  距离: 31.1816
  内存使用: 0.6511
  能量使用: 0.9580
  推理时间: 2.5990秒

批次 36:
  奖励值: 135.3988
  收益率: 0.1703
  距离: 37.6105
  内存使用: 0.7141
  能量使用: 1.0779
  推理时间: 2.8315秒

批次 37:
  奖励值: 121.6887
  收益率: 0.1566
  距离: 33.6115
  内存使用: 0.6637
  能量使用: 0.9474
  推理时间: 2.5574秒

批次 38:
  奖励值: 135.8155
  收益率: 0.1721
  距离: 30.5434
  内存使用: 0.8199
  能量使用: 1.0055
  推理时间: 2.7014秒

批次 39:
  奖励值: 130.2843
  收益率: 0.1664
  距离: 35.5797
  内存使用: 0.6268
  能量使用: 1.0248
  推理时间: 2.7130秒

批次 40:
  奖励值: 126.1938
  收益率: 0.1571
  距离: 35.5565
  内存使用: 0.6643
  能量使用: 0.9916
  推理时间: 2.6576秒

批次 41:
  奖励值: 143.9068
  收益率: 0.1802
  距离: 37.1172
  内存使用: 0.7254
  能量使用: 1.1437
  推理时间: 2.9345秒

批次 42:
  奖励值: 107.2582
  收益率: 0.1362
  距离: 31.0938
  内存使用: 0.5306
  能量使用: 0.8591
  推理时间: 2.2065秒

批次 43:
  奖励值: 131.1240
  收益率: 0.1618
  距离: 30.7131
  内存使用: 0.7188
  能量使用: 1.1201
  推理时间: 3.5860秒

批次 44:
  奖励值: 153.5380
  收益率: 0.1931
  距离: 39.6683
  内存使用: 0.8236
  能量使用: 1.2320
  推理时间: 3.1241秒

批次 45:
  奖励值: 127.6387
  收益率: 0.1592
  距离: 31.4635
  内存使用: 0.6838
  能量使用: 0.9753
  推理时间: 2.6279秒

批次 46:
  奖励值: 148.7912
  收益率: 0.1930
  距离: 39.0181
  内存使用: 0.8407
  能量使用: 1.1588
  推理时间: 2.9113秒

批次 47:
  奖励值: 126.6346
  收益率: 0.1585
  距离: 33.6770
  内存使用: 0.6349
  能量使用: 1.0189
  推理时间: 2.6212秒

批次 48:
  奖励值: 148.8595
  收益率: 0.1873
  距离: 39.6954
  内存使用: 0.7860
  能量使用: 1.1995
  推理时间: 3.0527秒

批次 49:
  奖励值: 144.0977
  收益率: 0.1848
  距离: 39.5418
  内存使用: 0.7179
  能量使用: 1.1448
  推理时间: 2.9756秒

批次 50:
  奖励值: 149.7847
  收益率: 0.1852
  距离: 34.1389
  内存使用: 0.8532
  能量使用: 1.0384
  推理时间: 2.9694秒

批次 51:
  奖励值: 157.9341
  收益率: 0.2000
  距离: 38.2863
  内存使用: 0.8304
  能量使用: 1.2152
  推理时间: 3.1824秒

批次 52:
  奖励值: 152.6332
  收益率: 0.1892
  距离: 38.9651
  内存使用: 0.8586
  能量使用: 1.1098
  推理时间: 3.0975秒

批次 53:
  奖励值: 155.4395
  收益率: 0.1943
  距离: 38.2587
  内存使用: 0.8530
  能量使用: 1.2092
  推理时间: 3.0915秒

批次 54:
  奖励值: 136.6194
  收益率: 0.1726
  距离: 33.7637
  内存使用: 0.6528
  能量使用: 1.1446
  推理时间: 2.7329秒

批次 55:
  奖励值: 140.6762
  收益率: 0.1778
  距离: 36.0439
  内存使用: 0.8707
  能量使用: 1.1330
  推理时间: 2.9725秒

批次 56:
  奖励值: 141.3678
  收益率: 0.1774
  距离: 34.3535
  内存使用: 0.8016
  能量使用: 1.1092
  推理时间: 2.7743秒

批次 57:
  奖励值: 141.2600
  收益率: 0.1774
  距离: 36.2274
  内存使用: 0.7325
  能量使用: 1.1201
  推理时间: 2.8733秒

批次 58:
  奖励值: 154.5509
  收益率: 0.1938
  距离: 38.8860
  内存使用: 0.8616
  能量使用: 1.1506
  推理时间: 2.9571秒

批次 59:
  奖励值: 146.8077
  收益率: 0.1869
  距离: 39.9622
  内存使用: 0.7811
  能量使用: 1.0413
  推理时间: 2.9586秒

批次 60:
  奖励值: 126.7245
  收益率: 0.1642
  距离: 35.3390
  内存使用: 0.6406
  能量使用: 0.9713
  推理时间: 2.5508秒

批次 61:
  奖励值: 121.1446
  收益率: 0.1517
  距离: 31.1982
  内存使用: 0.8998
  能量使用: 1.0127
  推理时间: 2.5367秒

批次 62:
  奖励值: 132.5540
  收益率: 0.1644
  距离: 32.9969
  内存使用: 0.6568
  能量使用: 1.0382
  推理时间: 2.7542秒

批次 63:
  奖励值: 135.4661
  收益率: 0.1702
  距离: 34.9825
  内存使用: 0.6917
  能量使用: 0.9934
  推理时间: 2.6564秒

批次 64:
  奖励值: 140.6339
  收益率: 0.1780
  距离: 35.7006
  内存使用: 0.7580
  能量使用: 1.0736
  推理时间: 2.8403秒

批次 65:
  奖励值: 143.9180
  收益率: 0.1758
  距离: 36.9104
  内存使用: 0.7525
  能量使用: 1.1011
  推理时间: 2.8237秒

批次 66:
  奖励值: 132.8151
  收益率: 0.1677
  距离: 32.0638
  内存使用: 0.6660
  能量使用: 1.0400
  推理时间: 2.6115秒

批次 67:
  奖励值: 131.3667
  收益率: 0.1648
  距离: 32.9280
  内存使用: 0.7127
  能量使用: 1.0258
  推理时间: 2.7392秒

批次 68:
  奖励值: 141.8951
  收益率: 0.1755
  距离: 31.9440
  内存使用: 0.7409
  能量使用: 1.1249
  推理时间: 3.2446秒

批次 69:
  奖励值: 147.3674
  收益率: 0.1870
  距离: 37.9142
  内存使用: 0.8308
  能量使用: 1.1203
  推理时间: 2.9487秒

批次 70:
  奖励值: 145.6114
  收益率: 0.1796
  距离: 35.2987
  内存使用: 0.7662
  能量使用: 1.1122
  推理时间: 2.9000秒

批次 71:
  奖励值: 144.2789
  收益率: 0.1817
  距离: 39.2632
  内存使用: 0.8120
  能量使用: 1.1669
  推理时间: 2.9741秒

批次 72:
  奖励值: 141.4105
  收益率: 0.1749
  距离: 34.7657
  内存使用: 0.7131
  能量使用: 1.1001
  推理时间: 2.8648秒

批次 73:
  奖励值: 151.1238
  收益率: 0.1909
  距离: 41.4212
  内存使用: 0.8777
  能量使用: 1.2535
  推理时间: 3.0748秒

批次 74:
  奖励值: 139.0657
  收益率: 0.1731
  距离: 34.9988
  内存使用: 0.7580
  能量使用: 1.1117
  推理时间: 2.8173秒

批次 75:
  奖励值: 119.5330
  收益率: 0.1512
  距离: 33.1300
  内存使用: 0.6571
  能量使用: 0.9470
  推理时间: 2.5221秒

批次 76:
  奖励值: 136.0758
  收益率: 0.1698
  距离: 32.0266
  内存使用: 0.7222
  能量使用: 1.0997
  推理时间: 2.8089秒

批次 77:
  奖励值: 151.0244
  收益率: 0.1886
  距离: 35.5657
  内存使用: 0.8295
  能量使用: 1.1559
  推理时间: 3.0642秒

批次 78:
  奖励值: 132.2407
  收益率: 0.1686
  距离: 33.0774
  内存使用: 0.6512
  能量使用: 0.9900
  推理时间: 2.7005秒

批次 79:
  奖励值: 139.6816
  收益率: 0.1776
  距离: 32.6398
  内存使用: 0.7069
  能量使用: 1.0901
  推理时间: 2.7152秒

批次 80:
  奖励值: 127.6613
  收益率: 0.1608
  距离: 33.0509
  内存使用: 0.6553
  能量使用: 0.9712
  推理时间: 2.6234秒

批次 81:
  奖励值: 150.3674
  收益率: 0.1886
  距离: 36.2895
  内存使用: 0.8178
  能量使用: 1.1578
  推理时间: 3.3586秒

批次 82:
  奖励值: 145.0178
  收益率: 0.1807
  距离: 33.4672
  内存使用: 0.7547
  能量使用: 1.1424
  推理时间: 2.9877秒

批次 83:
  奖励值: 126.0105
  收益率: 0.1586
  距离: 33.3583
  内存使用: 0.6393
  能量使用: 0.9814
  推理时间: 2.6137秒

批次 84:
  奖励值: 132.8365
  收益率: 0.1684
  距离: 35.6233
  内存使用: 0.7044
  能量使用: 0.9993
  推理时间: 2.7428秒

批次 85:
  奖励值: 149.7642
  收益率: 0.1898
  距离: 39.3068
  内存使用: 0.8404
  能量使用: 1.1245
  推理时间: 2.9232秒

批次 86:
  奖励值: 125.1165
  收益率: 0.1555
  距离: 32.5233
  内存使用: 0.6115
  能量使用: 1.0323
  推理时间: 2.5803秒

批次 87:
  奖励值: 149.5371
  收益率: 0.1888
  距离: 37.1127
  内存使用: 0.8873
  能量使用: 1.1257
  推理时间: 2.9786秒

批次 88:
  奖励值: 134.6434
  收益率: 0.1649
  距离: 32.3672
  内存使用: 0.6804
  能量使用: 1.0656
  推理时间: 2.6947秒

批次 89:
  奖励值: 151.5903
  收益率: 0.1873
  距离: 40.1741
  内存使用: 0.8496
  能量使用: 1.1959
  推理时间: 3.0016秒

批次 90:
  奖励值: 130.6750
  收益率: 0.1642
  距离: 33.9728
  内存使用: 0.6332
  能量使用: 1.0439
  推理时间: 2.5727秒

批次 91:
  奖励值: 141.3034
  收益率: 0.1765
  距离: 36.0366
  内存使用: 0.6288
  能量使用: 1.1277
  推理时间: 2.8373秒

批次 92:
  奖励值: 145.7232
  收益率: 0.1800
  距离: 37.5766
  内存使用: 0.8247
  能量使用: 1.1523
  推理时间: 2.9086秒

批次 93:
  奖励值: 120.8253
  收益率: 0.1499
  距离: 30.3306
  内存使用: 0.8941
  能量使用: 0.9247
  推理时间: 2.4006秒

批次 94:
  奖励值: 152.6454
  收益率: 0.1930
  距离: 36.7019
  内存使用: 0.8635
  能量使用: 1.1439
  推理时间: 3.0225秒

批次 95:
  奖励值: 132.4707
  收益率: 0.1651
  距离: 28.8020
  内存使用: 0.6449
  能量使用: 1.0117
  推理时间: 2.6704秒

批次 96:
  奖励值: 120.4737
  收益率: 0.1528
  距离: 31.1357
  内存使用: 0.8999
  能量使用: 1.0169
  推理时间: 2.4402秒

批次 97:
  奖励值: 142.3906
  收益率: 0.1811
  距离: 38.8360
  内存使用: 0.8101
  能量使用: 1.1726
  推理时间: 2.8008秒

批次 98:
  奖励值: 143.3454
  收益率: 0.1800
  距离: 38.0574
  内存使用: 0.7485
  能量使用: 1.1033
  推理时间: 2.9297秒

批次 99:
  奖励值: 141.0701
  收益率: 0.1795
  距离: 40.6952
  内存使用: 0.6619
  能量使用: 1.1026
  推理时间: 2.9190秒

批次 100:
  奖励值: 150.0773
  收益率: 0.1916
  距离: 41.8697
  内存使用: 0.8104
  能量使用: 1.1653
  推理时间: 3.1086秒


==================== 总结 ====================
平均收益率: 0.1739
平均能量使用: 1.0761
平均推理时间: 2.8139秒
