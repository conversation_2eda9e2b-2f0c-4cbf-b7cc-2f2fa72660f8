推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 63.9465
  收益率: 0.5103
  距离: 15.6398
  内存使用: 0.1669
  能量使用: 0.5004
  推理时间: 1.5269秒

批次 2:
  奖励值: 55.5545
  收益率: 0.4735
  距离: 17.0533
  内存使用: 0.1778
  能量使用: 0.4598
  推理时间: 1.3636秒

批次 3:
  奖励值: 54.9747
  收益率: 0.4519
  距离: 13.4951
  内存使用: 0.1320
  能量使用: 0.4236
  推理时间: 1.3093秒

批次 4:
  奖励值: 63.6033
  收益率: 0.5263
  距离: 16.2422
  内存使用: 0.2241
  能量使用: 0.5271
  推理时间: 1.6080秒

批次 5:
  奖励值: 64.9415
  收益率: 0.4969
  距离: 15.3145
  内存使用: 0.2602
  能量使用: 0.5423
  推理时间: 1.5940秒

批次 6:
  奖励值: 67.8336
  收益率: 0.5377
  距离: 14.7850
  内存使用: 0.1914
  能量使用: 0.5564
  推理时间: 1.5992秒

批次 7:
  奖励值: 69.2390
  收益率: 0.5720
  距离: 21.0420
  内存使用: 0.2618
  能量使用: 0.6181
  推理时间: 1.9517秒

批次 8:
  奖励值: 66.3582
  收益率: 0.5532
  距离: 16.3601
  内存使用: 0.2286
  能量使用: 0.4949
  推理时间: 1.7061秒

批次 9:
  奖励值: 65.2110
  收益率: 0.5261
  距离: 16.2873
  内存使用: 0.2012
  能量使用: 0.4895
  推理时间: 1.4775秒

批次 10:
  奖励值: 60.0330
  收益率: 0.5015
  距离: 13.9047
  内存使用: 0.2128
  能量使用: 0.4164
  推理时间: 1.4883秒

批次 11:
  奖励值: 61.8908
  收益率: 0.4989
  距离: 17.0955
  内存使用: 0.1683
  能量使用: 0.5546
  推理时间: 1.6881秒

批次 12:
  奖励值: 62.0524
  收益率: 0.5120
  距离: 15.4934
  内存使用: 0.2466
  能量使用: 0.5551
  推理时间: 1.5664秒

批次 13:
  奖励值: 57.4662
  收益率: 0.4936
  距离: 14.6255
  内存使用: 0.1588
  能量使用: 0.4833
  推理时间: 1.4142秒

批次 14:
  奖励值: 63.7717
  收益率: 0.5280
  距离: 16.7626
  内存使用: 0.2183
  能量使用: 0.4756
  推理时间: 1.5986秒

批次 15:
  奖励值: 61.0505
  收益率: 0.4922
  距离: 15.4136
  内存使用: 0.2348
  能量使用: 0.4507
  推理时间: 1.4205秒

批次 16:
  奖励值: 56.7331
  收益率: 0.4816
  距离: 17.1288
  内存使用: 0.1156
  能量使用: 0.4894
  推理时间: 1.3452秒

批次 17:
  奖励值: 63.5252
  收益率: 0.5289
  距离: 16.5568
  内存使用: 0.2531
  能量使用: 0.5398
  推理时间: 1.4934秒

批次 18:
  奖励值: 57.1083
  收益率: 0.4962
  距离: 16.2910
  内存使用: 0.1678
  能量使用: 0.5245
  推理时间: 1.5851秒

批次 19:
  奖励值: 62.5557
  收益率: 0.5159
  距离: 16.7522
  内存使用: 0.2179
  能量使用: 0.5186
  推理时间: 1.4868秒

批次 20:
  奖励值: 53.0483
  收益率: 0.4720
  距离: 16.4594
  内存使用: 0.1827
  能量使用: 0.4613
  推理时间: 1.5959秒

批次 21:
  奖励值: 63.5840
  收益率: 0.5352
  距离: 19.6275
  内存使用: 0.2265
  能量使用: 0.5780
  推理时间: 1.5349秒

批次 22:
  奖励值: 59.6482
  收益率: 0.5086
  距离: 16.8399
  内存使用: 0.1935
  能量使用: 0.4702
  推理时间: 1.5340秒

批次 23:
  奖励值: 62.9169
  收益率: 0.5312
  距离: 14.8491
  内存使用: 0.1909
  能量使用: 0.5262
  推理时间: 1.4369秒

批次 24:
  奖励值: 66.0424
  收益率: 0.5265
  距离: 18.1206
  内存使用: 0.2324
  能量使用: 0.5622
  推理时间: 1.5727秒

批次 25:
  奖励值: 54.9275
  收益率: 0.4758
  距离: 15.0377
  内存使用: 0.2101
  能量使用: 0.4973
  推理时间: 1.2882秒

批次 26:
  奖励值: 62.8243
  收益率: 0.5252
  距离: 17.3823
  内存使用: 0.2846
  能量使用: 0.4846
  推理时间: 1.4986秒

批次 27:
  奖励值: 66.5885
  收益率: 0.5307
  距离: 17.9785
  内存使用: 0.2719
  能量使用: 0.5726
  推理时间: 1.5455秒

批次 28:
  奖励值: 63.5869
  收益率: 0.5156
  距离: 16.7278
  内存使用: 0.5688
  能量使用: 0.5296
  推理时间: 1.5596秒

批次 29:
  奖励值: 74.5131
  收益率: 0.5569
  距离: 20.2566
  内存使用: 0.3283
  能量使用: 0.5889
  推理时间: 1.6960秒

批次 30:
  奖励值: 59.7400
  收益率: 0.4902
  距离: 15.4532
  内存使用: 0.2124
  能量使用: 0.4544
  推理时间: 1.2879秒

批次 31:
  奖励值: 60.9986
  收益率: 0.5091
  距离: 17.0497
  内存使用: 0.2201
  能量使用: 0.5331
  推理时间: 1.4768秒

批次 32:
  奖励值: 65.7716
  收益率: 0.5095
  距离: 13.0769
  内存使用: 0.2266
  能量使用: 0.5533
  推理时间: 1.5663秒

批次 33:
  奖励值: 60.6769
  收益率: 0.5159
  距离: 14.3966
  内存使用: 0.1895
  能量使用: 0.4600
  推理时间: 1.4556秒

批次 34:
  奖励值: 60.6336
  收益率: 0.5352
  距离: 16.1705
  内存使用: 0.2354
  能量使用: 0.5269
  推理时间: 1.4607秒

批次 35:
  奖励值: 64.5791
  收益率: 0.5389
  距离: 18.4229
  内存使用: 0.1972
  能量使用: 0.4218
  推理时间: 1.5663秒

批次 36:
  奖励值: 68.4552
  收益率: 0.5787
  距离: 20.9478
  内存使用: 0.3185
  能量使用: 0.6234
  推理时间: 1.6989秒

批次 37:
  奖励值: 62.1125
  收益率: 0.5192
  距离: 18.2426
  内存使用: 0.2504
  能量使用: 0.4821
  推理时间: 1.4322秒

批次 38:
  奖励值: 64.7392
  收益率: 0.5321
  距离: 17.3923
  内存使用: 0.2266
  能量使用: 0.5026
  推理时间: 1.5187秒

批次 39:
  奖励值: 60.8760
  收益率: 0.5349
  距离: 17.1116
  内存使用: 0.2357
  能量使用: 0.5006
  推理时间: 1.5261秒

批次 40:
  奖励值: 68.2897
  收益率: 0.5438
  距离: 16.7864
  内存使用: 0.1820
  能量使用: 0.4523
  推理时间: 1.5848秒

批次 41:
  奖励值: 59.9509
  收益率: 0.5155
  距离: 16.0382
  内存使用: 0.1462
  能量使用: 0.5138
  推理时间: 1.5067秒

批次 42:
  奖励值: 62.0783
  收益率: 0.5093
  距离: 16.9563
  内存使用: 0.2090
  能量使用: 0.4779
  推理时间: 1.4938秒

批次 43:
  奖励值: 59.5624
  收益率: 0.4939
  距离: 17.4428
  内存使用: 0.1855
  能量使用: 0.4470
  推理时间: 1.4732秒

批次 44:
  奖励值: 63.5601
  收益率: 0.5313
  距离: 14.9636
  内存使用: 0.1762
  能量使用: 0.5401
  推理时间: 1.4648秒

批次 45:
  奖励值: 60.7173
  收益率: 0.5149
  距离: 16.2493
  内存使用: 0.2234
  能量使用: 0.4927
  推理时间: 1.4690秒

批次 46:
  奖励值: 62.4496
  收益率: 0.5154
  距离: 17.8837
  内存使用: 0.2538
  能量使用: 0.5632
  推理时间: 1.6080秒

批次 47:
  奖励值: 64.1054
  收益率: 0.5159
  距离: 16.9760
  内存使用: 0.2789
  能量使用: 0.5342
  推理时间: 1.5034秒

批次 48:
  奖励值: 63.8748
  收益率: 0.5126
  距离: 14.1799
  内存使用: 0.1945
  能量使用: 0.4759
  推理时间: 1.4936秒

批次 49:
  奖励值: 62.6842
  收益率: 0.5308
  距离: 18.5165
  内存使用: 0.1890
  能量使用: 0.5261
  推理时间: 1.5418秒

批次 50:
  奖励值: 65.3312
  收益率: 0.5423
  距离: 16.0224
  内存使用: 0.2178
  能量使用: 0.4974
  推理时间: 1.4540秒

批次 51:
  奖励值: 58.9618
  收益率: 0.4909
  距离: 15.8160
  内存使用: 0.1746
  能量使用: 0.4857
  推理时间: 1.4435秒

批次 52:
  奖励值: 58.4076
  收益率: 0.4845
  距离: 13.9168
  内存使用: 0.2116
  能量使用: 0.4564
  推理时间: 1.3754秒

批次 53:
  奖励值: 66.9804
  收益率: 0.5530
  距离: 16.6347
  内存使用: 0.2869
  能量使用: 0.5177
  推理时间: 1.6026秒

批次 54:
  奖励值: 57.9395
  收益率: 0.5194
  距离: 19.9846
  内存使用: 0.2001
  能量使用: 0.5363
  推理时间: 1.4654秒

批次 55:
  奖励值: 57.6075
  收益率: 0.4941
  距离: 17.4289
  内存使用: 0.1320
  能量使用: 0.4242
  推理时间: 1.3367秒

批次 56:
  奖励值: 59.7360
  收益率: 0.5064
  距离: 14.2806
  内存使用: 0.1855
  能量使用: 0.4618
  推理时间: 1.4780秒

批次 57:
  奖励值: 65.3259
  收益率: 0.5114
  距离: 16.4134
  内存使用: 0.2369
  能量使用: 0.4980
  推理时间: 1.5533秒

批次 58:
  奖励值: 68.3520
  收益率: 0.5529
  距离: 18.3376
  内存使用: 0.2478
  能量使用: 0.5107
  推理时间: 1.7433秒

批次 59:
  奖励值: 66.2791
  收益率: 0.5494
  距离: 17.7589
  内存使用: 0.2818
  能量使用: 0.6011
  推理时间: 1.6013秒

批次 60:
  奖励值: 62.7502
  收益率: 0.5150
  距离: 14.7237
  内存使用: 0.2887
  能量使用: 0.4872
  推理时间: 1.4857秒

批次 61:
  奖励值: 59.2196
  收益率: 0.5141
  距离: 15.3957
  内存使用: 0.2066
  能量使用: 0.5584
  推理时间: 1.3964秒

批次 62:
  奖励值: 60.2703
  收益率: 0.5029
  距离: 15.1311
  内存使用: 0.2094
  能量使用: 0.5190
  推理时间: 1.4455秒

批次 63:
  奖励值: 58.9373
  收益率: 0.4989
  距离: 15.0737
  内存使用: 0.1796
  能量使用: 0.4722
  推理时间: 1.3393秒

批次 64:
  奖励值: 64.0928
  收益率: 0.5414
  距离: 18.8299
  内存使用: 0.2533
  能量使用: 0.5313
  推理时间: 1.5145秒

批次 65:
  奖励值: 62.1827
  收益率: 0.4954
  距离: 14.1626
  内存使用: 0.1935
  能量使用: 0.4785
  推理时间: 1.3766秒

批次 66:
  奖励值: 64.7343
  收益率: 0.5169
  距离: 16.9532
  内存使用: 0.2424
  能量使用: 0.5357
  推理时间: 1.5018秒

批次 67:
  奖励值: 63.1915
  收益率: 0.5508
  距离: 19.1475
  内存使用: 0.2949
  能量使用: 0.5745
  推理时间: 1.5419秒

批次 68:
  奖励值: 66.3659
  收益率: 0.5288
  距离: 17.0573
  内存使用: 0.2629
  能量使用: 0.5480
  推理时间: 1.5864秒

批次 69:
  奖励值: 69.1806
  收益率: 0.5603
  距离: 16.9782
  内存使用: 0.3112
  能量使用: 0.5494
  推理时间: 1.6328秒

批次 70:
  奖励值: 62.3323
  收益率: 0.5152
  距离: 13.6197
  内存使用: 0.2226
  能量使用: 0.4930
  推理时间: 1.4685秒

批次 71:
  奖励值: 60.4570
  收益率: 0.5110
  距离: 17.9643
  内存使用: 0.2052
  能量使用: 0.5165
  推理时间: 1.4092秒

批次 72:
  奖励值: 62.2369
  收益率: 0.5116
  距离: 16.0024
  内存使用: 0.2335
  能量使用: 0.4904
  推理时间: 1.4735秒

批次 73:
  奖励值: 63.7915
  收益率: 0.5238
  距离: 14.9465
  内存使用: 0.1759
  能量使用: 0.4948
  推理时间: 1.4476秒

批次 74:
  奖励值: 64.3818
  收益率: 0.5277
  距离: 15.2816
  内存使用: 0.1980
  能量使用: 0.5568
  推理时间: 1.5025秒

批次 75:
  奖励值: 56.7247
  收益率: 0.4958
  距离: 14.5552
  内存使用: 0.1658
  能量使用: 0.4295
  推理时间: 1.3142秒

批次 76:
  奖励值: 60.5057
  收益率: 0.4928
  距离: 15.3370
  内存使用: 0.1855
  能量使用: 0.4661
  推理时间: 1.4424秒

批次 77:
  奖励值: 61.6138
  收益率: 0.5058
  距离: 15.4688
  内存使用: 0.2061
  能量使用: 0.4974
  推理时间: 1.4502秒

批次 78:
  奖励值: 62.2096
  收益率: 0.5096
  距离: 17.3267
  内存使用: 0.5470
  能量使用: 0.5071
  推理时间: 1.5584秒

批次 79:
  奖励值: 62.7510
  收益率: 0.5339
  距离: 16.8771
  内存使用: 0.2573
  能量使用: 0.5636
  推理时间: 1.5300秒

批次 80:
  奖励值: 62.1139
  收益率: 0.4979
  距离: 15.7592
  内存使用: 0.5124
  能量使用: 0.5006
  推理时间: 1.5951秒

批次 81:
  奖励值: 58.2928
  收益率: 0.5046
  距离: 15.1480
  内存使用: 0.1526
  能量使用: 0.5137
  推理时间: 1.5366秒

批次 82:
  奖励值: 64.3039
  收益率: 0.5098
  距离: 16.2827
  内存使用: 0.2068
  能量使用: 0.5027
  推理时间: 1.4505秒

批次 83:
  奖励值: 63.8579
  收益率: 0.5035
  距离: 15.2101
  内存使用: 0.2202
  能量使用: 0.4539
  推理时间: 1.5397秒

批次 84:
  奖励值: 64.5947
  收益率: 0.5419
  距离: 17.6838
  内存使用: 0.2341
  能量使用: 0.4927
  推理时间: 1.5523秒

批次 85:
  奖励值: 59.8945
  收益率: 0.5037
  距离: 16.3640
  内存使用: 0.1762
  能量使用: 0.4709
  推理时间: 1.4279秒

批次 86:
  奖励值: 58.0541
  收益率: 0.4936
  距离: 15.2104
  内存使用: 0.1922
  能量使用: 0.4893
  推理时间: 1.3583秒

批次 87:
  奖励值: 57.0013
  收益率: 0.4672
  距离: 15.1747
  内存使用: 0.1937
  能量使用: 0.4291
  推理时间: 1.3298秒

批次 88:
  奖励值: 59.3722
  收益率: 0.4853
  距离: 15.7903
  内存使用: 0.2148
  能量使用: 0.4504
  推理时间: 1.3748秒

批次 89:
  奖励值: 59.1820
  收益率: 0.4859
  距离: 15.4417
  内存使用: 0.1486
  能量使用: 0.4458
  推理时间: 1.3378秒

批次 90:
  奖励值: 65.7321
  收益率: 0.5324
  距离: 18.7707
  内存使用: 0.2436
  能量使用: 0.5214
  推理时间: 1.4814秒

批次 91:
  奖励值: 59.4092
  收益率: 0.4887
  距离: 14.5787
  内存使用: 0.1496
  能量使用: 0.4532
  推理时间: 1.3252秒

批次 92:
  奖励值: 58.4185
  收益率: 0.5015
  距离: 18.2672
  内存使用: 0.1956
  能量使用: 0.5003
  推理时间: 1.3811秒

批次 93:
  奖励值: 61.9550
  收益率: 0.5088
  距离: 14.2496
  内存使用: 0.1988
  能量使用: 0.4416
  推理时间: 1.3612秒

批次 94:
  奖励值: 62.8809
  收益率: 0.5228
  距离: 17.7238
  内存使用: 0.2093
  能量使用: 0.5052
  推理时间: 1.4865秒

批次 95:
  奖励值: 50.5841
  收益率: 0.4492
  距离: 14.7511
  内存使用: 0.1481
  能量使用: 0.5327
  推理时间: 1.3243秒

批次 96:
  奖励值: 66.5469
  收益率: 0.5412
  距离: 17.3495
  内存使用: 0.2487
  能量使用: 0.5081
  推理时间: 1.5118秒

批次 97:
  奖励值: 58.9174
  收益率: 0.4862
  距离: 14.9320
  内存使用: 0.1515
  能量使用: 0.4818
  推理时间: 1.3486秒

批次 98:
  奖励值: 62.1968
  收益率: 0.5244
  距离: 18.8640
  内存使用: 0.2175
  能量使用: 0.5250
  推理时间: 1.4339秒

批次 99:
  奖励值: 63.9994
  收益率: 0.5414
  距离: 17.7336
  内存使用: 0.1728
  能量使用: 0.4947
  推理时间: 1.4755秒

批次 100:
  奖励值: 60.3218
  收益率: 0.4978
  距离: 14.2871
  内存使用: 0.1854
  能量使用: 0.4839
  推理时间: 1.3406秒


==================== 总结 ====================
平均收益率: 0.5147
平均能量使用: 0.5043
平均推理时间: 1.4906秒
