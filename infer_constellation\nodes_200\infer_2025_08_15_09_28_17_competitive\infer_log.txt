推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 40.6105
  收益率: 0.5134
  距离: 10.2273
  内存使用: 0.0552
  能量使用: 0.2964
  推理时间: 0.9693秒

批次 2:
  奖励值: 49.4566
  收益率: 0.6149
  距离: 14.5729
  内存使用: 0.1200
  能量使用: 0.3939
  推理时间: 1.2448秒

批次 3:
  奖励值: 44.0346
  收益率: 0.5653
  距离: 11.9313
  内存使用: 0.0664
  能量使用: 0.3332
  推理时间: 1.0568秒

批次 4:
  奖励值: 45.3403
  收益率: 0.5560
  距离: 14.0477
  内存使用: 0.1003
  能量使用: 0.3714
  推理时间: 1.1343秒

批次 5:
  奖励值: 45.7826
  收益率: 0.5857
  距离: 12.6115
  内存使用: 0.0840
  能量使用: 0.3394
  推理时间: 1.1165秒

批次 6:
  奖励值: 56.0548
  收益率: 0.6653
  距离: 15.6912
  内存使用: 0.1502
  能量使用: 0.4491
  推理时间: 1.3343秒

批次 7:
  奖励值: 54.0675
  收益率: 0.6148
  距离: 12.9402
  内存使用: 0.1036
  能量使用: 0.4212
  推理时间: 1.2174秒

批次 8:
  奖励值: 49.5135
  收益率: 0.6030
  距离: 13.4507
  内存使用: 0.1041
  能量使用: 0.4181
  推理时间: 1.2459秒

批次 9:
  奖励值: 46.5265
  收益率: 0.5689
  距离: 11.2763
  内存使用: 0.1000
  能量使用: 0.3706
  推理时间: 1.1368秒

批次 10:
  奖励值: 48.3752
  收益率: 0.5763
  距离: 14.5567
  内存使用: 0.1423
  能量使用: 0.3506
  推理时间: 1.1928秒

批次 11:
  奖励值: 45.7729
  收益率: 0.5666
  距离: 12.6578
  内存使用: 0.0517
  能量使用: 0.3447
  推理时间: 1.0819秒

批次 12:
  奖励值: 48.5192
  收益率: 0.5996
  距离: 12.0235
  内存使用: 0.1338
  能量使用: 0.4043
  推理时间: 1.2376秒

批次 13:
  奖励值: 47.3937
  收益率: 0.5991
  距离: 14.0906
  内存使用: 0.0968
  能量使用: 0.4252
  推理时间: 1.2103秒

批次 14:
  奖励值: 51.1409
  收益率: 0.5918
  距离: 12.6393
  内存使用: 0.0837
  能量使用: 0.3951
  推理时间: 1.0224秒

批次 15:
  奖励值: 45.8687
  收益率: 0.5710
  距离: 13.1916
  内存使用: 0.1076
  能量使用: 0.3633
  推理时间: 1.0806秒

批次 16:
  奖励值: 50.5554
  收益率: 0.6162
  距离: 13.9358
  内存使用: 0.1331
  能量使用: 0.4203
  推理时间: 1.2966秒

批次 17:
  奖励值: 53.1374
  收益率: 0.6129
  距离: 10.9758
  内存使用: 0.1413
  能量使用: 0.4081
  推理时间: 1.2400秒

批次 18:
  奖励值: 48.2947
  收益率: 0.5650
  距离: 11.4812
  内存使用: 0.0975
  能量使用: 0.4319
  推理时间: 1.1670秒

批次 19:
  奖励值: 43.2095
  收益率: 0.5398
  距离: 10.7467
  内存使用: 0.0475
  能量使用: 0.3869
  推理时间: 1.0742秒

批次 20:
  奖励值: 49.5978
  收益率: 0.6065
  距离: 13.1054
  内存使用: 0.0687
  能量使用: 0.4304
  推理时间: 1.1904秒

批次 21:
  奖励值: 52.6669
  收益率: 0.6095
  距离: 12.1611
  内存使用: 0.1279
  能量使用: 0.3772
  推理时间: 1.1768秒

批次 22:
  奖励值: 44.1149
  收益率: 0.5356
  距离: 10.6077
  内存使用: 0.0824
  能量使用: 0.2801
  推理时间: 0.9084秒

批次 23:
  奖励值: 47.7297
  收益率: 0.5888
  距离: 13.4605
  内存使用: 0.0854
  能量使用: 0.3946
  推理时间: 1.0478秒

批次 24:
  奖励值: 44.8851
  收益率: 0.5553
  距离: 14.2122
  内存使用: 0.0743
  能量使用: 0.3433
  推理时间: 1.0746秒

批次 25:
  奖励值: 44.2864
  收益率: 0.5546
  距离: 11.5576
  内存使用: 0.0895
  能量使用: 0.3800
  推理时间: 0.9495秒

批次 26:
  奖励值: 47.5081
  收益率: 0.5912
  距离: 14.6346
  内存使用: 0.1135
  能量使用: 0.4357
  推理时间: 1.1035秒

批次 27:
  奖励值: 48.7715
  收益率: 0.6072
  距离: 13.5258
  内存使用: 0.1271
  能量使用: 0.4041
  推理时间: 1.1493秒

批次 28:
  奖励值: 44.2374
  收益率: 0.5802
  距离: 14.4326
  内存使用: 0.0650
  能量使用: 0.4276
  推理时间: 1.0665秒

批次 29:
  奖励值: 47.8099
  收益率: 0.5642
  距离: 14.7395
  内存使用: 0.0676
  能量使用: 0.3731
  推理时间: 1.2144秒

批次 30:
  奖励值: 48.3625
  收益率: 0.6044
  距离: 11.6211
  内存使用: 0.0963
  能量使用: 0.3576
  推理时间: 1.1751秒

批次 31:
  奖励值: 46.5378
  收益率: 0.5815
  距离: 12.0457
  内存使用: 0.0679
  能量使用: 0.4108
  推理时间: 1.0586秒

批次 32:
  奖励值: 47.8538
  收益率: 0.6124
  距离: 14.3878
  内存使用: 0.0959
  能量使用: 0.4089
  推理时间: 1.1813秒

批次 33:
  奖励值: 47.5989
  收益率: 0.5844
  距离: 10.9725
  内存使用: 0.0481
  能量使用: 0.3816
  推理时间: 1.1168秒

批次 34:
  奖励值: 50.2835
  收益率: 0.6291
  距离: 13.9422
  内存使用: 0.1202
  能量使用: 0.3706
  推理时间: 1.2293秒

批次 35:
  奖励值: 46.6295
  收益率: 0.5749
  距离: 10.6476
  内存使用: 0.0598
  能量使用: 0.4336
  推理时间: 1.2171秒

批次 36:
  奖励值: 48.3112
  收益率: 0.6233
  距离: 13.0118
  内存使用: 0.0973
  能量使用: 0.4686
  推理时间: 1.1972秒

批次 37:
  奖励值: 49.8347
  收益率: 0.6260
  距离: 13.6363
  内存使用: 0.0482
  能量使用: 0.4460
  推理时间: 1.2414秒

批次 38:
  奖励值: 43.8517
  收益率: 0.5421
  距离: 11.0850
  内存使用: 0.0355
  能量使用: 0.3762
  推理时间: 1.0576秒

批次 39:
  奖励值: 44.5850
  收益率: 0.5673
  距离: 13.1609
  内存使用: 0.0880
  能量使用: 0.3811
  推理时间: 1.1023秒

批次 40:
  奖励值: 44.1404
  收益率: 0.5531
  距离: 10.2668
  内存使用: 0.0647
  能量使用: 0.3878
  推理时间: 1.1573秒

批次 41:
  奖励值: 47.5319
  收益率: 0.6073
  距离: 11.5181
  内存使用: 0.0187
  能量使用: 0.3994
  推理时间: 1.1799秒

批次 42:
  奖励值: 47.3137
  收益率: 0.5751
  距离: 11.2956
  内存使用: 0.1058
  能量使用: 0.3713
  推理时间: 1.1311秒

批次 43:
  奖励值: 48.8358
  收益率: 0.5991
  距离: 12.4486
  内存使用: 0.0990
  能量使用: 0.3808
  推理时间: 1.1397秒

批次 44:
  奖励值: 45.8904
  收益率: 0.5829
  距离: 13.9994
  内存使用: 0.0621
  能量使用: 0.3577
  推理时间: 1.1337秒

批次 45:
  奖励值: 43.4805
  收益率: 0.5378
  距离: 10.8501
  内存使用: 0.0554
  能量使用: 0.3335
  推理时间: 1.0202秒

批次 46:
  奖励值: 47.6408
  收益率: 0.5850
  距离: 13.6947
  内存使用: 0.0435
  能量使用: 0.3802
  推理时间: 1.1462秒

批次 47:
  奖励值: 41.3496
  收益率: 0.5283
  距离: 12.1424
  内存使用: 0.0043
  能量使用: 0.3541
  推理时间: 0.9585秒

批次 48:
  奖励值: 44.8626
  收益率: 0.5614
  距离: 9.4336
  内存使用: 0.0391
  能量使用: 0.4022
  推理时间: 1.0416秒

批次 49:
  奖励值: 43.0182
  收益率: 0.5412
  距离: 10.7447
  内存使用: 0.0204
  能量使用: 0.3985
  推理时间: 1.0112秒

批次 50:
  奖励值: 45.3126
  收益率: 0.5661
  距离: 12.5228
  内存使用: 0.0711
  能量使用: 0.3809
  推理时间: 1.0937秒

批次 51:
  奖励值: 46.9283
  收益率: 0.5731
  距离: 12.2572
  内存使用: 0.0923
  能量使用: 0.3939
  推理时间: 1.0875秒

批次 52:
  奖励值: 50.5509
  收益率: 0.6138
  距离: 13.7278
  内存使用: 0.1548
  能量使用: 0.4155
  推理时间: 1.1483秒

批次 53:
  奖励值: 58.6550
  收益率: 0.6577
  距离: 14.2084
  内存使用: 0.1893
  能量使用: 0.4923
  推理时间: 1.3044秒

批次 54:
  奖励值: 42.1933
  收益率: 0.5335
  距离: 12.3715
  内存使用: 0.0640
  能量使用: 0.3779
  推理时间: 1.0214秒

批次 55:
  奖励值: 43.1438
  收益率: 0.5385
  距离: 9.7092
  内存使用: 0.0794
  能量使用: 0.3238
  推理时间: 1.0132秒

批次 56:
  奖励值: 51.1877
  收益率: 0.6031
  距离: 14.1243
  内存使用: 0.0957
  能量使用: 0.4046
  推理时间: 1.1878秒

批次 57:
  奖励值: 44.6096
  收益率: 0.5685
  距离: 14.1213
  内存使用: 0.0854
  能量使用: 0.3742
  推理时间: 1.1081秒

批次 58:
  奖励值: 46.3302
  收益率: 0.5801
  距离: 14.0525
  内存使用: 0.0541
  能量使用: 0.4330
  推理时间: 1.0872秒

批次 59:
  奖励值: 48.4088
  收益率: 0.5991
  距离: 11.5500
  内存使用: 0.0478
  能量使用: 0.3847
  推理时间: 1.1385秒

批次 60:
  奖励值: 45.1514
  收益率: 0.5739
  距离: 14.9142
  内存使用: 0.0414
  能量使用: 0.3416
  推理时间: 1.0703秒

批次 61:
  奖励值: 47.7963
  收益率: 0.5927
  距离: 13.8821
  内存使用: 0.1176
  能量使用: 0.4076
  推理时间: 1.1528秒

批次 62:
  奖励值: 40.5639
  收益率: 0.4941
  距离: 9.9508
  内存使用: 0.0107
  能量使用: 0.3107
  推理时间: 0.9224秒

批次 63:
  奖励值: 40.0626
  收益率: 0.5260
  距离: 10.7107
  内存使用: 0.0166
  能量使用: 0.3474
  推理时间: 0.9719秒

批次 64:
  奖励值: 46.2012
  收益率: 0.5772
  距离: 13.2733
  内存使用: 0.1342
  能量使用: 0.3901
  推理时间: 1.1510秒

批次 65:
  奖励值: 47.0470
  收益率: 0.5755
  距离: 15.2650
  内存使用: 0.0844
  能量使用: 0.3565
  推理时间: 1.2058秒

批次 66:
  奖励值: 46.6387
  收益率: 0.5739
  距离: 12.6781
  内存使用: 0.0925
  能量使用: 0.3520
  推理时间: 1.0690秒

批次 67:
  奖励值: 46.4945
  收益率: 0.5880
  距离: 14.1899
  内存使用: 0.0754
  能量使用: 0.3457
  推理时间: 1.0952秒

批次 68:
  奖励值: 44.2531
  收益率: 0.5653
  距离: 14.0226
  内存使用: 0.0746
  能量使用: 0.3967
  推理时间: 1.0762秒

批次 69:
  奖励值: 48.2943
  收益率: 0.5976
  距离: 13.0982
  内存使用: 0.0917
  能量使用: 0.4216
  推理时间: 1.7883秒

批次 70:
  奖励值: 49.5866
  收益率: 0.6204
  距离: 14.6585
  内存使用: 0.1384
  能量使用: 0.4300
  推理时间: 1.2534秒

批次 71:
  奖励值: 45.5015
  收益率: 0.5660
  距离: 12.3923
  内存使用: 0.0300
  能量使用: 0.3747
  推理时间: 1.0339秒

批次 72:
  奖励值: 42.0218
  收益率: 0.5513
  距离: 12.3239
  内存使用: 0.0646
  能量使用: 0.3678
  推理时间: 1.0458秒

批次 73:
  奖励值: 46.5138
  收益率: 0.5750
  距离: 12.3014
  内存使用: 0.0736
  能量使用: 0.3974
  推理时间: 1.1812秒

批次 74:
  奖励值: 46.8604
  收益率: 0.5763
  距离: 11.5870
  内存使用: 0.0913
  能量使用: 0.4009
  推理时间: 1.1656秒

批次 75:
  奖励值: 43.7315
  收益率: 0.5380
  距离: 12.6579
  内存使用: 0.1061
  能量使用: 0.3730
  推理时间: 1.1978秒

批次 76:
  奖励值: 48.0436
  收益率: 0.5852
  距离: 10.6695
  内存使用: 0.0623
  能量使用: 0.3811
  推理时间: 1.2031秒

批次 77:
  奖励值: 44.9938
  收益率: 0.5755
  距离: 13.9421
  内存使用: 0.0745
  能量使用: 0.3957
  推理时间: 1.2666秒

批次 78:
  奖励值: 46.7647
  收益率: 0.5942
  距离: 12.0010
  内存使用: 0.0711
  能量使用: 0.3985
  推理时间: 1.1185秒

批次 79:
  奖励值: 41.9427
  收益率: 0.5318
  距离: 11.0604
  内存使用: 0.0881
  能量使用: 0.3662
  推理时间: 1.0812秒

批次 80:
  奖励值: 50.3463
  收益率: 0.5954
  距离: 13.5508
  内存使用: 0.1274
  能量使用: 0.4291
  推理时间: 1.2106秒

批次 81:
  奖励值: 40.1121
  收益率: 0.5182
  距离: 10.1596
  内存使用: 0.3131
  能量使用: 0.3031
  推理时间: 0.9907秒

批次 82:
  奖励值: 47.0125
  收益率: 0.5782
  距离: 13.5013
  内存使用: 0.1124
  能量使用: 0.3613
  推理时间: 1.2113秒

批次 83:
  奖励值: 50.9303
  收益率: 0.6021
  距离: 10.8090
  内存使用: 0.0970
  能量使用: 0.4333
  推理时间: 1.2186秒

批次 84:
  奖励值: 45.0149
  收益率: 0.5644
  距离: 11.1497
  内存使用: 0.0494
  能量使用: 0.3711
  推理时间: 1.2041秒

批次 85:
  奖励值: 44.3515
  收益率: 0.5640
  距离: 10.9362
  内存使用: 0.3667
  能量使用: 0.3941
  推理时间: 1.1033秒

批次 86:
  奖励值: 39.8987
  收益率: 0.5524
  距离: 12.5622
  内存使用: 0.1086
  能量使用: 0.3317
  推理时间: 1.0275秒

批次 87:
  奖励值: 49.5012
  收益率: 0.6032
  距离: 13.2565
  内存使用: 0.1315
  能量使用: 0.4097
  推理时间: 1.0745秒

批次 88:
  奖励值: 46.3221
  收益率: 0.5675
  距离: 12.8665
  内存使用: 0.0961
  能量使用: 0.3931
  推理时间: 1.1438秒

批次 89:
  奖励值: 49.2533
  收益率: 0.6054
  距离: 12.2819
  内存使用: 0.1207
  能量使用: 0.4908
  推理时间: 1.1835秒

批次 90:
  奖励值: 49.1257
  收益率: 0.5908
  距离: 13.8519
  内存使用: 0.0925
  能量使用: 0.4042
  推理时间: 1.1564秒

批次 91:
  奖励值: 47.4946
  收益率: 0.5899
  距离: 10.1424
  内存使用: 0.0803
  能量使用: 0.3882
  推理时间: 1.1278秒

批次 92:
  奖励值: 46.4693
  收益率: 0.5862
  距离: 13.4249
  内存使用: 0.1035
  能量使用: 0.3330
  推理时间: 1.1672秒

批次 93:
  奖励值: 43.1064
  收益率: 0.5562
  距离: 13.3263
  内存使用: 0.0709
  能量使用: 0.3899
  推理时间: 1.3461秒

批次 94:
  奖励值: 44.8791
  收益率: 0.5537
  距离: 11.8798
  内存使用: 0.0581
  能量使用: 0.3038
  推理时间: 1.0715秒

批次 95:
  奖励值: 49.7265
  收益率: 0.5922
  距离: 11.8361
  内存使用: 0.1006
  能量使用: 0.4103
  推理时间: 1.2467秒

批次 96:
  奖励值: 50.0312
  收益率: 0.6189
  距离: 13.3791
  内存使用: 0.1086
  能量使用: 0.4499
  推理时间: 1.0995秒

批次 97:
  奖励值: 47.2220
  收益率: 0.5686
  距离: 13.6585
  内存使用: 0.0950
  能量使用: 0.4024
  推理时间: 1.0863秒

批次 98:
  奖励值: 42.6320
  收益率: 0.5398
  距离: 10.8648
  内存使用: -0.0079
  能量使用: 0.2792
  推理时间: 0.9928秒

批次 99:
  奖励值: 53.2180
  收益率: 0.6194
  距离: 12.8231
  内存使用: 0.1186
  能量使用: 0.4222
  推理时间: 1.2088秒

批次 100:
  奖励值: 44.2413
  收益率: 0.5747
  距离: 12.6995
  内存使用: 0.0590
  能量使用: 0.3589
  推理时间: 1.1547秒


==================== 总结 ====================
平均收益率: 0.5789
平均能量使用: 0.3857
平均推理时间: 1.1370秒
