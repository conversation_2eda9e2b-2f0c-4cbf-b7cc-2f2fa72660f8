推理数据数量: 100
每个序列任务数量: 300
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 77.7791
  收益率: 0.6256
  距离: 20.3185
  内存使用: 0.3021
  能量使用: 0.6214
  推理时间: 1.7413秒

批次 2:
  奖励值: 62.7304
  收益率: 0.5316
  距离: 18.3147
  内存使用: 0.2577
  能量使用: 0.5668
  推理时间: 1.3919秒

批次 3:
  奖励值: 71.3111
  收益率: 0.6020
  距离: 21.6654
  内存使用: 0.2923
  能量使用: 0.6201
  推理时间: 1.7791秒

批次 4:
  奖励值: 74.4471
  收益率: 0.6175
  距离: 19.3453
  内存使用: 0.3230
  能量使用: 0.6376
  推理时间: 1.7373秒

批次 5:
  奖励值: 78.8986
  收益率: 0.6120
  距离: 20.9371
  内存使用: 0.3664
  能量使用: 0.6994
  推理时间: 1.8239秒

批次 6:
  奖励值: 80.8978
  收益率: 0.6522
  距离: 20.5911
  内存使用: 0.3385
  能量使用: 0.6747
  推理时间: 1.8577秒

批次 7:
  奖励值: 69.1454
  收益率: 0.5720
  距离: 21.2245
  内存使用: 0.2470
  能量使用: 0.6376
  推理时间: 1.6690秒

批次 8:
  奖励值: 70.5118
  收益率: 0.5886
  距离: 17.5769
  内存使用: 0.2896
  能量使用: 0.5099
  推理时间: 1.6804秒

批次 9:
  奖励值: 72.0199
  收益率: 0.5839
  距离: 18.7024
  内存使用: 0.2818
  能量使用: 0.5759
  推理时间: 1.6040秒

批次 10:
  奖励值: 68.0488
  收益率: 0.5820
  距离: 19.2827
  内存使用: 0.2910
  能量使用: 0.5286
  推理时间: 1.5755秒

批次 11:
  奖励值: 73.2302
  收益率: 0.5920
  距离: 20.5902
  内存使用: 0.2928
  能量使用: 0.6565
  推理时间: 1.6592秒

批次 12:
  奖励值: 75.4662
  收益率: 0.6251
  距离: 19.4564
  内存使用: 0.3398
  能量使用: 0.6714
  推理时间: 1.7424秒

批次 13:
  奖励值: 66.9066
  收益率: 0.5815
  距离: 18.7944
  内存使用: 0.2496
  能量使用: 0.5428
  推理时间: 1.4898秒

批次 14:
  奖励值: 74.7033
  收益率: 0.6271
  距离: 21.8598
  内存使用: 0.3610
  能量使用: 0.5727
  推理时间: 1.7729秒

批次 15:
  奖励值: 72.9064
  收益率: 0.5855
  距离: 17.6736
  内存使用: 0.3187
  能量使用: 0.5949
  推理时间: 1.5802秒

批次 16:
  奖励值: 63.8253
  收益率: 0.5404
  距离: 18.7601
  内存使用: 0.2094
  能量使用: 0.5799
  推理时间: 1.4296秒

批次 17:
  奖励值: 69.1517
  收益率: 0.5788
  距离: 18.8553
  内存使用: 0.2793
  能量使用: 0.5621
  推理时间: 1.4996秒

批次 18:
  奖励值: 66.3711
  收益率: 0.5832
  距离: 20.5938
  内存使用: 0.2802
  能量使用: 0.5837
  推理时间: 1.5639秒

批次 19:
  奖励值: 75.4553
  收益率: 0.6252
  距离: 20.8763
  内存使用: 0.3721
  能量使用: 0.6410
  推理时间: 1.6458秒

批次 20:
  奖励值: 61.0466
  收益率: 0.5364
  距离: 17.1830
  内存使用: 0.2040
  能量使用: 0.5199
  推理时间: 1.5226秒

批次 21:
  奖励值: 74.6751
  收益率: 0.6157
  距离: 19.4773
  内存使用: 0.3237
  能量使用: 0.6488
  推理时间: 1.7385秒

批次 22:
  奖励值: 64.4231
  收益率: 0.5487
  距离: 18.0248
  内存使用: 0.2164
  能量使用: 0.5127
  推理时间: 1.4795秒

批次 23:
  奖励值: 67.9162
  收益率: 0.5800
  距离: 17.6782
  内存使用: 0.2764
  能量使用: 0.6131
  推理时间: 1.6076秒

批次 24:
  奖励值: 72.7552
  收益率: 0.5816
  距离: 20.3628
  内存使用: 0.2952
  能量使用: 0.6315
  推理时间: 1.6822秒

批次 25:
  奖励值: 69.1381
  收益率: 0.6053
  距离: 20.5579
  内存使用: 0.3263
  能量使用: 0.6396
  推理时间: 1.7692秒

批次 26:
  奖励值: 71.4587
  收益率: 0.5851
  距离: 16.3399
  内存使用: 0.3701
  能量使用: 0.5728
  推理时间: 1.6420秒

批次 27:
  奖励值: 77.2688
  收益率: 0.6187
  距离: 21.6094
  内存使用: 0.3746
  能量使用: 0.6784
  推理时间: 1.8107秒

批次 28:
  奖励值: 65.1989
  收益率: 0.5249
  距离: 16.4666
  内存使用: 0.2386
  能量使用: 0.4971
  推理时间: 1.5145秒

批次 29:
  奖励值: 82.1507
  收益率: 0.6119
  距离: 21.6401
  内存使用: 0.3972
  能量使用: 0.6612
  推理时间: 1.8439秒

批次 30:
  奖励值: 73.7692
  收益率: 0.6023
  距离: 18.1570
  内存使用: 0.3311
  能量使用: 0.5734
  推理时间: 1.6317秒

批次 31:
  奖励值: 68.3789
  收益率: 0.5586
  距离: 15.8613
  内存使用: 0.2265
  能量使用: 0.5544
  推理时间: 1.7291秒

批次 32:
  奖励值: 74.1747
  收益率: 0.5886
  距离: 18.6631
  内存使用: 0.3271
  能量使用: 0.6605
  推理时间: 1.8144秒

批次 33:
  奖励值: 69.1647
  收益率: 0.5953
  距离: 18.2436
  内存使用: 0.2768
  能量使用: 0.5503
  推理时间: 1.7088秒

批次 34:
  奖励值: 65.8750
  收益率: 0.5833
  距离: 17.9631
  内存使用: 0.3015
  能量使用: 0.5854
  推理时间: 1.6205秒

批次 35:
  奖励值: 73.3260
  收益率: 0.6138
  距离: 21.3123
  内存使用: 0.3020
  能量使用: 0.5337
  推理时间: 1.7197秒

批次 36:
  奖励值: 75.0048
  收益率: 0.6283
  距离: 21.3475
  内存使用: 0.3617
  能量使用: 0.6811
  推理时间: 1.8146秒

批次 37:
  奖励值: 69.5477
  收益率: 0.5801
  距离: 19.9708
  内存使用: 0.3598
  能量使用: 0.5739
  推理时间: 1.6335秒

批次 38:
  奖励值: 70.1551
  收益率: 0.5854
  距离: 21.1895
  内存使用: 0.2953
  能量使用: 0.6050
  推理时间: 1.7279秒

批次 39:
  奖励值: 68.8990
  收益率: 0.6038
  距离: 18.9047
  内存使用: 0.3226
  能量使用: 0.5747
  推理时间: 1.7041秒

批次 40:
  奖励值: 80.6518
  收益率: 0.6462
  距离: 20.8357
  内存使用: 0.2883
  能量使用: 0.5723
  推理时间: 1.8737秒

批次 41:
  奖励值: 69.7340
  收益率: 0.6045
  距离: 19.8514
  内存使用: 0.2618
  能量使用: 0.6188
  推理时间: 1.7059秒

批次 42:
  奖励值: 68.5430
  收益率: 0.5603
  距离: 18.1059
  内存使用: 0.2740
  能量使用: 0.5342
  推理时间: 1.5861秒

批次 43:
  奖励值: 71.2682
  收益率: 0.5860
  距离: 19.4418
  内存使用: 0.2715
  能量使用: 0.5502
  推理时间: 1.6920秒

批次 44:
  奖励值: 68.2064
  收益率: 0.5811
  距离: 18.9489
  内存使用: 0.2685
  能量使用: 0.5698
  推理时间: 1.5418秒

批次 45:
  奖励值: 72.7361
  收益率: 0.6057
  距离: 16.4281
  内存使用: 0.3130
  能量使用: 0.5868
  推理时间: 1.7247秒

批次 46:
  奖励值: 75.1606
  收益率: 0.6202
  距离: 21.4082
  内存使用: 0.3560
  能量使用: 0.7146
  推理时间: 1.8872秒

批次 47:
  奖励值: 74.1500
  收益率: 0.5986
  距离: 20.0892
  内存使用: 0.3496
  能量使用: 0.6612
  推理时间: 1.7661秒

批次 48:
  奖励值: 76.2738
  收益率: 0.6211
  距离: 19.2950
  内存使用: 0.3406
  能量使用: 0.6168
  推理时间: 1.7737秒

批次 49:
  奖励值: 71.3522
  收益率: 0.6050
  距离: 21.1545
  内存使用: 0.2952
  能量使用: 0.6458
  推理时间: 1.7377秒

批次 50:
  奖励值: 64.5392
  收益率: 0.5401
  距离: 16.9959
  内存使用: 0.2460
  能量使用: 0.4798
  推理时间: 1.4759秒

批次 51:
  奖励值: 63.8494
  收益率: 0.5267
  距离: 15.7612
  内存使用: 0.2211
  能量使用: 0.5190
  推理时间: 1.4825秒

批次 52:
  奖励值: 71.4682
  收益率: 0.5973
  距离: 18.1784
  内存使用: 0.3223
  能量使用: 0.5630
  推理时间: 1.6975秒

批次 53:
  奖励值: 76.6162
  收益率: 0.6399
  距离: 20.9641
  内存使用: 0.3697
  能量使用: 0.6337
  推理时间: 1.8468秒

批次 54:
  奖励值: 57.8161
  收益率: 0.5110
  距离: 18.0437
  内存使用: 0.1993
  能量使用: 0.5248
  推理时间: 1.4460秒

批次 55:
  奖励值: 59.9055
  收益率: 0.5148
  距离: 18.3111
  内存使用: 0.1916
  能量使用: 0.4862
  推理时间: 1.4989秒

批次 56:
  奖励值: 70.8631
  收益率: 0.6089
  距离: 19.0184
  内存使用: 0.3054
  能量使用: 0.5500
  推理时间: 1.7281秒

批次 57:
  奖励值: 75.5626
  收益率: 0.5965
  距离: 20.2731
  内存使用: 0.3902
  能量使用: 0.6115
  推理时间: 1.8100秒

批次 58:
  奖励值: 72.6291
  收益率: 0.5770
  距离: 16.4654
  内存使用: 0.3017
  能量使用: 0.5749
  推理时间: 1.7232秒

批次 59:
  奖励值: 71.2907
  收益率: 0.5967
  距离: 20.6044
  内存使用: 0.3626
  能量使用: 0.6515
  推理时间: 1.7461秒

批次 60:
  奖励值: 71.6424
  收益率: 0.5909
  距离: 17.5842
  内存使用: 0.3431
  能量使用: 0.5878
  推理时间: 1.7589秒

批次 61:
  奖励值: 63.3398
  收益率: 0.5523
  距离: 17.1310
  内存使用: 0.2182
  能量使用: 0.5712
  推理时间: 1.6596秒

批次 62:
  奖励值: 67.2301
  收益率: 0.5708
  距离: 19.4244
  内存使用: 0.3011
  能量使用: 0.6143
  推理时间: 1.6551秒

批次 63:
  奖励值: 74.5971
  收益率: 0.6355
  距离: 20.0365
  内存使用: 0.3241
  能量使用: 0.6453
  推理时间: 1.7496秒

批次 64:
  奖励值: 72.8059
  收益率: 0.6066
  距离: 18.9832
  内存使用: 0.3594
  能量使用: 0.6457
  推理时间: 1.7577秒

批次 65:
  奖励值: 71.4755
  收益率: 0.5829
  距离: 19.9675
  内存使用: 0.2943
  能量使用: 0.5871
  推理时间: 1.6893秒

批次 66:
  奖励值: 70.9288
  收益率: 0.5751
  距离: 21.0453
  内存使用: 0.3102
  能量使用: 0.5870
  推理时间: 1.7445秒

批次 67:
  奖励值: 65.1192
  收益率: 0.5681
  距离: 19.9112
  内存使用: 0.3070
  能量使用: 0.5435
  推理时间: 1.5936秒

批次 68:
  奖励值: 69.7644
  收益率: 0.5576
  距离: 18.3979
  内存使用: 0.3179
  能量使用: 0.5553
  推理时间: 1.6580秒

批次 69:
  奖励值: 73.4377
  收益率: 0.6026
  距离: 20.1555
  内存使用: 0.3571
  能量使用: 0.6119
  推理时间: 1.7337秒

批次 70:
  奖励值: 63.0259
  收益率: 0.5300
  距离: 16.2036
  内存使用: 0.2465
  能量使用: 0.4982
  推理时间: 1.4629秒

批次 71:
  奖励值: 71.8705
  收益率: 0.5966
  距离: 18.3040
  内存使用: 0.3257
  能量使用: 0.6293
  推理时间: 1.6892秒

批次 72:
  奖励值: 68.0937
  收益率: 0.5551
  距离: 16.1915
  内存使用: 0.2831
  能量使用: 0.5381
  推理时间: 1.5751秒

批次 73:
  奖励值: 65.7655
  收益率: 0.5479
  距离: 17.5303
  内存使用: 0.2109
  能量使用: 0.5279
  推理时间: 1.5480秒

批次 74:
  奖励值: 74.7947
  收益率: 0.6232
  距离: 20.4519
  内存使用: 0.3272
  能量使用: 0.6315
  推理时间: 1.7981秒

批次 75:
  奖励值: 66.9133
  收益率: 0.5952
  距离: 19.7532
  内存使用: 0.2677
  能量使用: 0.5525
  推理时间: 1.6656秒

批次 76:
  奖励值: 71.9259
  收益率: 0.5928
  距离: 20.0577
  内存使用: 0.3046
  能量使用: 0.5858
  推理时间: 1.7369秒

批次 77:
  奖励值: 75.0417
  收益率: 0.6161
  距离: 18.7394
  内存使用: 0.3473
  能量使用: 0.6299
  推理时间: 1.7766秒

批次 78:
  奖励值: 75.8389
  收益率: 0.6053
  距离: 16.9666
  内存使用: 0.3599
  能量使用: 0.5958
  推理时间: 1.7765秒

批次 79:
  奖励值: 68.3752
  收益率: 0.5854
  距离: 19.3996
  内存使用: 0.2902
  能量使用: 0.5597
  推理时间: 1.6715秒

批次 80:
  奖励值: 72.9783
  收益率: 0.5876
  距离: 19.5407
  内存使用: 0.3023
  能量使用: 0.6004
  推理时间: 1.7413秒

批次 81:
  奖励值: 60.8303
  收益率: 0.5213
  距离: 14.4629
  内存使用: 0.1643
  能量使用: 0.5121
  推理时间: 1.3969秒

批次 82:
  奖励值: 75.9894
  收益率: 0.6043
  距离: 19.6264
  内存使用: 0.3453
  能量使用: 0.6495
  推理时间: 1.7878秒

批次 83:
  奖励值: 80.2131
  收益率: 0.6367
  距离: 20.2553
  内存使用: 0.3362
  能量使用: 0.5823
  推理时间: 1.8848秒

批次 84:
  奖励值: 69.3167
  收益率: 0.5881
  距离: 20.7326
  内存使用: 0.3085
  能量使用: 0.5255
  推理时间: 1.6524秒

批次 85:
  奖励值: 71.0771
  收益率: 0.5972
  距离: 19.1327
  内存使用: 0.3145
  能量使用: 0.5987
  推理时间: 1.7638秒

批次 86:
  奖励值: 64.3373
  收益率: 0.5542
  距离: 18.7044
  内存使用: 0.2621
  能量使用: 0.5589
  推理时间: 1.5679秒

批次 87:
  奖励值: 73.4260
  收益率: 0.6033
  距离: 19.8274
  内存使用: 0.3476
  能量使用: 0.5730
  推理时间: 1.7233秒

批次 88:
  奖励值: 69.5452
  收益率: 0.5729
  距离: 19.7015
  内存使用: 0.2938
  能量使用: 0.5142
  推理时间: 1.6861秒

批次 89:
  奖励值: 67.5840
  收益率: 0.5589
  距离: 18.6282
  内存使用: 0.2590
  能量使用: 0.5448
  推理时间: 1.5651秒

批次 90:
  奖励值: 71.7282
  收益率: 0.5712
  距离: 17.6757
  内存使用: 0.3038
  能量使用: 0.5642
  推理时间: 1.5973秒

批次 91:
  奖励值: 68.5020
  收益率: 0.5704
  距离: 18.5892
  内存使用: 0.2711
  能量使用: 0.5357
  推理时间: 1.6286秒

批次 92:
  奖励值: 67.1704
  收益率: 0.5724
  距离: 19.8472
  内存使用: 0.2524
  能量使用: 0.5596
  推理时间: 1.6143秒

批次 93:
  奖励值: 68.1410
  收益率: 0.5704
  距离: 18.5284
  内存使用: 0.2801
  能量使用: 0.5096
  推理时间: 1.5843秒

批次 94:
  奖励值: 75.4285
  收益率: 0.6338
  距离: 22.9874
  内存使用: 0.3478
  能量使用: 0.6078
  推理时间: 1.8986秒

批次 95:
  奖励值: 64.9414
  收益率: 0.5639
  距离: 15.6096
  内存使用: 0.2496
  能量使用: 0.6579
  推理时间: 1.6166秒

批次 96:
  奖励值: 65.8126
  收益率: 0.5369
  距离: 17.6119
  内存使用: 0.2368
  能量使用: 0.5260
  推理时间: 1.5384秒

批次 97:
  奖励值: 65.4318
  收益率: 0.5510
  距离: 19.5785
  内存使用: 0.2186
  能量使用: 0.5394
  推理时间: 1.5266秒

批次 98:
  奖励值: 70.4106
  收益率: 0.5878
  距离: 19.7109
  内存使用: 0.3064
  能量使用: 0.5616
  推理时间: 1.7231秒

批次 99:
  奖励值: 68.4305
  收益率: 0.5762
  距离: 18.1775
  内存使用: 0.2310
  能量使用: 0.5306
  推理时间: 1.7381秒

批次 100:
  奖励值: 65.3563
  收益率: 0.5389
  距离: 15.3825
  内存使用: 0.2055
  能量使用: 0.4994
  推理时间: 1.4814秒


==================== 总结 ====================
平均收益率: 0.5858
平均能量使用: 0.5846
平均推理时间: 1.6712秒
