推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 125.8182
  收益率: 0.3227
  距离: 29.7776
  内存使用: 0.7044
  能量使用: 1.0816
  推理时间: 2.7082秒

批次 2:
  奖励值: 125.7775
  收益率: 0.3140
  距离: 32.1993
  内存使用: 0.6887
  能量使用: 0.9570
  推理时间: 2.9742秒

批次 3:
  奖励值: 114.7647
  收益率: 0.2881
  距离: 32.6393
  内存使用: 0.8997
  能量使用: 0.9316
  推理时间: 2.6859秒

批次 4:
  奖励值: 135.0865
  收益率: 0.3388
  距离: 37.5464
  内存使用: 0.7749
  能量使用: 1.1057
  推理时间: 3.0393秒

批次 5:
  奖励值: 122.9841
  收益率: 0.3126
  距离: 33.7695
  内存使用: 0.6738
  能量使用: 0.9884
  推理时间: 2.8533秒

批次 6:
  奖励值: 126.8205
  收益率: 0.3187
  距离: 31.3267
  内存使用: 0.6528
  能量使用: 0.9795
  推理时间: 2.7566秒

批次 7:
  奖励值: 114.7799
  收益率: 0.2919
  距离: 27.9081
  内存使用: 0.6243
  能量使用: 0.9078
  推理时间: 2.4124秒

批次 8:
  奖励值: 125.8415
  收益率: 0.3186
  距离: 32.3660
  内存使用: 0.7628
  能量使用: 0.9883
  推理时间: 2.8509秒

批次 9:
  奖励值: 124.5991
  收益率: 0.3139
  距离: 32.0426
  内存使用: 0.6578
  能量使用: 0.9013
  推理时间: 2.5420秒

批次 10:
  奖励值: 127.6428
  收益率: 0.3198
  距离: 35.5091
  内存使用: 0.6308
  能量使用: 0.9745
  推理时间: 2.8105秒

批次 11:
  奖励值: 120.1553
  收益率: 0.2943
  距离: 30.5551
  内存使用: 0.6258
  能量使用: 0.9085
  推理时间: 2.6673秒

批次 12:
  奖励值: 140.6981
  收益率: 0.3565
  距离: 34.1967
  内存使用: 0.7334
  能量使用: 1.0736
  推理时间: 3.0804秒

批次 13:
  奖励值: 114.3009
  收益率: 0.2915
  距离: 32.2074
  内存使用: 0.6823
  能量使用: 0.9083
  推理时间: 2.4444秒

批次 14:
  奖励值: 128.5320
  收益率: 0.3268
  距离: 33.6820
  内存使用: 0.7008
  能量使用: 0.9531
  推理时间: 2.9017秒

批次 15:
  奖励值: 129.6363
  收益率: 0.3293
  距离: 32.7689
  内存使用: 0.7556
  能量使用: 1.0030
  推理时间: 2.6801秒

批次 16:
  奖励值: 121.3725
  收益率: 0.3096
  距离: 34.1226
  内存使用: 0.6481
  能量使用: 0.8842
  推理时间: 2.7283秒

批次 17:
  奖励值: 129.2888
  收益率: 0.3286
  距离: 35.1345
  内存使用: 0.6974
  能量使用: 0.9904
  推理时间: 2.9180秒

批次 18:
  奖励值: 130.1622
  收益率: 0.3323
  距离: 33.5318
  内存使用: 0.6858
  能量使用: 1.0580
  推理时间: 2.6798秒

批次 19:
  奖励值: 130.0060
  收益率: 0.3243
  距离: 30.9801
  内存使用: 0.7504
  能量使用: 1.0575
  推理时间: 2.9303秒

批次 20:
  奖励值: 137.2129
  收益率: 0.3386
  距离: 33.3924
  内存使用: 0.7118
  能量使用: 1.0698
  推理时间: 2.8625秒

批次 21:
  奖励值: 131.0913
  收益率: 0.3132
  距离: 32.1087
  内存使用: 0.6366
  能量使用: 1.0720
  推理时间: 2.8804秒

批次 22:
  奖励值: 134.2648
  收益率: 0.3352
  距离: 35.1059
  内存使用: 0.7088
  能量使用: 1.0558
  推理时间: 3.0520秒

批次 23:
  奖励值: 123.6708
  收益率: 0.3180
  距离: 34.9661
  内存使用: 0.6808
  能量使用: 1.0115
  推理时间: 2.6463秒

批次 24:
  奖励值: 131.8699
  收益率: 0.3292
  距离: 34.0535
  内存使用: 0.7749
  能量使用: 1.0317
  推理时间: 2.6970秒

批次 25:
  奖励值: 134.4740
  收益率: 0.3269
  距离: 35.1764
  内存使用: 0.6558
  能量使用: 1.0198
  推理时间: 2.9956秒

批次 26:
  奖励值: 124.6882
  收益率: 0.3182
  距离: 33.7007
  内存使用: 0.7108
  能量使用: 1.0122
  推理时间: 2.8068秒

批次 27:
  奖励值: 129.4037
  收益率: 0.3333
  距离: 34.7395
  内存使用: 0.6648
  能量使用: 0.9883
  推理时间: 2.8897秒

批次 28:
  奖励值: 119.0655
  收益率: 0.2985
  距离: 30.7308
  内存使用: 0.6060
  能量使用: 0.9321
  推理时间: 2.6635秒

批次 29:
  奖励值: 134.1431
  收益率: 0.3377
  距离: 35.6167
  内存使用: 0.7491
  能量使用: 1.1480
  推理时间: 3.0543秒

批次 30:
  奖励值: 120.6974
  收益率: 0.2943
  距离: 31.1391
  内存使用: 0.6748
  能量使用: 0.9913
  推理时间: 2.7320秒

批次 31:
  奖励值: 133.8517
  收益率: 0.3376
  距离: 33.5844
  内存使用: 0.6847
  能量使用: 1.0275
  推理时间: 2.8363秒

批次 32:
  奖励值: 132.7672
  收益率: 0.3340
  距离: 35.4758
  内存使用: 0.7488
  能量使用: 1.0410
  推理时间: 3.2037秒

批次 33:
  奖励值: 120.0907
  收益率: 0.3032
  距离: 33.5643
  内存使用: 0.8997
  能量使用: 0.9547
  推理时间: 2.8545秒

批次 34:
  奖励值: 137.2005
  收益率: 0.3412
  距离: 34.9465
  内存使用: 0.8109
  能量使用: 1.0416
  推理时间: 2.8689秒

批次 35:
  奖励值: 119.6399
  收益率: 0.3011
  距离: 30.8707
  内存使用: 0.6290
  能量使用: 1.0204
  推理时间: 2.5046秒

批次 36:
  奖励值: 129.4741
  收益率: 0.3259
  距离: 33.2427
  内存使用: 0.6013
  能量使用: 1.0077
  推理时间: 2.8722秒

批次 37:
  奖励值: 123.3580
  收益率: 0.3119
  距离: 31.7399
  内存使用: 0.7266
  能量使用: 1.0175
  推理时间: 2.9583秒

批次 38:
  奖励值: 119.2206
  收益率: 0.2989
  距离: 27.3889
  内存使用: 0.6640
  能量使用: 0.9059
  推理时间: 2.6089秒

批次 39:
  奖励值: 125.4064
  收益率: 0.3257
  距离: 33.0741
  内存使用: 0.6794
  能量使用: 1.0337
  推理时间: 2.8158秒

批次 40:
  奖励值: 127.2938
  收益率: 0.3172
  距离: 31.3739
  内存使用: 0.6272
  能量使用: 1.0112
  推理时间: 2.7118秒

批次 41:
  奖励值: 117.6916
  收益率: 0.2963
  距离: 30.4152
  内存使用: 0.6268
  能量使用: 0.9747
  推理时间: 2.6342秒

批次 42:
  奖励值: 132.5093
  收益率: 0.3301
  距离: 34.9893
  内存使用: 0.7495
  能量使用: 1.0427
  推理时间: 3.1754秒

批次 43:
  奖励值: 120.9188
  收益率: 0.3094
  距离: 30.1728
  内存使用: 0.6340
  能量使用: 0.9556
  推理时间: 2.8210秒

批次 44:
  奖励值: 119.3503
  收益率: 0.3049
  距离: 30.9361
  内存使用: 0.6163
  能量使用: 0.9470
  推理时间: 2.5583秒

批次 45:
  奖励值: 126.4428
  收益率: 0.3213
  距离: 34.7981
  内存使用: 0.6901
  能量使用: 1.0262
  推理时间: 2.9356秒

批次 46:
  奖励值: 132.0379
  收益率: 0.3418
  距离: 39.3129
  内存使用: 0.6527
  能量使用: 1.1584
  推理时间: 2.8541秒

批次 47:
  奖励值: 116.1369
  收益率: 0.2906
  距离: 29.7076
  内存使用: 0.6110
  能量使用: 1.0076
  推理时间: 3.3263秒

批次 48:
  奖励值: 131.5148
  收益率: 0.3352
  距离: 28.4804
  内存使用: 0.6869
  能量使用: 1.0031
  推理时间: 2.8419秒

批次 49:
  奖励值: 112.4675
  收益率: 0.2832
  距离: 30.4009
  内存使用: 0.6007
  能量使用: 0.8634
  推理时间: 2.5497秒

批次 50:
  奖励值: 127.8287
  收益率: 0.3136
  距离: 32.6510
  内存使用: 0.6658
  能量使用: 1.0257
  推理时间: 2.9782秒

批次 51:
  奖励值: 131.7759
  收益率: 0.3249
  距离: 32.5855
  内存使用: 0.7097
  能量使用: 0.9529
  推理时间: 3.0327秒

批次 52:
  奖励值: 133.6641
  收益率: 0.3303
  距离: 32.4076
  内存使用: 0.7428
  能量使用: 1.1213
  推理时间: 3.0341秒

批次 53:
  奖励值: 121.7854
  收益率: 0.3127
  距离: 29.1001
  内存使用: 0.6346
  能量使用: 0.8945
  推理时间: 2.7807秒

批次 54:
  奖励值: 130.7018
  收益率: 0.3266
  距离: 33.3573
  内存使用: 0.7449
  能量使用: 1.0941
  推理时间: 3.0631秒

批次 55:
  奖励值: 116.0595
  收益率: 0.2882
  距离: 27.2803
  内存使用: 0.8990
  能量使用: 0.9016
  推理时间: 2.8437秒

批次 56:
  奖励值: 127.0233
  收益率: 0.3178
  距离: 33.9353
  内存使用: 0.7585
  能量使用: 1.0596
  推理时间: 3.1764秒

批次 57:
  奖励值: 115.9312
  收益率: 0.3067
  距离: 34.2976
  内存使用: 0.6614
  能量使用: 0.9785
  推理时间: 2.6823秒

批次 58:
  奖励值: 122.3288
  收益率: 0.3052
  距离: 30.5668
  内存使用: 0.6066
  能量使用: 0.9689
  推理时间: 2.9328秒

批次 59:
  奖励值: 131.7858
  收益率: 0.3347
  距离: 35.1051
  内存使用: 0.7227
  能量使用: 1.0005
  推理时间: 2.9963秒

批次 60:
  奖励值: 115.9417
  收益率: 0.2954
  距离: 30.4651
  内存使用: 0.6099
  能量使用: 0.8416
  推理时间: 2.3674秒

批次 61:
  奖励值: 135.8778
  收益率: 0.3456
  距离: 37.0225
  内存使用: 0.7781
  能量使用: 1.0438
  推理时间: 3.5009秒

批次 62:
  奖励值: 129.4556
  收益率: 0.3266
  距离: 33.5169
  内存使用: 0.7580
  能量使用: 1.0139
  推理时间: 3.0419秒

批次 63:
  奖励值: 116.3333
  收益率: 0.2919
  距离: 34.1871
  内存使用: 0.6441
  能量使用: 0.9022
  推理时间: 2.5376秒

批次 64:
  奖励值: 123.1023
  收益率: 0.3159
  距离: 35.9396
  内存使用: 0.6251
  能量使用: 1.0307
  推理时间: 2.6614秒

批次 65:
  奖励值: 129.6436
  收益率: 0.3247
  距离: 33.8180
  内存使用: 0.6765
  能量使用: 1.0184
  推理时间: 2.8735秒

批次 66:
  奖励值: 132.7840
  收益率: 0.3350
  距离: 35.3992
  内存使用: 0.7917
  能量使用: 1.0410
  推理时间: 2.6934秒

批次 67:
  奖励值: 132.6975
  收益率: 0.3331
  距离: 33.3739
  内存使用: 0.7102
  能量使用: 1.1208
  推理时间: 2.9187秒

批次 68:
  奖励值: 130.6080
  收益率: 0.3281
  距离: 31.6737
  内存使用: 0.6652
  能量使用: 1.0451
  推理时间: 2.7104秒

批次 69:
  奖励值: 123.2265
  收益率: 0.3119
  距离: 33.9048
  内存使用: 0.6900
  能量使用: 0.9521
  推理时间: 2.9459秒

批次 70:
  奖励值: 125.4759
  收益率: 0.3133
  距离: 30.3252
  内存使用: 0.6990
  能量使用: 1.0239
  推理时间: 2.6552秒

批次 71:
  奖励值: 116.5244
  收益率: 0.2922
  距离: 31.3706
  内存使用: 0.6030
  能量使用: 0.9777
  推理时间: 2.5421秒

批次 72:
  奖励值: 141.4386
  收益率: 0.3615
  距离: 40.0366
  内存使用: 0.7466
  能量使用: 1.1395
  推理时间: 3.2090秒

批次 73:
  奖励值: 132.6732
  收益率: 0.3441
  距离: 36.0283
  内存使用: 0.7234
  能量使用: 1.1016
  推理时间: 3.0754秒

批次 74:
  奖励值: 127.9376
  收益率: 0.3084
  距离: 30.9172
  内存使用: 0.6734
  能量使用: 0.9341
  推理时间: 2.5602秒

批次 75:
  奖励值: 133.6752
  收益率: 0.3203
  距离: 33.5398
  内存使用: 0.6872
  能量使用: 1.0225
  推理时间: 2.7614秒

批次 76:
  奖励值: 126.5095
  收益率: 0.3218
  距离: 32.6076
  内存使用: 0.6431
  能量使用: 0.9304
  推理时间: 2.8459秒

批次 77:
  奖励值: 121.9730
  收益率: 0.2989
  距离: 32.0014
  内存使用: 0.6817
  能量使用: 0.9708
  推理时间: 2.6295秒

批次 78:
  奖励值: 120.8188
  收益率: 0.3055
  距离: 30.2933
  内存使用: 0.6771
  能量使用: 0.9918
  推理时间: 2.7199秒

批次 79:
  奖励值: 112.0677
  收益率: 0.2849
  距离: 32.2450
  内存使用: 0.8999
  能量使用: 0.9197
  推理时间: 2.6168秒

批次 80:
  奖励值: 113.5564
  收益率: 0.2845
  距离: 31.3676
  内存使用: 0.6016
  能量使用: 0.9179
  推理时间: 2.3453秒

批次 81:
  奖励值: 133.4422
  收益率: 0.3306
  距离: 32.9491
  内存使用: 0.7093
  能量使用: 1.0572
  推理时间: 2.9443秒

批次 82:
  奖励值: 121.6216
  收益率: 0.3093
  距离: 30.2605
  内存使用: 0.6750
  能量使用: 1.0213
  推理时间: 2.6930秒

批次 83:
  奖励值: 111.8228
  收益率: 0.2805
  距离: 27.6768
  内存使用: 0.6145
  能量使用: 0.8631
  推理时间: 2.4493秒

批次 84:
  奖励值: 128.5861
  收益率: 0.3287
  距离: 33.7039
  内存使用: 0.6800
  能量使用: 1.0437
  推理时间: 2.8562秒

批次 85:
  奖励值: 120.8442
  收益率: 0.3024
  距离: 30.1689
  内存使用: 0.6064
  能量使用: 0.9364
  推理时间: 2.8247秒

批次 86:
  奖励值: 131.6150
  收益率: 0.3435
  距离: 34.7941
  内存使用: 0.7098
  能量使用: 1.0662
  推理时间: 2.7660秒

批次 87:
  奖励值: 123.7907
  收益率: 0.3224
  距离: 32.2328
  内存使用: 0.6913
  能量使用: 1.0945
  推理时间: 2.6296秒

批次 88:
  奖励值: 124.9981
  收益率: 0.3170
  距离: 30.9234
  内存使用: 0.6827
  能量使用: 0.9977
  推理时间: 2.7701秒

批次 89:
  奖励值: 105.1306
  收益率: 0.2640
  距离: 28.3749
  内存使用: 0.5441
  能量使用: 0.8198
  推理时间: 2.3763秒

批次 90:
  奖励值: 129.4605
  收益率: 0.3269
  距离: 35.8262
  内存使用: 0.7346
  能量使用: 1.1182
  推理时间: 2.6006秒

批次 91:
  奖励值: 129.8807
  收益率: 0.3330
  距离: 36.4651
  内存使用: 0.7280
  能量使用: 1.0455
  推理时间: 2.6421秒

批次 92:
  奖励值: 135.9923
  收益率: 0.3441
  距离: 36.0523
  内存使用: 0.8155
  能量使用: 1.1476
  推理时间: 3.0535秒

批次 93:
  奖励值: 116.4136
  收益率: 0.2910
  距离: 32.9438
  内存使用: 0.7161
  能量使用: 0.9128
  推理时间: 2.5961秒

批次 94:
  奖励值: 120.7186
  收益率: 0.2995
  距离: 31.6694
  内存使用: 0.6835
  能量使用: 1.0098
  推理时间: 2.6828秒

批次 95:
  奖励值: 121.6238
  收益率: 0.3091
  距离: 36.8986
  内存使用: 0.6491
  能量使用: 1.0046
  推理时间: 2.7118秒

批次 96:
  奖励值: 126.1222
  收益率: 0.3156
  距离: 30.0821
  内存使用: 0.6124
  能量使用: 1.0612
  推理时间: 2.5296秒

批次 97:
  奖励值: 134.0856
  收益率: 0.3320
  距离: 31.8237
  内存使用: 0.6994
  能量使用: 1.1057
  推理时间: 2.9789秒

批次 98:
  奖励值: 131.2762
  收益率: 0.3379
  距离: 35.2687
  内存使用: 0.7073
  能量使用: 1.0716
  推理时间: 2.9255秒

批次 99:
  奖励值: 125.7976
  收益率: 0.3178
  距离: 35.0436
  内存使用: 0.7352
  能量使用: 1.0258
  推理时间: 2.8427秒

批次 100:
  奖励值: 126.8679
  收益率: 0.3227
  距离: 31.9848
  内存使用: 0.6572
  能量使用: 1.0221
  推理时间: 2.9064秒


==================== 总结 ====================
平均收益率: 0.3172
平均能量使用: 1.0039
平均推理时间: 2.8039秒
