推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 108.4280
  收益率: 0.3591
  距离: 26.2953
  内存使用: 0.4793
  能量使用: 0.7694
  推理时间: 2.0052秒

批次 2:
  奖励值: 106.3369
  收益率: 0.3518
  距离: 25.2225
  内存使用: 0.5225
  能量使用: 0.7812
  推理时间: 2.1121秒

批次 3:
  奖励值: 110.7839
  收益率: 0.3726
  距离: 27.5436
  内存使用: 0.6041
  能量使用: 0.8845
  推理时间: 3.7370秒

批次 4:
  奖励值: 112.1164
  收益率: 0.3768
  距离: 25.7143
  内存使用: 0.4726
  能量使用: 0.7804
  推理时间: 2.2646秒

批次 5:
  奖励值: 106.0922
  收益率: 0.3599
  距离: 24.2551
  内存使用: 0.5210
  能量使用: 0.8396
  推理时间: 2.1115秒

批次 6:
  奖励值: 108.6273
  收益率: 0.3725
  距离: 28.0655
  内存使用: 0.5539
  能量使用: 0.9260
  推理时间: 2.2453秒

批次 7:
  奖励值: 104.0115
  收益率: 0.3474
  距离: 25.8859
  内存使用: 0.4902
  能量使用: 0.8010
  推理时间: 2.0777秒

批次 8:
  奖励值: 110.7306
  收益率: 0.3651
  距离: 25.6032
  内存使用: 0.5339
  能量使用: 0.8017
  推理时间: 2.1510秒

批次 9:
  奖励值: 111.5470
  收益率: 0.3884
  距离: 26.7103
  内存使用: 0.4914
  能量使用: 0.9042
  推理时间: 2.2687秒

批次 10:
  奖励值: 109.6021
  收益率: 0.3542
  距离: 25.7513
  内存使用: 0.5807
  能量使用: 0.8637
  推理时间: 2.1568秒

批次 11:
  奖励值: 104.2802
  收益率: 0.3581
  距离: 24.7328
  内存使用: 0.5225
  能量使用: 0.7843
  推理时间: 2.1137秒

批次 12:
  奖励值: 110.3280
  收益率: 0.3627
  距离: 26.3397
  内存使用: 0.5580
  能量使用: 0.8463
  推理时间: 2.1776秒

批次 13:
  奖励值: 108.4202
  收益率: 0.3505
  距离: 24.6095
  内存使用: 0.5477
  能量使用: 0.8024
  推理时间: 2.0993秒

批次 14:
  奖励值: 108.8503
  收益率: 0.3747
  距离: 29.0757
  内存使用: 0.5409
  能量使用: 0.8827
  推理时间: 2.1885秒

批次 15:
  奖励值: 108.0748
  收益率: 0.3579
  距离: 25.0621
  内存使用: 0.4974
  能量使用: 0.8908
  推理时间: 2.1253秒

批次 16:
  奖励值: 101.8867
  收益率: 0.3321
  距离: 23.7986
  内存使用: 0.4695
  能量使用: 0.7585
  推理时间: 2.0610秒

批次 17:
  奖励值: 113.1266
  收益率: 0.3643
  距离: 28.7463
  内存使用: 0.5914
  能量使用: 0.8091
  推理时间: 2.3007秒

批次 18:
  奖励值: 105.2054
  收益率: 0.3568
  距离: 28.2474
  内存使用: 0.5083
  能量使用: 0.8335
  推理时间: 2.1424秒

批次 19:
  奖励值: 110.8399
  收益率: 0.3579
  距离: 26.5576
  内存使用: 0.5771
  能量使用: 0.7855
  推理时间: 2.2070秒

批次 20:
  奖励值: 113.0870
  收益率: 0.3773
  距离: 29.1195
  内存使用: 0.5882
  能量使用: 0.9181
  推理时间: 2.2582秒

批次 21:
  奖励值: 109.6069
  收益率: 0.3575
  距离: 29.6103
  内存使用: 0.5140
  能量使用: 0.8619
  推理时间: 2.2015秒

批次 22:
  奖励值: 107.5699
  收益率: 0.3607
  距离: 28.2912
  内存使用: 0.5452
  能量使用: 0.8238
  推理时间: 2.2223秒

批次 23:
  奖励值: 103.9760
  收益率: 0.3462
  距离: 26.7297
  内存使用: 0.5589
  能量使用: 0.7593
  推理时间: 2.1034秒

批次 24:
  奖励值: 109.7533
  收益率: 0.3706
  距离: 29.1122
  内存使用: 0.5573
  能量使用: 0.8239
  推理时间: 2.2619秒

批次 25:
  奖励值: 105.3976
  收益率: 0.3374
  距离: 23.8788
  内存使用: 0.5533
  能量使用: 0.7727
  推理时间: 2.1454秒

批次 26:
  奖励值: 105.6329
  收益率: 0.3525
  距离: 23.9161
  内存使用: 0.5163
  能量使用: 0.9019
  推理时间: 2.1133秒

批次 27:
  奖励值: 107.0896
  收益率: 0.3429
  距离: 24.8227
  内存使用: 0.4911
  能量使用: 0.9071
  推理时间: 2.1522秒

批次 28:
  奖励值: 102.8510
  收益率: 0.3384
  距离: 26.2797
  内存使用: 0.4770
  能量使用: 0.7413
  推理时间: 2.0841秒

批次 29:
  奖励值: 102.6535
  收益率: 0.3437
  距离: 26.4493
  内存使用: 0.5054
  能量使用: 0.7382
  推理时间: 2.0551秒

批次 30:
  奖励值: 116.9338
  收益率: 0.3758
  距离: 29.4267
  内存使用: 0.5754
  能量使用: 0.9303
  推理时间: 2.3440秒

批次 31:
  奖励值: 107.6775
  收益率: 0.3531
  距离: 27.6816
  内存使用: 0.5104
  能量使用: 0.8530
  推理时间: 2.1864秒

批次 32:
  奖励值: 107.2452
  收益率: 0.3472
  距离: 26.6054
  内存使用: 0.4808
  能量使用: 0.8233
  推理时间: 2.0749秒

批次 33:
  奖励值: 108.5094
  收益率: 0.3600
  距离: 26.6281
  内存使用: 0.5419
  能量使用: 0.8112
  推理时间: 2.1662秒

批次 34:
  奖励值: 99.1473
  收益率: 0.3375
  距离: 24.0278
  内存使用: 0.4861
  能量使用: 0.7913
  推理时间: 2.0486秒

批次 35:
  奖励值: 107.7565
  收益率: 0.3600
  距离: 28.1681
  内存使用: 0.4909
  能量使用: 0.8281
  推理时间: 2.1523秒

批次 36:
  奖励值: 109.3329
  收益率: 0.3665
  距离: 27.1924
  内存使用: 0.4981
  能量使用: 0.8437
  推理时间: 2.1617秒

批次 37:
  奖励值: 105.2520
  收益率: 0.3555
  距离: 26.2912
  内存使用: 0.5620
  能量使用: 0.8429
  推理时间: 2.0406秒

批次 38:
  奖励值: 112.2172
  收益率: 0.3689
  距离: 27.1756
  内存使用: 0.5293
  能量使用: 0.8607
  推理时间: 2.2245秒

批次 39:
  奖励值: 110.7619
  收益率: 0.3618
  距离: 23.6981
  内存使用: 0.5516
  能量使用: 0.8265
  推理时间: 2.2195秒

批次 40:
  奖励值: 101.6004
  收益率: 0.3372
  距离: 23.6811
  内存使用: 0.4237
  能量使用: 0.6943
  推理时间: 2.0068秒

批次 41:
  奖励值: 108.8762
  收益率: 0.3646
  距离: 24.8584
  内存使用: 0.5613
  能量使用: 0.8277
  推理时间: 2.1760秒

批次 42:
  奖励值: 104.2148
  收益率: 0.3307
  距离: 26.0671
  内存使用: 0.5451
  能量使用: 0.7582
  推理时间: 2.0572秒

批次 43:
  奖励值: 106.7827
  收益率: 0.3525
  距离: 25.9836
  内存使用: 0.5025
  能量使用: 0.7484
  推理时间: 2.1761秒

批次 44:
  奖励值: 117.4919
  收益率: 0.3842
  距离: 28.7113
  内存使用: 0.5521
  能量使用: 0.9528
  推理时间: 2.3267秒

批次 45:
  奖励值: 99.8728
  收益率: 0.3358
  距离: 24.0207
  内存使用: 0.4276
  能量使用: 0.8060
  推理时间: 2.0377秒

批次 46:
  奖励值: 103.5740
  收益率: 0.3399
  距离: 26.4000
  内存使用: 0.5181
  能量使用: 0.7339
  推理时间: 2.0573秒

批次 47:
  奖励值: 110.8154
  收益率: 0.3588
  距离: 23.0743
  内存使用: 0.5183
  能量使用: 0.7767
  推理时间: 2.2042秒

批次 48:
  奖励值: 101.7952
  收益率: 0.3409
  距离: 24.7400
  内存使用: 0.4578
  能量使用: 0.8119
  推理时间: 2.0879秒

批次 49:
  奖励值: 110.0769
  收益率: 0.3677
  距离: 29.5309
  内存使用: 0.5554
  能量使用: 0.7600
  推理时间: 2.2115秒

批次 50:
  奖励值: 115.0225
  收益率: 0.3709
  距离: 28.3502
  内存使用: 0.5546
  能量使用: 0.8503
  推理时间: 2.2775秒

批次 51:
  奖励值: 106.5248
  收益率: 0.3694
  距离: 25.7703
  内存使用: 0.4768
  能量使用: 0.9033
  推理时间: 2.2222秒

批次 52:
  奖励值: 109.3384
  收益率: 0.3564
  距离: 25.0969
  内存使用: 0.5153
  能量使用: 0.9110
  推理时间: 2.1539秒

批次 53:
  奖励值: 107.5654
  收益率: 0.3612
  距离: 26.3765
  内存使用: 0.4622
  能量使用: 0.8303
  推理时间: 2.1520秒

批次 54:
  奖励值: 103.1790
  收益率: 0.3472
  距离: 25.4883
  内存使用: 0.5159
  能量使用: 0.8378
  推理时间: 2.0550秒

批次 55:
  奖励值: 108.2514
  收益率: 0.3710
  距离: 27.9370
  内存使用: 0.5159
  能量使用: 0.8442
  推理时间: 2.2433秒

批次 56:
  奖励值: 103.4431
  收益率: 0.3486
  距离: 25.5975
  内存使用: 0.5209
  能量使用: 0.7953
  推理时间: 1.8994秒

批次 57:
  奖励值: 103.2836
  收益率: 0.3422
  距离: 26.5831
  内存使用: 0.4808
  能量使用: 0.7690
  推理时间: 2.1147秒

批次 58:
  奖励值: 108.6970
  收益率: 0.3528
  距离: 26.4455
  内存使用: 0.5442
  能量使用: 0.8163
  推理时间: 2.1187秒

批次 59:
  奖励值: 111.5612
  收益率: 0.3602
  距离: 23.2328
  内存使用: 0.5580
  能量使用: 0.8866
  推理时间: 2.2603秒

批次 60:
  奖励值: 105.1605
  收益率: 0.3657
  距离: 27.2516
  内存使用: 0.4614
  能量使用: 0.7659
  推理时间: 2.1451秒

批次 61:
  奖励值: 106.2153
  收益率: 0.3575
  距离: 29.4647
  内存使用: 0.4744
  能量使用: 0.8303
  推理时间: 2.1641秒

批次 62:
  奖励值: 103.4505
  收益率: 0.3478
  距离: 26.1261
  内存使用: 0.5103
  能量使用: 0.8625
  推理时间: 2.0407秒

批次 63:
  奖励值: 101.0853
  收益率: 0.3454
  距离: 26.9404
  内存使用: 0.4746
  能量使用: 0.8144
  推理时间: 2.0579秒

批次 64:
  奖励值: 95.2441
  收益率: 0.3130
  距离: 22.0182
  内存使用: 0.4358
  能量使用: 0.6577
  推理时间: 1.9255秒

批次 65:
  奖励值: 106.5355
  收益率: 0.3663
  距离: 26.3590
  内存使用: 0.4765
  能量使用: 0.8935
  推理时间: 2.1586秒

批次 66:
  奖励值: 109.1861
  收益率: 0.3721
  距离: 29.5617
  内存使用: 0.4924
  能量使用: 0.7736
  推理时间: 2.2536秒

批次 67:
  奖励值: 111.3941
  收益率: 0.3783
  距离: 28.4172
  内存使用: 0.5095
  能量使用: 0.8851
  推理时间: 2.2400秒

批次 68:
  奖励值: 105.8576
  收益率: 0.3507
  距离: 27.8183
  内存使用: 0.4755
  能量使用: 0.7910
  推理时间: 2.1073秒

批次 69:
  奖励值: 103.2514
  收益率: 0.3368
  距离: 25.2180
  内存使用: 0.4499
  能量使用: 0.8293
  推理时间: 2.0656秒

批次 70:
  奖励值: 105.7115
  收益率: 0.3529
  距离: 25.4254
  内存使用: 0.5033
  能量使用: 0.8482
  推理时间: 2.1594秒

批次 71:
  奖励值: 101.0748
  收益率: 0.3409
  距离: 26.0065
  内存使用: 0.4254
  能量使用: 0.8184
  推理时间: 2.0408秒

批次 72:
  奖励值: 111.2132
  收益率: 0.3606
  距离: 25.2838
  内存使用: 0.5027
  能量使用: 0.7871
  推理时间: 2.0043秒

批次 73:
  奖励值: 109.5881
  收益率: 0.3615
  距离: 26.0965
  内存使用: 0.5487
  能量使用: 0.7786
  推理时间: 2.1894秒

批次 74:
  奖励值: 111.4945
  收益率: 0.3779
  距离: 29.0100
  内存使用: 0.5647
  能量使用: 0.8363
  推理时间: 2.2479秒

批次 75:
  奖励值: 98.9371
  收益率: 0.3375
  距离: 26.4140
  内存使用: 0.4950
  能量使用: 0.8167
  推理时间: 2.0262秒

批次 76:
  奖励值: 102.7231
  收益率: 0.3429
  距离: 25.1644
  内存使用: 0.4851
  能量使用: 0.7043
  推理时间: 2.0276秒

批次 77:
  奖励值: 118.9010
  收益率: 0.3839
  距离: 27.5784
  内存使用: 0.5324
  能量使用: 0.8871
  推理时间: 2.3121秒

批次 78:
  奖励值: 118.0441
  收益率: 0.3827
  距离: 28.6475
  内存使用: 0.5376
  能量使用: 0.9267
  推理时间: 2.3403秒

批次 79:
  奖励值: 112.7988
  收益率: 0.3692
  距离: 29.1224
  内存使用: 0.5486
  能量使用: 0.9313
  推理时间: 2.2341秒

批次 80:
  奖励值: 98.7711
  收益率: 0.3332
  距离: 25.3928
  内存使用: 0.5014
  能量使用: 0.7635
  推理时间: 2.0286秒

批次 81:
  奖励值: 104.5591
  收益率: 0.3579
  距离: 24.6124
  内存使用: 0.5055
  能量使用: 0.7639
  推理时间: 2.0609秒

批次 82:
  奖励值: 101.6983
  收益率: 0.3447
  距离: 26.7676
  内存使用: 0.5040
  能量使用: 0.7318
  推理时间: 1.8526秒

批次 83:
  奖励值: 96.9064
  收益率: 0.3327
  距离: 24.7731
  内存使用: 0.4699
  能量使用: 0.7441
  推理时间: 3.5070秒

批次 84:
  奖励值: 99.5895
  收益率: 0.3270
  距离: 25.3046
  内存使用: 0.5278
  能量使用: 0.7885
  推理时间: 2.0086秒

批次 85:
  奖励值: 105.3397
  收益率: 0.3529
  距离: 24.8460
  内存使用: 0.5333
  能量使用: 0.7412
  推理时间: 2.0799秒

批次 86:
  奖励值: 109.2423
  收益率: 0.3633
  距离: 27.1933
  内存使用: 0.4775
  能量使用: 0.8445
  推理时间: 2.1895秒

批次 87:
  奖励值: 113.6747
  收益率: 0.3764
  距离: 27.5800
  内存使用: 0.5600
  能量使用: 0.9106
  推理时间: 2.2950秒

批次 88:
  奖励值: 104.8376
  收益率: 0.3430
  距离: 23.2672
  内存使用: 0.4199
  能量使用: 0.8376
  推理时间: 2.1016秒

批次 89:
  奖励值: 107.9857
  收益率: 0.3604
  距离: 24.1547
  内存使用: 0.4724
  能量使用: 0.8016
  推理时间: 2.1885秒

批次 90:
  奖励值: 96.7539
  收益率: 0.3230
  距离: 23.6563
  内存使用: 0.4747
  能量使用: 0.7611
  推理时间: 1.9308秒

批次 91:
  奖励值: 109.1321
  收益率: 0.3600
  距离: 29.5530
  内存使用: 0.5870
  能量使用: 0.7958
  推理时间: 2.1802秒

批次 92:
  奖励值: 112.6466
  收益率: 0.3607
  距离: 28.0473
  内存使用: 0.5829
  能量使用: 0.8766
  推理时间: 2.2079秒

批次 93:
  奖励值: 111.8779
  收益率: 0.3545
  距离: 24.4921
  内存使用: 0.5029
  能量使用: 0.8491
  推理时间: 2.1911秒

批次 94:
  奖励值: 102.9444
  收益率: 0.3596
  距离: 26.1807
  内存使用: 0.5453
  能量使用: 0.7853
  推理时间: 2.1637秒

批次 95:
  奖励值: 106.0391
  收益率: 0.3584
  距离: 25.9277
  内存使用: 0.5593
  能量使用: 0.8347
  推理时间: 2.1277秒

批次 96:
  奖励值: 107.0641
  收益率: 0.3585
  距离: 25.0702
  内存使用: 0.5120
  能量使用: 0.8895
  推理时间: 2.1128秒

批次 97:
  奖励值: 104.1847
  收益率: 0.3510
  距离: 23.9543
  内存使用: 0.5155
  能量使用: 0.7608
  推理时间: 2.1141秒

批次 98:
  奖励值: 109.9390
  收益率: 0.3666
  距离: 27.5224
  内存使用: 0.5556
  能量使用: 0.8440
  推理时间: 2.1541秒

批次 99:
  奖励值: 105.9127
  收益率: 0.3468
  距离: 28.6240
  内存使用: 0.5460
  能量使用: 0.8046
  推理时间: 2.1086秒

批次 100:
  奖励值: 115.6517
  收益率: 0.3711
  距离: 26.2910
  内存使用: 0.5412
  能量使用: 0.8644
  推理时间: 2.2692秒


==================== 总结 ====================
平均收益率: 0.3561
平均能量使用: 0.8215
平均推理时间: 2.1721秒
