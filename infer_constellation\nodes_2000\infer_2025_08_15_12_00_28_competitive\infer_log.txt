推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 136.6899
  收益率: 0.1724
  距离: 33.2988
  内存使用: 0.6688
  能量使用: 1.0525
  推理时间: 2.6427秒

批次 2:
  奖励值: 125.2460
  收益率: 0.1571
  距离: 29.4233
  内存使用: 0.8998
  能量使用: 0.9849
  推理时间: 2.6507秒

批次 3:
  奖励值: 145.4788
  收益率: 0.1801
  距离: 32.4346
  内存使用: 0.7392
  能量使用: 1.0685
  推理时间: 2.8139秒

批次 4:
  奖励值: 141.1213
  收益率: 0.1736
  距离: 33.8789
  内存使用: 0.7497
  能量使用: 1.1288
  推理时间: 2.8282秒

批次 5:
  奖励值: 146.2882
  收益率: 0.1860
  距离: 35.7653
  内存使用: 0.7525
  能量使用: 1.1057
  推理时间: 2.8642秒

批次 6:
  奖励值: 112.7324
  收益率: 0.1412
  距离: 28.9280
  内存使用: 0.8996
  能量使用: 0.9077
  推理时间: 2.3537秒

批次 7:
  奖励值: 129.8329
  收益率: 0.1614
  距离: 32.0519
  内存使用: 0.6650
  能量使用: 1.0175
  推理时间: 2.6401秒

批次 8:
  奖励值: 126.8079
  收益率: 0.1586
  距离: 32.0212
  内存使用: 0.6137
  能量使用: 0.9493
  推理时间: 2.4902秒

批次 9:
  奖励值: 134.9479
  收益率: 0.1663
  距离: 32.4510
  内存使用: 0.6638
  能量使用: 0.9893
  推理时间: 2.6457秒

批次 10:
  奖励值: 123.8603
  收益率: 0.1573
  距离: 33.4155
  内存使用: 0.8997
  能量使用: 0.9642
  推理时间: 2.5461秒

批次 11:
  奖励值: 144.3718
  收益率: 0.1836
  距离: 34.5586
  内存使用: 0.8207
  能量使用: 1.0771
  推理时间: 2.8828秒

批次 12:
  奖励值: 131.4239
  收益率: 0.1632
  距离: 28.4975
  内存使用: 0.6570
  能量使用: 0.9976
  推理时间: 2.5793秒

批次 13:
  奖励值: 135.2570
  收益率: 0.1693
  距离: 33.5142
  内存使用: 0.8995
  能量使用: 1.0723
  推理时间: 2.7716秒

批次 14:
  奖励值: 152.8520
  收益率: 0.1922
  距离: 37.4170
  内存使用: 0.8763
  能量使用: 1.2026
  推理时间: 3.0003秒

批次 15:
  奖励值: 128.3369
  收益率: 0.1596
  距离: 30.4592
  内存使用: 0.6715
  能量使用: 0.9954
  推理时间: 2.6188秒

批次 16:
  奖励值: 115.5348
  收益率: 0.1444
  距离: 29.9606
  内存使用: 0.8995
  能量使用: 0.8703
  推理时间: 2.4146秒

批次 17:
  奖励值: 136.3621
  收益率: 0.1687
  距离: 30.2037
  内存使用: 0.6674
  能量使用: 1.0045
  推理时间: 2.6244秒

批次 18:
  奖励值: 132.4308
  收益率: 0.1674
  距离: 34.9958
  内存使用: 0.7290
  能量使用: 1.0135
  推理时间: 2.6685秒

批次 19:
  奖励值: 125.8398
  收益率: 0.1575
  距离: 29.0133
  内存使用: 0.5688
  能量使用: 0.9383
  推理时间: 2.4888秒

批次 20:
  奖励值: 140.3506
  收益率: 0.1788
  距离: 36.0803
  内存使用: 0.6900
  能量使用: 1.1284
  推理时间: 2.8056秒

批次 21:
  奖励值: 134.1760
  收益率: 0.1663
  距离: 25.1652
  内存使用: 0.6592
  能量使用: 1.0237
  推理时间: 2.5557秒

批次 22:
  奖励值: 137.0916
  收益率: 0.1752
  距离: 34.2693
  内存使用: 0.7346
  能量使用: 1.0127
  推理时间: 2.7136秒

批次 23:
  奖励值: 143.9540
  收益率: 0.1774
  距离: 33.1095
  内存使用: 0.7395
  能量使用: 1.0431
  推理时间: 2.7415秒

批次 24:
  奖励值: 133.3189
  收益率: 0.1642
  距离: 30.1072
  内存使用: 0.6333
  能量使用: 1.0217
  推理时间: 2.6429秒

批次 25:
  奖励值: 116.6201
  收益率: 0.1465
  距离: 29.3267
  内存使用: 0.8996
  能量使用: 0.9335
  推理时间: 2.4753秒

批次 26:
  奖励值: 130.3855
  收益率: 0.1661
  距离: 32.2054
  内存使用: 0.6694
  能量使用: 0.9543
  推理时间: 2.5291秒

批次 27:
  奖励值: 133.1668
  收益率: 0.1669
  距离: 33.7351
  内存使用: 0.6879
  能量使用: 1.0433
  推理时间: 2.6566秒

批次 28:
  奖励值: 121.5144
  收益率: 0.1503
  距离: 30.4929
  内存使用: 0.8998
  能量使用: 0.9785
  推理时间: 2.5250秒

批次 29:
  奖励值: 133.2956
  收益率: 0.1670
  距离: 31.1402
  内存使用: 0.6509
  能量使用: 1.0176
  推理时间: 2.5756秒

批次 30:
  奖励值: 135.9439
  收益率: 0.1678
  距离: 30.4443
  内存使用: 0.7142
  能量使用: 0.9536
  推理时间: 2.6624秒

批次 31:
  奖励值: 140.0611
  收益率: 0.1744
  距离: 30.9415
  内存使用: 0.6891
  能量使用: 1.0470
  推理时间: 2.6647秒

批次 32:
  奖励值: 127.4687
  收益率: 0.1593
  距离: 31.2941
  内存使用: 0.8998
  能量使用: 1.0687
  推理时间: 2.7760秒

批次 33:
  奖励值: 147.2110
  收益率: 0.1845
  距离: 36.1436
  内存使用: 0.7300
  能量使用: 1.1044
  推理时间: 2.9318秒

批次 34:
  奖励值: 149.2447
  收益率: 0.1834
  距离: 32.5865
  内存使用: 0.7718
  能量使用: 1.1521
  推理时间: 2.8927秒

批次 35:
  奖励值: 128.4968
  收益率: 0.1569
  距离: 29.6273
  内存使用: 0.6317
  能量使用: 0.9473
  推理时间: 2.5569秒

批次 36:
  奖励值: 136.7105
  收益率: 0.1693
  距离: 33.1810
  内存使用: 0.7110
  能量使用: 1.0868
  推理时间: 2.6881秒

批次 37:
  奖励值: 139.1597
  收益率: 0.1773
  距离: 35.5456
  内存使用: 0.6772
  能量使用: 1.0577
  推理时间: 2.7703秒

批次 38:
  奖励值: 131.7191
  收益率: 0.1682
  距离: 31.8925
  内存使用: 0.7574
  能量使用: 0.9119
  推理时间: 2.6316秒

批次 39:
  奖励值: 122.3526
  收益率: 0.1536
  距离: 28.3020
  内存使用: 0.8995
  能量使用: 0.8932
  推理时间: 2.4361秒

批次 40:
  奖励值: 125.2626
  收益率: 0.1522
  距离: 28.4873
  内存使用: 0.6600
  能量使用: 0.9276
  推理时间: 2.5120秒

批次 41:
  奖励值: 138.7228
  收益率: 0.1737
  距离: 35.8466
  内存使用: 0.6921
  能量使用: 1.0156
  推理时间: 2.8616秒

批次 42:
  奖励值: 130.3958
  收益率: 0.1624
  距离: 31.8786
  内存使用: 0.6927
  能量使用: 1.0371
  推理时间: 2.6097秒

批次 43:
  奖励值: 128.0515
  收益率: 0.1593
  距离: 32.4960
  内存使用: 0.6646
  能量使用: 0.9365
  推理时间: 2.5560秒

批次 44:
  奖励值: 161.8717
  收益率: 0.2016
  距离: 38.4498
  内存使用: 0.8146
  能量使用: 1.1920
  推理时间: 3.1942秒

批次 45:
  奖励值: 128.5085
  收益率: 0.1602
  距离: 31.5674
  内存使用: 0.5862
  能量使用: 1.0295
  推理时间: 2.4991秒

批次 46:
  奖励值: 115.4262
  收益率: 0.1504
  距离: 31.3334
  内存使用: 0.8985
  能量使用: 0.9157
  推理时间: 2.3463秒

批次 47:
  奖励值: 130.0046
  收益率: 0.1624
  距离: 34.1565
  内存使用: 0.6285
  能量使用: 1.0057
  推理时间: 2.6714秒

批次 48:
  奖励值: 135.6746
  收益率: 0.1686
  距离: 32.4709
  内存使用: 0.7248
  能量使用: 1.0549
  推理时间: 2.7118秒

批次 49:
  奖励值: 131.9779
  收益率: 0.1669
  距离: 32.1302
  内存使用: 0.6907
  能量使用: 1.0233
  推理时间: 2.5801秒

批次 50:
  奖励值: 126.8445
  收益率: 0.1584
  距离: 31.4462
  内存使用: 0.8996
  能量使用: 0.9651
  推理时间: 2.5400秒

批次 51:
  奖励值: 127.5406
  收益率: 0.1622
  距离: 32.1691
  内存使用: 0.7053
  能量使用: 1.0444
  推理时间: 2.4887秒

批次 52:
  奖励值: 117.5910
  收益率: 0.1463
  距离: 30.5854
  内存使用: 0.8999
  能量使用: 0.9327
  推理时间: 2.4372秒

批次 53:
  奖励值: 119.6994
  收益率: 0.1487
  距离: 27.9285
  内存使用: 0.6003
  能量使用: 0.8725
  推理时间: 2.3899秒

批次 54:
  奖励值: 124.4698
  收益率: 0.1554
  距离: 27.4728
  内存使用: 0.6012
  能量使用: 0.9863
  推理时间: 2.5370秒

批次 55:
  奖励值: 130.4915
  收益率: 0.1645
  距离: 32.8759
  内存使用: 0.7459
  能量使用: 0.9953
  推理时间: 2.6233秒

批次 56:
  奖励值: 120.8309
  收益率: 0.1517
  距离: 29.3119
  内存使用: 0.8997
  能量使用: 0.9266
  推理时间: 2.4341秒

批次 57:
  奖励值: 138.4570
  收益率: 0.1717
  距离: 31.6873
  内存使用: 0.7062
  能量使用: 1.0924
  推理时间: 2.7552秒

批次 58:
  奖励值: 123.8619
  收益率: 0.1550
  距离: 30.2259
  内存使用: 0.8995
  能量使用: 0.9507
  推理时间: 2.5515秒

批次 59:
  奖励值: 142.7231
  收益率: 0.1778
  距离: 31.9935
  内存使用: 0.7185
  能量使用: 1.0418
  推理时间: 2.7913秒

批次 60:
  奖励值: 121.7808
  收益率: 0.1560
  距离: 30.5977
  内存使用: 0.8999
  能量使用: 0.9408
  推理时间: 2.5332秒

批次 61:
  奖励值: 134.8894
  收益率: 0.1677
  距离: 32.9570
  内存使用: 0.6613
  能量使用: 1.0029
  推理时间: 2.6503秒

批次 62:
  奖励值: 133.4224
  收益率: 0.1659
  距离: 34.1038
  内存使用: 0.6428
  能量使用: 1.0087
  推理时间: 2.6378秒

批次 63:
  奖励值: 127.2297
  收益率: 0.1597
  距离: 32.5934
  内存使用: 0.5835
  能量使用: 0.9636
  推理时间: 2.5690秒

批次 64:
  奖励值: 126.4807
  收益率: 0.1595
  距离: 31.0982
  内存使用: 0.5917
  能量使用: 0.9486
  推理时间: 2.5005秒

批次 65:
  奖励值: 138.7557
  收益率: 0.1684
  距离: 33.5700
  内存使用: 0.6642
  能量使用: 1.0544
  推理时间: 2.7360秒

批次 66:
  奖励值: 116.5588
  收益率: 0.1475
  距离: 28.4568
  内存使用: 0.8992
  能量使用: 0.9263
  推理时间: 2.3253秒

批次 67:
  奖励值: 114.5494
  收益率: 0.1422
  距离: 25.6700
  内存使用: 0.8999
  能量使用: 0.9312
  推理时间: 2.2946秒

批次 68:
  奖励值: 146.5114
  收益率: 0.1833
  距离: 36.9409
  内存使用: 0.8071
  能量使用: 1.0292
  推理时间: 2.9330秒

批次 69:
  奖励值: 112.7612
  收益率: 0.1410
  距离: 25.0377
  内存使用: 0.8555
  能量使用: 0.7844
  推理时间: 2.3695秒

批次 70:
  奖励值: 139.7258
  收益率: 0.1732
  距离: 35.5235
  内存使用: 0.7352
  能量使用: 1.0897
  推理时间: 2.7813秒

批次 71:
  奖励值: 117.9679
  收益率: 0.1472
  距离: 29.4435
  内存使用: 0.8994
  能量使用: 0.9212
  推理时间: 2.4652秒

批次 72:
  奖励值: 132.4766
  收益率: 0.1631
  距离: 31.1660
  内存使用: 0.6926
  能量使用: 0.9881
  推理时间: 2.5882秒

批次 73:
  奖励值: 127.9522
  收益率: 0.1606
  距离: 33.5157
  内存使用: 0.6422
  能量使用: 0.9376
  推理时间: 2.5928秒

批次 74:
  奖励值: 132.0584
  收益率: 0.1650
  距离: 34.3401
  内存使用: 0.6796
  能量使用: 1.0634
  推理时间: 2.9699秒

批次 75:
  奖励值: 127.1244
  收益率: 0.1583
  距离: 30.8587
  内存使用: 0.6294
  能量使用: 0.8629
  推理时间: 2.5445秒

批次 76:
  奖励值: 130.6933
  收益率: 0.1637
  距离: 31.9563
  内存使用: 0.6486
  能量使用: 1.0086
  推理时间: 2.5515秒

批次 77:
  奖励值: 120.6320
  收益率: 0.1513
  距离: 29.2631
  内存使用: 0.8988
  能量使用: 0.9741
  推理时间: 2.4741秒

批次 78:
  奖励值: 115.9059
  收益率: 0.1496
  距离: 31.9902
  内存使用: 0.8999
  能量使用: 0.8981
  推理时间: 2.4348秒

批次 79:
  奖励值: 127.1111
  收益率: 0.1621
  距离: 30.6544
  内存使用: 0.6095
  能量使用: 0.9138
  推理时间: 2.5723秒

批次 80:
  奖励值: 127.7983
  收益率: 0.1622
  距离: 35.4749
  内存使用: 0.5716
  能量使用: 0.9567
  推理时间: 2.5916秒

批次 81:
  奖励值: 121.9745
  收益率: 0.1546
  距离: 32.0925
  内存使用: 0.8959
  能量使用: 0.8624
  推理时间: 2.5363秒

批次 82:
  奖励值: 134.6619
  收益率: 0.1701
  距离: 35.3872
  内存使用: 0.6246
  能量使用: 1.0643
  推理时间: 2.7169秒

批次 83:
  奖励值: 133.0631
  收益率: 0.1669
  距离: 34.1556
  内存使用: 0.6451
  能量使用: 1.0731
  推理时间: 2.6668秒

批次 84:
  奖励值: 127.7528
  收益率: 0.1617
  距离: 33.6328
  内存使用: 0.8997
  能量使用: 0.9618
  推理时间: 2.5280秒

批次 85:
  奖励值: 111.8746
  收益率: 0.1411
  距离: 27.9020
  内存使用: 0.8999
  能量使用: 0.8488
  推理时间: 2.3453秒

批次 86:
  奖励值: 117.6300
  收益率: 0.1449
  距离: 27.9704
  内存使用: 0.8997
  能量使用: 0.8699
  推理时间: 2.4098秒

批次 87:
  奖励值: 122.4860
  收益率: 0.1553
  距离: 31.3837
  内存使用: 0.8999
  能量使用: 0.9444
  推理时间: 2.4422秒

批次 88:
  奖励值: 132.7392
  收益率: 0.1602
  距离: 27.6143
  内存使用: 0.7297
  能量使用: 0.9775
  推理时间: 2.5933秒

批次 89:
  奖励值: 115.2678
  收益率: 0.1424
  距离: 30.2261
  内存使用: 0.8997
  能量使用: 0.9386
  推理时间: 2.3363秒

批次 90:
  奖励值: 123.5202
  收益率: 0.1541
  距离: 29.9664
  内存使用: 0.8998
  能量使用: 0.8933
  推理时间: 2.5511秒

批次 91:
  奖励值: 121.6728
  收益率: 0.1505
  距离: 27.9562
  内存使用: 0.8973
  能量使用: 1.0009
  推理时间: 2.5409秒

批次 92:
  奖励值: 135.6004
  收益率: 0.1657
  距离: 31.7162
  内存使用: 0.8990
  能量使用: 0.9839
  推理时间: 2.7680秒

批次 93:
  奖励值: 139.4241
  收益率: 0.1723
  距离: 34.1734
  内存使用: 0.7204
  能量使用: 1.0579
  推理时间: 2.7783秒

批次 94:
  奖励值: 124.6834
  收益率: 0.1560
  距离: 26.7524
  内存使用: 0.8974
  能量使用: 0.9834
  推理时间: 2.6452秒

批次 95:
  奖励值: 125.7816
  收益率: 0.1598
  距离: 32.9024
  内存使用: 0.5826
  能量使用: 0.9518
  推理时间: 2.6833秒

批次 96:
  奖励值: 124.9316
  收益率: 0.1576
  距离: 30.8178
  内存使用: 0.8992
  能量使用: 1.0199
  推理时间: 2.5499秒

批次 97:
  奖励值: 137.7668
  收益率: 0.1734
  距离: 34.5009
  内存使用: 0.7010
  能量使用: 1.0644
  推理时间: 2.7125秒

批次 98:
  奖励值: 132.6338
  收益率: 0.1636
  距离: 29.9411
  内存使用: 0.6138
  能量使用: 0.9775
  推理时间: 2.5532秒

批次 99:
  奖励值: 121.8415
  收益率: 0.1523
  距离: 29.7955
  内存使用: 0.8997
  能量使用: 1.0217
  推理时间: 2.5757秒

批次 100:
  奖励值: 120.9665
  收益率: 0.1523
  距离: 29.7018
  内存使用: 0.8995
  能量使用: 0.8655
  推理时间: 2.4668秒


==================== 总结 ====================
平均收益率: 0.1627
平均能量使用: 0.9923
平均推理时间: 2.6136秒
