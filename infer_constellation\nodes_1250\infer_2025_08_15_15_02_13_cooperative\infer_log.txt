推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 139.6834
  收益率: 0.2926
  距离: 41.5759
  内存使用: 0.7006
  能量使用: 1.1567
  推理时间: 2.9232秒

批次 2:
  奖励值: 134.1553
  收益率: 0.2681
  距离: 35.0227
  内存使用: 0.7368
  能量使用: 1.1298
  推理时间: 2.9824秒

批次 3:
  奖励值: 131.3254
  收益率: 0.2615
  距离: 32.2066
  内存使用: 0.7056
  能量使用: 1.0369
  推理时间: 2.9147秒

批次 4:
  奖励值: 122.8703
  收益率: 0.2457
  距离: 30.5791
  内存使用: 0.6919
  能量使用: 0.9885
  推理时间: 2.4920秒

批次 5:
  奖励值: 134.8819
  收益率: 0.2644
  距离: 31.2566
  内存使用: 0.7931
  能量使用: 0.9865
  推理时间: 2.7331秒

批次 6:
  奖励值: 135.6442
  收益率: 0.2698
  距离: 35.4772
  内存使用: 0.7494
  能量使用: 1.0851
  推理时间: 2.9471秒

批次 7:
  奖励值: 129.2016
  收益率: 0.2634
  距离: 36.9722
  内存使用: 0.7279
  能量使用: 1.0967
  推理时间: 2.5669秒

批次 8:
  奖励值: 138.5057
  收益率: 0.2740
  距离: 31.0177
  内存使用: 0.7103
  能量使用: 1.0605
  推理时间: 2.6840秒

批次 9:
  奖励值: 146.2249
  收益率: 0.2960
  距离: 41.0897
  内存使用: 0.7523
  能量使用: 1.1081
  推理时间: 3.1599秒

批次 10:
  奖励值: 133.5785
  收益率: 0.2671
  距离: 33.4906
  内存使用: 0.7219
  能量使用: 1.0304
  推理时间: 2.6296秒

批次 11:
  奖励值: 134.4632
  收益率: 0.2724
  距离: 35.0289
  内存使用: 0.7403
  能量使用: 1.1043
  推理时间: 2.7662秒

批次 12:
  奖励值: 144.5915
  收益率: 0.2861
  距离: 40.0548
  内存使用: 0.7950
  能量使用: 1.1672
  推理时间: 2.9264秒

批次 13:
  奖励值: 128.3873
  收益率: 0.2631
  距离: 35.7440
  内存使用: 0.6958
  能量使用: 0.9856
  推理时间: 2.5994秒

批次 14:
  奖励值: 128.8154
  收益率: 0.2588
  距离: 36.4295
  内存使用: 0.7085
  能量使用: 1.0311
  推理时间: 2.8259秒

批次 15:
  奖励值: 133.7295
  收益率: 0.2651
  距离: 30.3660
  内存使用: 0.7488
  能量使用: 1.0263
  推理时间: 2.9628秒

批次 16:
  奖励值: 125.3918
  收益率: 0.2530
  距离: 31.1562
  内存使用: 0.6522
  能量使用: 1.0079
  推理时间: 2.6767秒

批次 17:
  奖励值: 137.5368
  收益率: 0.2768
  距离: 31.3483
  内存使用: 0.7088
  能量使用: 1.0691
  推理时间: 2.8021秒

批次 18:
  奖励值: 128.3226
  收益率: 0.2555
  距离: 33.9751
  内存使用: 0.6885
  能量使用: 1.0912
  推理时间: 2.6660秒

批次 19:
  奖励值: 119.0630
  收益率: 0.2298
  距离: 28.8791
  内存使用: 0.8999
  能量使用: 0.8951
  推理时间: 2.4626秒

批次 20:
  奖励值: 128.8234
  收益率: 0.2564
  距离: 33.4617
  内存使用: 0.6375
  能量使用: 1.0539
  推理时间: 2.6927秒

批次 21:
  奖励值: 130.5247
  收益率: 0.2627
  距离: 33.1918
  内存使用: 0.7147
  能量使用: 1.0441
  推理时间: 2.6411秒

批次 22:
  奖励值: 127.0443
  收益率: 0.2507
  距离: 33.2473
  内存使用: 0.6674
  能量使用: 0.9967
  推理时间: 2.6362秒

批次 23:
  奖励值: 132.8511
  收益率: 0.2694
  距离: 35.2667
  内存使用: 0.7823
  能量使用: 1.0487
  推理时间: 2.7828秒

批次 24:
  奖励值: 123.2954
  收益率: 0.2476
  距离: 33.2680
  内存使用: 0.6624
  能量使用: 1.0787
  推理时间: 2.8206秒

批次 25:
  奖励值: 129.2317
  收益率: 0.2600
  距离: 32.5805
  内存使用: 0.6999
  能量使用: 1.0561
  推理时间: 2.9052秒

批次 26:
  奖励值: 126.8237
  收益率: 0.2554
  距离: 32.1997
  内存使用: 0.7773
  能量使用: 0.9755
  推理时间: 2.8178秒

批次 27:
  奖励值: 130.7858
  收益率: 0.2623
  距离: 32.2648
  内存使用: 0.7438
  能量使用: 1.0197
  推理时间: 2.6094秒

批次 28:
  奖励值: 121.4371
  收益率: 0.2378
  距离: 31.4410
  内存使用: 0.6118
  能量使用: 0.8730
  推理时间: 2.5192秒

批次 29:
  奖励值: 143.6822
  收益率: 0.2871
  距离: 36.2338
  内存使用: 0.7271
  能量使用: 1.0747
  推理时间: 3.1552秒

批次 30:
  奖励值: 139.8180
  收益率: 0.2767
  距离: 33.1124
  内存使用: 0.7845
  能量使用: 1.0671
  推理时间: 2.9134秒

批次 31:
  奖励值: 136.2626
  收益率: 0.2789
  距离: 34.6802
  内存使用: 0.7215
  能量使用: 1.0730
  推理时间: 2.8342秒

批次 32:
  奖励值: 132.7203
  收益率: 0.2655
  距离: 34.2974
  内存使用: 0.7678
  能量使用: 1.0942
  推理时间: 2.7576秒

批次 33:
  奖励值: 140.5790
  收益率: 0.2804
  距离: 36.3471
  内存使用: 0.7488
  能量使用: 1.1460
  推理时间: 2.9152秒

批次 34:
  奖励值: 140.8622
  收益率: 0.2848
  距离: 35.1942
  内存使用: 0.7338
  能量使用: 1.1475
  推理时间: 2.8785秒

批次 35:
  奖励值: 138.7491
  收益率: 0.2780
  距离: 35.1286
  内存使用: 0.8362
  能量使用: 1.1370
  推理时间: 3.0646秒

批次 36:
  奖励值: 136.7110
  收益率: 0.2813
  距离: 37.0269
  内存使用: 0.8382
  能量使用: 1.1128
  推理时间: 2.8498秒

批次 37:
  奖励值: 140.9227
  收益率: 0.2788
  距离: 31.2624
  内存使用: 0.7991
  能量使用: 1.0930
  推理时间: 3.1097秒

批次 38:
  奖励值: 142.0071
  收益率: 0.2817
  距离: 37.7440
  内存使用: 0.7206
  能量使用: 1.1211
  推理时间: 2.9788秒

批次 39:
  奖励值: 135.7201
  收益率: 0.2741
  距离: 37.0012
  内存使用: 0.7393
  能量使用: 1.0192
  推理时间: 2.7662秒

批次 40:
  奖励值: 126.0858
  收益率: 0.2515
  距离: 29.3605
  内存使用: 0.6609
  能量使用: 1.0069
  推理时间: 2.7846秒

批次 41:
  奖励值: 131.7254
  收益率: 0.2633
  距离: 31.4106
  内存使用: 0.7238
  能量使用: 1.0148
  推理时间: 2.6269秒

批次 42:
  奖励值: 123.0182
  收益率: 0.2428
  距离: 30.7027
  内存使用: 0.6420
  能量使用: 1.0189
  推理时间: 2.4864秒

批次 43:
  奖励值: 140.0753
  收益率: 0.2838
  距离: 36.9174
  内存使用: 0.7884
  能量使用: 1.1435
  推理时间: 2.8057秒

批次 44:
  奖励值: 134.3451
  收益率: 0.2676
  距离: 31.9731
  内存使用: 0.7233
  能量使用: 1.0133
  推理时间: 2.7154秒

批次 45:
  奖励值: 127.3280
  收益率: 0.2609
  距离: 34.5942
  内存使用: 0.6703
  能量使用: 1.0794
  推理时间: 2.6699秒

批次 46:
  奖励值: 113.0803
  收益率: 0.2275
  距离: 27.5490
  内存使用: 0.6062
  能量使用: 0.8842
  推理时间: 2.2867秒

批次 47:
  奖励值: 139.5823
  收益率: 0.2731
  距离: 31.4420
  内存使用: 0.7507
  能量使用: 1.0427
  推理时间: 2.7265秒

批次 48:
  奖励值: 122.1431
  收益率: 0.2453
  距离: 27.5426
  内存使用: 0.6601
  能量使用: 0.9112
  推理时间: 2.6579秒

批次 49:
  奖励值: 138.1213
  收益率: 0.2790
  距离: 35.6665
  内存使用: 0.7928
  能量使用: 1.0981
  推理时间: 2.9553秒

批次 50:
  奖励值: 125.7402
  收益率: 0.2588
  距离: 34.6702
  内存使用: 0.6533
  能量使用: 0.9961
  推理时间: 2.8710秒

批次 51:
  奖励值: 112.2311
  收益率: 0.2293
  距离: 32.2228
  内存使用: 0.8259
  能量使用: 0.8891
  推理时间: 2.5941秒

批次 52:
  奖励值: 131.7398
  收益率: 0.2673
  距离: 34.2506
  内存使用: 0.6200
  能量使用: 1.0498
  推理时间: 2.7071秒

批次 53:
  奖励值: 119.6452
  收益率: 0.2403
  距离: 31.6644
  内存使用: 0.7224
  能量使用: 0.9228
  推理时间: 2.5079秒

批次 54:
  奖励值: 131.5605
  收益率: 0.2705
  距离: 31.8181
  内存使用: 0.6565
  能量使用: 1.0043
  推理时间: 2.9149秒

批次 55:
  奖励值: 139.5184
  收益率: 0.2878
  距离: 45.0876
  内存使用: 0.7848
  能量使用: 1.0908
  推理时间: 2.9367秒

批次 56:
  奖励值: 99.1749
  收益率: 0.1987
  距离: 24.5176
  内存使用: 0.4511
  能量使用: 0.6815
  推理时间: 2.2649秒

批次 57:
  奖励值: 121.6891
  收益率: 0.2395
  距离: 28.5697
  内存使用: 0.6307
  能量使用: 1.0213
  推理时间: 2.4353秒

批次 58:
  奖励值: 143.8140
  收益率: 0.2946
  距离: 37.8937
  内存使用: 0.7070
  能量使用: 1.1712
  推理时间: 2.8664秒

批次 59:
  奖励值: 137.0819
  收益率: 0.2698
  距离: 37.1878
  内存使用: 0.6743
  能量使用: 1.1740
  推理时间: 2.9500秒

批次 60:
  奖励值: 127.3324
  收益率: 0.2668
  距离: 36.7095
  内存使用: 0.7532
  能量使用: 1.0725
  推理时间: 2.8588秒

批次 61:
  奖励值: 123.6443
  收益率: 0.2441
  距离: 35.1596
  内存使用: 0.6107
  能量使用: 0.9411
  推理时间: 2.5019秒

批次 62:
  奖励值: 136.5607
  收益率: 0.2696
  距离: 30.2149
  内存使用: 0.7972
  能量使用: 1.0665
  推理时间: 2.6779秒

批次 63:
  奖励值: 136.5852
  收益率: 0.2761
  距离: 36.2353
  内存使用: 0.7394
  能量使用: 1.0548
  推理时间: 2.6867秒

批次 64:
  奖励值: 126.3049
  收益率: 0.2527
  距离: 35.7771
  内存使用: 0.6593
  能量使用: 0.9537
  推理时间: 2.5641秒

批次 65:
  奖励值: 120.8480
  收益率: 0.2432
  距离: 33.9601
  内存使用: 0.6356
  能量使用: 0.9082
  推理时间: 2.4458秒

批次 66:
  奖励值: 129.9862
  收益率: 0.2662
  距离: 32.6713
  内存使用: 0.6953
  能量使用: 1.0610
  推理时间: 2.7740秒

批次 67:
  奖励值: 126.6685
  收益率: 0.2542
  距离: 34.3963
  内存使用: 0.6484
  能量使用: 1.0182
  推理时间: 2.5326秒

批次 68:
  奖励值: 135.2243
  收益率: 0.2797
  距离: 34.8530
  内存使用: 0.7551
  能量使用: 1.1432
  推理时间: 2.7046秒

批次 69:
  奖励值: 124.7243
  收益率: 0.2529
  距离: 32.6985
  内存使用: 0.6670
  能量使用: 0.9858
  推理时间: 2.4792秒

批次 70:
  奖励值: 119.5010
  收益率: 0.2381
  距离: 36.0561
  内存使用: 0.6300
  能量使用: 0.9119
  推理时间: 2.3996秒

批次 71:
  奖励值: 147.7942
  收益率: 0.2987
  距离: 36.4492
  内存使用: 0.7792
  能量使用: 1.1831
  推理时间: 3.1838秒

批次 72:
  奖励值: 127.2745
  收益率: 0.2558
  距离: 32.7457
  内存使用: 0.7113
  能量使用: 0.9939
  推理时间: 2.5224秒

批次 73:
  奖励值: 111.3902
  收益率: 0.2204
  距离: 27.7420
  内存使用: 0.6054
  能量使用: 0.8722
  推理时间: 2.2706秒

批次 74:
  奖励值: 140.8022
  收益率: 0.2867
  距离: 37.7169
  内存使用: 0.7339
  能量使用: 1.1446
  推理时间: 3.0753秒

批次 75:
  奖励值: 134.1100
  收益率: 0.2656
  距离: 33.1327
  内存使用: 0.7742
  能量使用: 1.0732
  推理时间: 2.8946秒

批次 76:
  奖励值: 132.9799
  收益率: 0.2658
  距离: 31.0264
  内存使用: 0.7175
  能量使用: 1.0962
  推理时间: 2.6005秒

批次 77:
  奖励值: 129.2020
  收益率: 0.2664
  距离: 33.8357
  内存使用: 0.6898
  能量使用: 1.0705
  推理时间: 2.6213秒

批次 78:
  奖励值: 123.7731
  收益率: 0.2489
  距离: 31.1366
  内存使用: 0.7041
  能量使用: 1.0132
  推理时间: 2.7027秒

批次 79:
  奖励值: 121.7128
  收益率: 0.2417
  距离: 31.1882
  内存使用: 0.6457
  能量使用: 0.9406
  推理时间: 2.4975秒

批次 80:
  奖励值: 134.3258
  收益率: 0.2727
  距离: 36.1109
  内存使用: 0.7748
  能量使用: 1.0627
  推理时间: 2.7402秒

批次 81:
  奖励值: 136.1780
  收益率: 0.2770
  距离: 34.1267
  内存使用: 0.7757
  能量使用: 1.1416
  推理时间: 2.7062秒

批次 82:
  奖励值: 133.5039
  收益率: 0.2606
  距离: 32.3856
  内存使用: 0.7173
  能量使用: 1.0892
  推理时间: 2.5822秒

批次 83:
  奖励值: 127.3280
  收益率: 0.2534
  距离: 30.2997
  内存使用: 0.6545
  能量使用: 0.9898
  推理时间: 2.5013秒

批次 84:
  奖励值: 122.6754
  收益率: 0.2531
  距离: 33.4955
  内存使用: 0.8995
  能量使用: 1.0541
  推理时间: 2.5334秒

批次 85:
  奖励值: 133.3183
  收益率: 0.2732
  距离: 36.0188
  内存使用: 0.6893
  能量使用: 1.0553
  推理时间: 2.9140秒

批次 86:
  奖励值: 131.2587
  收益率: 0.2648
  距离: 35.5880
  内存使用: 0.6585
  能量使用: 1.0360
  推理时间: 2.8024秒

批次 87:
  奖励值: 129.9250
  收益率: 0.2634
  距离: 31.7953
  内存使用: 0.6438
  能量使用: 1.1108
  推理时间: 2.5540秒

批次 88:
  奖励值: 130.8666
  收益率: 0.2704
  距离: 33.0701
  内存使用: 0.7691
  能量使用: 1.0276
  推理时间: 2.8368秒

批次 89:
  奖励值: 135.1337
  收益率: 0.2687
  距离: 33.6053
  内存使用: 0.6406
  能量使用: 1.0867
  推理时间: 2.6778秒

批次 90:
  奖励值: 138.6692
  收益率: 0.2823
  距离: 40.1977
  内存使用: 0.8343
  能量使用: 1.2295
  推理时间: 3.0530秒

批次 91:
  奖励值: 140.9921
  收益率: 0.2767
  距离: 35.0268
  内存使用: 0.7153
  能量使用: 1.0735
  推理时间: 2.9631秒

批次 92:
  奖励值: 133.0488
  收益率: 0.2689
  距离: 32.7319
  内存使用: 0.7766
  能量使用: 0.9939
  推理时间: 2.8073秒

批次 93:
  奖励值: 131.1526
  收益率: 0.2613
  距离: 31.5384
  内存使用: 0.7574
  能量使用: 0.9990
  推理时间: 2.7840秒

批次 94:
  奖励值: 133.8512
  收益率: 0.2691
  距离: 33.8970
  内存使用: 0.7328
  能量使用: 1.0677
  推理时间: 2.6148秒

批次 95:
  奖励值: 122.1406
  收益率: 0.2381
  距离: 32.0910
  内存使用: 0.6158
  能量使用: 1.0120
  推理时间: 2.3806秒

批次 96:
  奖励值: 138.8024
  收益率: 0.2773
  距离: 39.0755
  内存使用: 0.7808
  能量使用: 1.1389
  推理时间: 3.0591秒

批次 97:
  奖励值: 144.5465
  收益率: 0.2952
  距离: 37.9932
  内存使用: 0.7712
  能量使用: 1.1425
  推理时间: 2.8803秒

批次 98:
  奖励值: 116.9641
  收益率: 0.2317
  距离: 30.4379
  内存使用: 0.8998
  能量使用: 0.9341
  推理时间: 2.6656秒

批次 99:
  奖励值: 141.3067
  收益率: 0.2844
  距离: 37.4935
  内存使用: 0.7627
  能量使用: 1.1303
  推理时间: 2.8309秒

批次 100:
  奖励值: 138.0491
  收益率: 0.2791
  距离: 38.6447
  内存使用: 0.7612
  能量使用: 1.0957
  推理时间: 3.0374秒


==================== 总结 ====================
平均收益率: 0.2640
平均能量使用: 1.0448
平均推理时间: 2.7405秒
