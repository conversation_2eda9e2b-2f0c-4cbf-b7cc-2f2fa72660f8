推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 28.3091
  收益率: 0.6984
  距离: 6.1896
  内存使用: -0.0473
  能量使用: 0.2394
  推理时间: 0.9331秒

批次 2:
  奖励值: 26.2695
  收益率: 0.6466
  距离: 7.3577
  内存使用: -0.0818
  能量使用: 0.1852
  推理时间: 0.5129秒

批次 3:
  奖励值: 28.9923
  收益率: 0.7255
  距离: 8.4049
  内存使用: -0.0315
  能量使用: 0.2420
  推理时间: 0.5920秒

批次 4:
  奖励值: 27.2928
  收益率: 0.6732
  距离: 7.4118
  内存使用: -0.0614
  能量使用: 0.2640
  推理时间: 0.6698秒

批次 5:
  奖励值: 26.4822
  收益率: 0.6659
  距离: 5.5131
  内存使用: -0.1012
  能量使用: 0.2237
  推理时间: 0.6582秒

批次 6:
  奖励值: 29.8447
  收益率: 0.6945
  距离: 8.3529
  内存使用: -0.0833
  能量使用: 0.2409
  推理时间: 0.6763秒

批次 7:
  奖励值: 28.0721
  收益率: 0.7283
  距离: 9.0182
  内存使用: -0.0461
  能量使用: 0.2837
  推理时间: 0.6643秒

批次 8:
  奖励值: 27.2998
  收益率: 0.6645
  距离: 7.0396
  内存使用: -0.0798
  能量使用: 0.2406
  推理时间: 0.5767秒

批次 9:
  奖励值: 25.6390
  收益率: 0.6473
  距离: 8.8018
  内存使用: -0.1079
  能量使用: 0.2281
  推理时间: 0.5802秒

批次 10:
  奖励值: 26.3099
  收益率: 0.6534
  距离: 9.0546
  内存使用: 0.2614
  能量使用: 0.2642
  推理时间: 0.5929秒

批次 11:
  奖励值: 29.7124
  收益率: 0.7528
  距离: 8.5880
  内存使用: -0.0842
  能量使用: 0.2715
  推理时间: 0.6773秒

批次 12:
  奖励值: 30.9545
  收益率: 0.7155
  距离: 6.3212
  内存使用: -0.0760
  能量使用: 0.2458
  推理时间: 0.6392秒

批次 13:
  奖励值: 30.4070
  收益率: 0.7273
  距离: 9.3446
  内存使用: -0.0169
  能量使用: 0.2583
  推理时间: 0.8342秒

批次 14:
  奖励值: 27.6953
  收益率: 0.6896
  距离: 6.5976
  内存使用: -0.0433
  能量使用: 0.2551
  推理时间: 0.7214秒

批次 15:
  奖励值: 28.1378
  收益率: 0.6730
  距离: 7.7705
  内存使用: -0.0637
  能量使用: 0.2176
  推理时间: 0.6250秒

批次 16:
  奖励值: 31.0380
  收益率: 0.7365
  距离: 8.7141
  内存使用: -0.0370
  能量使用: 0.2470
  推理时间: 0.9463秒

批次 17:
  奖励值: 27.9828
  收益率: 0.6824
  距离: 7.3750
  内存使用: -0.0296
  能量使用: 0.2890
  推理时间: 0.6847秒

批次 18:
  奖励值: 24.2478
  收益率: 0.6183
  距离: 6.8114
  内存使用: -0.1028
  能量使用: 0.1957
  推理时间: 0.5788秒

批次 19:
  奖励值: 28.6786
  收益率: 0.6957
  距离: 9.6334
  内存使用: -0.0636
  能量使用: 0.2730
  推理时间: 0.7257秒

批次 20:
  奖励值: 27.9321
  收益率: 0.7009
  距离: 6.7697
  内存使用: -0.0547
  能量使用: 0.2209
  推理时间: 0.6684秒

批次 21:
  奖励值: 28.6226
  收益率: 0.6809
  距离: 6.5724
  内存使用: -0.0723
  能量使用: 0.2547
  推理时间: 0.7828秒

批次 22:
  奖励值: 27.5916
  收益率: 0.6539
  距离: 6.1996
  内存使用: -0.0132
  能量使用: 0.2305
  推理时间: 0.7625秒

批次 23:
  奖励值: 28.3769
  收益率: 0.6774
  距离: 6.4430
  内存使用: -0.0490
  能量使用: 0.2523
  推理时间: 0.7511秒

批次 24:
  奖励值: 26.0848
  收益率: 0.6728
  距离: 6.4565
  内存使用: -0.0619
  能量使用: 0.2357
  推理时间: 0.6741秒

批次 25:
  奖励值: 29.5385
  收益率: 0.7052
  距离: 9.1253
  内存使用: -0.0758
  能量使用: 0.2734
  推理时间: 0.7180秒

批次 26:
  奖励值: 25.4772
  收益率: 0.6342
  距离: 7.4802
  内存使用: -0.0728
  能量使用: 0.2382
  推理时间: 0.6413秒

批次 27:
  奖励值: 28.0096
  收益率: 0.6639
  距离: 8.5916
  内存使用: -0.0483
  能量使用: 0.2375
  推理时间: 0.6912秒

批次 28:
  奖励值: 28.3441
  收益率: 0.6933
  距离: 7.3018
  内存使用: -0.0009
  能量使用: 0.2109
  推理时间: 0.7971秒

批次 29:
  奖励值: 28.3757
  收益率: 0.7082
  距离: 6.7277
  内存使用: -0.0958
  能量使用: 0.2168
  推理时间: 0.7674秒

批次 30:
  奖励值: 28.5855
  收益率: 0.7056
  距离: 7.8123
  内存使用: -0.0429
  能量使用: 0.2597
  推理时间: 0.7836秒

批次 31:
  奖励值: 31.6507
  收益率: 0.7231
  距离: 9.0668
  内存使用: -0.0267
  能量使用: 0.2584
  推理时间: 0.8773秒

批次 32:
  奖励值: 27.9042
  收益率: 0.6844
  距离: 8.1888
  内存使用: -0.0400
  能量使用: 0.2429
  推理时间: 0.7361秒

批次 33:
  奖励值: 26.7188
  收益率: 0.6538
  距离: 7.6218
  内存使用: -0.0960
  能量使用: 0.2366
  推理时间: 0.6728秒

批次 34:
  奖励值: 26.9212
  收益率: 0.6581
  距离: 7.2429
  内存使用: -0.0897
  能量使用: 0.2044
  推理时间: 0.6960秒

批次 35:
  奖励值: 28.7629
  收益率: 0.7182
  距离: 10.0390
  内存使用: -0.0324
  能量使用: 0.2677
  推理时间: 0.8358秒

批次 36:
  奖励值: 33.7625
  收益率: 0.7565
  距离: 8.0547
  内存使用: -0.0788
  能量使用: 0.2992
  推理时间: 0.8388秒

批次 37:
  奖励值: 23.1233
  收益率: 0.6256
  距离: 5.4726
  内存使用: -0.1171
  能量使用: 0.1979
  推理时间: 0.6055秒

批次 38:
  奖励值: 24.1626
  收益率: 0.6544
  距离: 8.4032
  内存使用: -0.1261
  能量使用: 0.1978
  推理时间: 0.6061秒

批次 39:
  奖励值: 29.3701
  收益率: 0.7158
  距离: 8.0745
  内存使用: -0.0628
  能量使用: 0.2481
  推理时间: 0.6515秒

批次 40:
  奖励值: 27.0051
  收益率: 0.6733
  距离: 6.4224
  内存使用: -0.0782
  能量使用: 0.2455
  推理时间: 0.7208秒

批次 41:
  奖励值: 27.2333
  收益率: 0.7036
  距离: 7.5692
  内存使用: -0.0684
  能量使用: 0.2325
  推理时间: 0.7318秒

批次 42:
  奖励值: 28.7138
  收益率: 0.6853
  距离: 8.6040
  内存使用: -0.0585
  能量使用: 0.2268
  推理时间: 0.7844秒

批次 43:
  奖励值: 27.2742
  收益率: 0.6797
  距离: 8.0608
  内存使用: -0.0396
  能量使用: 0.2305
  推理时间: 0.6007秒

批次 44:
  奖励值: 26.9945
  收益率: 0.6506
  距离: 8.0630
  内存使用: -0.1018
  能量使用: 0.2498
  推理时间: 0.6270秒

批次 45:
  奖励值: 29.0200
  收益率: 0.7229
  距离: 8.5724
  内存使用: -0.0420
  能量使用: 0.2295
  推理时间: 0.8015秒

批次 46:
  奖励值: 29.0997
  收益率: 0.7143
  距离: 7.6490
  内存使用: -0.0952
  能量使用: 0.2467
  推理时间: 0.8000秒

批次 47:
  奖励值: 26.5270
  收益率: 0.6674
  距离: 7.0037
  内存使用: -0.0955
  能量使用: 0.2377
  推理时间: 0.7282秒

批次 48:
  奖励值: 28.6073
  收益率: 0.7043
  距离: 8.4746
  内存使用: 0.2504
  能量使用: 0.2604
  推理时间: 0.8618秒

批次 49:
  奖励值: 32.4098
  收益率: 0.7439
  距离: 8.2249
  内存使用: -0.0713
  能量使用: 0.2269
  推理时间: 0.8801秒

批次 50:
  奖励值: 30.0846
  收益率: 0.7140
  距离: 8.0391
  内存使用: -0.0624
  能量使用: 0.2541
  推理时间: 0.7989秒

批次 51:
  奖励值: 29.2984
  收益率: 0.7217
  距离: 7.5954
  内存使用: -0.0503
  能量使用: 0.2581
  推理时间: 0.8082秒

批次 52:
  奖励值: 27.7799
  收益率: 0.6753
  距离: 6.6693
  内存使用: -0.0389
  能量使用: 0.2097
  推理时间: 0.7517秒

批次 53:
  奖励值: 28.3746
  收益率: 0.6743
  距离: 7.6537
  内存使用: -0.0481
  能量使用: 0.2453
  推理时间: 0.7540秒

批次 54:
  奖励值: 26.7380
  收益率: 0.6594
  距离: 6.7841
  内存使用: -0.0730
  能量使用: 0.2129
  推理时间: 0.7307秒

批次 55:
  奖励值: 28.0905
  收益率: 0.6793
  距离: 8.0217
  内存使用: -0.0437
  能量使用: 0.2410
  推理时间: 0.7903秒

批次 56:
  奖励值: 25.5720
  收益率: 0.6319
  距离: 5.7478
  内存使用: -0.0881
  能量使用: 0.1963
  推理时间: 0.6867秒

批次 57:
  奖励值: 25.9081
  收益率: 0.6896
  距离: 10.1825
  内存使用: -0.0454
  能量使用: 0.2468
  推理时间: 0.7924秒

批次 58:
  奖励值: 27.2791
  收益率: 0.7060
  距离: 8.6501
  内存使用: -0.0649
  能量使用: 0.2567
  推理时间: 0.7680秒

批次 59:
  奖励值: 32.6112
  收益率: 0.7312
  距离: 8.5789
  内存使用: -0.0520
  能量使用: 0.2506
  推理时间: 0.8292秒

批次 60:
  奖励值: 26.6484
  收益率: 0.6587
  距离: 7.5436
  内存使用: -0.0617
  能量使用: 0.2212
  推理时间: 0.7215秒

批次 61:
  奖励值: 26.4078
  收益率: 0.6616
  距离: 7.6110
  内存使用: -0.0696
  能量使用: 0.2429
  推理时间: 0.6832秒

批次 62:
  奖励值: 24.6225
  收益率: 0.6387
  距离: 5.4784
  内存使用: -0.1173
  能量使用: 0.1939
  推理时间: 0.6417秒

批次 63:
  奖励值: 27.0877
  收益率: 0.7014
  距离: 7.6140
  内存使用: -0.0342
  能量使用: 0.2448
  推理时间: 0.7519秒

批次 64:
  奖励值: 23.4528
  收益率: 0.6159
  距离: 5.5677
  内存使用: -0.0872
  能量使用: 0.2139
  推理时间: 0.6400秒

批次 65:
  奖励值: 30.1747
  收益率: 0.7055
  距离: 8.5135
  内存使用: -0.0790
  能量使用: 0.2162
  推理时间: 0.8310秒

批次 66:
  奖励值: 27.6847
  收益率: 0.6857
  距离: 9.4474
  内存使用: -0.0643
  能量使用: 0.2475
  推理时间: 0.7187秒

批次 67:
  奖励值: 29.1859
  收益率: 0.7032
  距离: 6.8031
  内存使用: -0.0411
  能量使用: 0.2662
  推理时间: 0.6749秒

批次 68:
  奖励值: 26.5068
  收益率: 0.6629
  距离: 6.2501
  内存使用: -0.0898
  能量使用: 0.2260
  推理时间: 0.5840秒

批次 69:
  奖励值: 27.5902
  收益率: 0.6776
  距离: 6.4924
  内存使用: -0.0848
  能量使用: 0.2120
  推理时间: 0.6637秒

批次 70:
  奖励值: 31.2303
  收益率: 0.7407
  距离: 9.3340
  内存使用: -0.0560
  能量使用: 0.2614
  推理时间: 0.7594秒

批次 71:
  奖励值: 28.3604
  收益率: 0.6910
  距离: 7.5336
  内存使用: -0.0836
  能量使用: 0.2291
  推理时间: 0.6618秒

批次 72:
  奖励值: 25.8258
  收益率: 0.6498
  距离: 7.2260
  内存使用: -0.1029
  能量使用: 0.2253
  推理时间: 0.6767秒

批次 73:
  奖励值: 32.4094
  收益率: 0.7336
  距离: 8.7199
  内存使用: -0.0385
  能量使用: 0.2999
  推理时间: 0.9416秒

批次 74:
  奖励值: 31.9282
  收益率: 0.7254
  距离: 6.7257
  内存使用: -0.0588
  能量使用: 0.2768
  推理时间: 0.9733秒

批次 75:
  奖励值: 28.9106
  收益率: 0.7133
  距离: 7.1714
  内存使用: -0.0406
  能量使用: 0.2480
  推理时间: 0.6375秒

批次 76:
  奖励值: 26.3043
  收益率: 0.7067
  距离: 8.4160
  内存使用: -0.0730
  能量使用: 0.2485
  推理时间: 0.7420秒

批次 77:
  奖励值: 29.9893
  收益率: 0.6748
  距离: 5.8642
  内存使用: -0.0703
  能量使用: 0.2276
  推理时间: 0.6069秒

批次 78:
  奖励值: 27.8222
  收益率: 0.6960
  距离: 7.4244
  内存使用: -0.1007
  能量使用: 0.2320
  推理时间: 0.6569秒

批次 79:
  奖励值: 24.8943
  收益率: 0.6768
  距离: 7.8854
  内存使用: -0.0754
  能量使用: 0.2014
  推理时间: 0.5895秒

批次 80:
  奖励值: 27.1184
  收益率: 0.7044
  距离: 8.9695
  内存使用: -0.0418
  能量使用: 0.2356
  推理时间: 0.6671秒

批次 81:
  奖励值: 21.8366
  收益率: 0.6234
  距离: 6.2400
  内存使用: -0.1098
  能量使用: 0.1967
  推理时间: 0.5245秒

批次 82:
  奖励值: 31.3761
  收益率: 0.7065
  距离: 6.7837
  内存使用: -0.0028
  能量使用: 0.2670
  推理时间: 0.6753秒

批次 83:
  奖励值: 30.2565
  收益率: 0.6848
  距离: 7.0496
  内存使用: -0.0456
  能量使用: 0.2831
  推理时间: 0.6298秒

批次 84:
  奖励值: 28.3898
  收益率: 0.6841
  距离: 8.4709
  内存使用: -0.0629
  能量使用: 0.2124
  推理时间: 0.6099秒

批次 85:
  奖励值: 29.1060
  收益率: 0.6883
  距离: 7.4380
  内存使用: -0.0849
  能量使用: 0.2349
  推理时间: 0.6221秒

批次 86:
  奖励值: 25.9077
  收益率: 0.6527
  距离: 7.0355
  内存使用: -0.0619
  能量使用: 0.2110
  推理时间: 0.6284秒

批次 87:
  奖励值: 26.7462
  收益率: 0.6807
  距离: 7.7120
  内存使用: -0.0735
  能量使用: 0.2692
  推理时间: 0.6795秒

批次 88:
  奖励值: 31.5901
  收益率: 0.7259
  距离: 6.0655
  内存使用: -0.0518
  能量使用: 0.2062
  推理时间: 0.7932秒

批次 89:
  奖励值: 25.2872
  收益率: 0.6540
  距离: 7.9215
  内存使用: -0.0765
  能量使用: 0.1806
  推理时间: 0.6392秒

批次 90:
  奖励值: 26.8524
  收益率: 0.6972
  距离: 6.8838
  内存使用: -0.0455
  能量使用: 0.2568
  推理时间: 0.7645秒

批次 91:
  奖励值: 27.9618
  收益率: 0.7078
  距离: 9.2662
  内存使用: -0.0515
  能量使用: 0.2619
  推理时间: 0.7981秒

批次 92:
  奖励值: 31.0269
  收益率: 0.7207
  距离: 7.9474
  内存使用: -0.0286
  能量使用: 0.2273
  推理时间: 0.8103秒

批次 93:
  奖励值: 33.5420
  收益率: 0.7445
  距离: 7.2585
  内存使用: -0.0162
  能量使用: 0.2736
  推理时间: 0.8159秒

批次 94:
  奖励值: 24.6142
  收益率: 0.6233
  距离: 6.2462
  内存使用: -0.0849
  能量使用: 0.2103
  推理时间: 0.6968秒

批次 95:
  奖励值: 29.5872
  收益率: 0.6933
  距离: 8.4551
  内存使用: -0.0522
  能量使用: 0.2226
  推理时间: 0.8091秒

批次 96:
  奖励值: 28.4830
  收益率: 0.7004
  距离: 7.9144
  内存使用: -0.0847
  能量使用: 0.2044
  推理时间: 0.7112秒

批次 97:
  奖励值: 25.4493
  收益率: 0.6368
  距离: 7.1432
  内存使用: -0.0670
  能量使用: 0.2251
  推理时间: 0.6464秒

批次 98:
  奖励值: 31.6560
  收益率: 0.7060
  距离: 7.0671
  内存使用: -0.0637
  能量使用: 0.2845
  推理时间: 0.7748秒

批次 99:
  奖励值: 24.3899
  收益率: 0.6612
  距离: 7.2753
  内存使用: -0.0870
  能量使用: 0.2318
  推理时间: 0.6423秒

批次 100:
  奖励值: 30.6168
  收益率: 0.7175
  距离: 9.2479
  内存使用: -0.1005
  能量使用: 0.2191
  推理时间: 0.7197秒


==================== 总结 ====================
平均收益率: 0.6874
平均能量使用: 0.2385
平均推理时间: 0.7151秒
