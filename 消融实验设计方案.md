# 敏捷观察卫星星座任务规划项目消融实验设计方案

## 📋 实验概述

本消融实验方案旨在系统性地验证项目中各个创新组件的有效性，通过逐步移除或替换关键模块，量化每个创新点对整体性能的贡献。实验设计遵循控制变量原则，确保结果的科学性和可信度。

## 🎯 实验目标

1. **验证创新组件有效性**: 量化每个创新模块的性能贡献
2. **对比不同配置**: 研究不同创新点的独立效果
3. **优化模型配置**: 找到最优的组件组合方案
4. **支撑学术发表**: 提供充分的实验证据支持论文发表

## 🧪 消融实验设计

### 实验1: 在轨充电功能消融 🔋

**实验目的**: 验证在轨充电机制对任务规划性能的影响

**实验组设置**:
- **Baseline**: 无充电功能 (`ORBITAL_CHARGING_RATE = 0`)
- **Proposed**: 有充电功能 (`ORBITAL_CHARGING_RATE = 0.005`)
- **Sensitivity**: 不同充电速率 (`0.001, 0.003, 0.005, 0.007, 0.01`)

**评估指标**:
- 任务完成率 (Task Completion Rate)
- 平均收益率 (Average Revenue Rate)
- 能量利用效率 (Energy Utilization Efficiency)
- 任务执行时长 (Mission Duration)

**实验配置**:
```python
# 充电功能消融实验配置
charging_rates = [0, 0.001, 0.003, 0.005, 0.007, 0.01]
test_scenarios = ['low_power', 'medium_power', 'high_power']
num_satellites = [2, 3, 5]
num_nodes = [50, 100, 200]
```

### 实验2: 架构创新消融 🏗️

#### 2.1 单星vs星座模型对比
**实验目的**: 量化从单星扩展到星座的性能提升

**实验组设置**:
- **Single Satellite**: 使用原始GPN4SMP单星模型
- **Multi Satellite (Independent)**: 多个独立的单星模型
- **Constellation Model**: 完整的星座协同模型

**对比维度**:
- 任务分配效率
- 资源利用率
- 计算复杂度
- 可扩展性

#### 2.2 Transformer架构消融
**实验目的**: 验证Transformer增强模块的有效性

**实验组设置**:
- **Baseline**: GPN + IndRNN (无Transformer)
- **Transformer Only**: 仅使用Transformer替换IndRNN
- **Hybrid**: GPN + IndRNN + Transformer (完整模型)
- **Transformer Variants**: 不同Transformer配置

**Transformer配置消融**:
```python
transformer_configs = [
    {'layers': 1, 'heads': 2, 'd_model': 128},
    {'layers': 2, 'heads': 4, 'd_model': 256},  # 默认配置
    {'layers': 3, 'heads': 8, 'd_model': 512},
    {'layers': 4, 'heads': 8, 'd_model': 256}
]
```

### 实验3: 星座协同机制消融 🛰️

#### 3.1 星座工作模式消融
**实验目的**: 对比三种星座工作模式的性能差异

**实验组设置**:
- **Competitive Mode**: 无信息交互
- **Cooperative Mode**: 完全信息共享
- **Hybrid Mode**: 门控信息交互
- **Ablated Hybrid**: 移除门控机制的混合模式

**测试场景**:
- 通信良好场景 (Communication Delay = 0.001)
- 通信受限场景 (Communication Delay = 0.05)
- 通信中断场景 (Communication Failure Rate = 0.1)

#### 3.2 卫星间注意力机制消融
**实验目的**: 验证卫星间注意力机制的重要性

**实验组设置**:
- **No Attention**: 无卫星间注意力
- **Simple Attention**: 简单平均池化
- **Multi-Head Attention**: 多头注意力机制
- **Gated Attention**: 门控注意力机制



## 📊 实验设置与配置

### 基础实验配置
```python
# 通用实验参数
EXPERIMENT_CONFIG = {
    'num_runs': 5,  # 每个配置运行5次取平均
    'epochs': 20,   # 训练轮数
    'batch_size': 64,
    'learning_rate': 1e-4,
    'seed_range': [12345, 12346, 12347, 12348, 12349],
    
    # 问题规模
    'node_sizes': [50, 100, 200],
    'satellite_counts': [2, 3, 5],
    'memory_total': 0.3,
    'power_total': 5.0,
    
    # 评估指标
    'metrics': [
        'reward', 'revenue_rate', 'distance', 
        'memory_usage', 'power_consumption',
        'training_time', 'inference_time',
        'model_parameters'
    ]
}
```

### 数据集配置
```python
# 测试数据集
DATASET_CONFIG = {
    'train_size': 50000,
    'valid_size': 5000,
    'test_size': 10000,
    
    # 场景多样性
    'scenarios': {
        'easy': {'task_density': 0.3, 'time_pressure': 'low'},
        'medium': {'task_density': 0.5, 'time_pressure': 'medium'},
        'hard': {'task_density': 0.8, 'time_pressure': 'high'}
    }
}
```

## 🔬 实验执行计划

### 阶段1: 核心组件消融 (2周)
- 周1: 在轨充电功能消融实验
- 周2: 架构创新消融实验

### 阶段2: 协同机制消融 (2周)
- 周3: 星座工作模式消融
- 周4: 注意力机制消融

### 阶段3: 结果分析与报告 (1周)
- 周5: 综合结果分析与实验报告撰写

## 📈 预期结果与分析

### 性能提升预期
1. **充电功能**: 预期提升能量利用效率15-25%
2. **星座扩展**: 预期提升任务完成率20-35%
3. **Transformer**: 预期提升收益率10-20%
4. **协同机制**: 预期提升整体性能15-30%

### 统计显著性检验
- 使用t检验验证性能差异显著性
- 设置显著性水平α = 0.05
- 计算效应大小(Effect Size)
- 进行多重比较校正

## 📋 实验结果记录模板

### 结果记录表格
```
| 实验组 | 配置 | 奖励 | 收益率 | 距离 | 内存 | 功耗 | 训练时间 | 参数量 |
|--------|------|------|--------|------|------|------|----------|--------|
| E1     | ... | ... | ... | ... | ... | ... | ... | ... |
```

### 可视化要求
1. **性能对比柱状图**: 各组件贡献对比
2. **训练曲线图**: 收敛性分析
3. **热力图**: 组件交互效应
4. **雷达图**: 多维度性能对比

## 🎯 实验成功标准

1. **统计显著性**: 所有关键改进都达到统计显著性
2. **实用意义**: 性能提升具有实际应用价值
3. **一致性**: 结果在不同场景下保持一致
4. **可重现性**: 实验结果可重现

## 🛠️ 实验实施细节

### 代码实现建议

#### 消融实验控制器
```python
class AblationController:
    def __init__(self, base_config):
        self.base_config = base_config
        self.results = {}

    def run_ablation_study(self, ablation_configs):
        """运行完整的消融实验"""
        for config_name, config in ablation_configs.items():
            print(f"Running ablation: {config_name}")
            results = self.run_single_experiment(config)
            self.results[config_name] = results
            self.save_intermediate_results(config_name, results)

    def run_single_experiment(self, config):
        """运行单个实验配置"""
        metrics = []
        for seed in config['seeds']:
            # 设置随机种子
            torch.manual_seed(seed)
            np.random.seed(seed)

            # 创建模型
            model = self.create_model(config)

            # 训练和评估
            result = self.train_and_evaluate(model, config)
            metrics.append(result)

        return self.aggregate_results(metrics)
```

#### 模型配置生成器
```python
def generate_ablation_configs():
    """生成所有消融实验配置"""
    configs = {}

    # 基础配置
    base = {
        'use_charging': True,
        'use_transformer': True,
        'constellation_mode': 'cooperative',
        'use_attention': True
    }

    # 单组件消融
    configs['no_charging'] = {**base, 'use_charging': False}
    configs['no_transformer'] = {**base, 'use_transformer': False}
    configs['competitive_mode'] = {**base, 'constellation_mode': 'competitive'}
    configs['no_attention'] = {**base, 'use_attention': False}

    # 最小配置（所有创新点都关闭）
    configs['minimal'] = {
        'use_charging': False,
        'use_transformer': False,
        'constellation_mode': 'competitive',
        'use_attention': False
    }

    return configs
```

### 实验监控与日志

#### 实验进度监控
```python
class ExperimentMonitor:
    def __init__(self, experiment_name):
        self.experiment_name = experiment_name
        self.start_time = time.time()
        self.logger = self.setup_logger()

    def log_experiment_start(self, config):
        self.logger.info(f"Starting experiment: {config}")

    def log_epoch_progress(self, epoch, metrics):
        self.logger.info(f"Epoch {epoch}: {metrics}")

    def log_experiment_end(self, results):
        duration = time.time() - self.start_time
        self.logger.info(f"Experiment completed in {duration:.2f}s")
        self.logger.info(f"Final results: {results}")
```

### 结果分析工具

#### 统计分析
```python
def statistical_analysis(results_dict):
    """进行统计显著性分析"""
    import scipy.stats as stats

    analysis_results = {}
    baseline = results_dict['baseline']

    for exp_name, results in results_dict.items():
        if exp_name == 'baseline':
            continue

        # t检验
        t_stat, p_value = stats.ttest_ind(baseline, results)

        # 效应大小 (Cohen's d)
        pooled_std = np.sqrt(((len(baseline)-1)*np.var(baseline) +
                             (len(results)-1)*np.var(results)) /
                            (len(baseline)+len(results)-2))
        cohens_d = (np.mean(results) - np.mean(baseline)) / pooled_std

        analysis_results[exp_name] = {
            't_statistic': t_stat,
            'p_value': p_value,
            'cohens_d': cohens_d,
            'significant': p_value < 0.05
        }

    return analysis_results
```

## 📊 实验结果可视化

### 可视化代码模板
```python
def create_ablation_plots(results_dict):
    """创建消融实验可视化图表"""

    # 1. 性能对比柱状图
    plt.figure(figsize=(12, 8))

    experiments = list(results_dict.keys())
    rewards = [np.mean(results_dict[exp]['reward']) for exp in experiments]
    errors = [np.std(results_dict[exp]['reward']) for exp in experiments]

    plt.bar(experiments, rewards, yerr=errors, capsize=5)
    plt.title('Ablation Study: Performance Comparison')
    plt.ylabel('Average Reward')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('ablation_performance_comparison.png', dpi=300)

    # 2. 组件贡献分析
    plt.figure(figsize=(10, 6))

    baseline_reward = np.mean(results_dict['baseline']['reward'])
    contributions = {}

    for exp in experiments:
        if exp != 'baseline':
            contrib = np.mean(results_dict[exp]['reward']) - baseline_reward
            contributions[exp] = contrib

    plt.bar(contributions.keys(), contributions.values())
    plt.title('Component Contribution Analysis')
    plt.ylabel('Performance Improvement')
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('component_contributions.png', dpi=300)
```

## 📋 实验检查清单

### 实验前检查
- [ ] 实验环境配置完成
- [ ] 数据集准备就绪
- [ ] 基线模型验证通过
- [ ] 随机种子设置正确
- [ ] 日志系统配置完成

### 实验中监控
- [ ] 训练收敛性检查
- [ ] 内存使用监控
- [ ] 中间结果保存
- [ ] 异常情况记录

### 实验后分析
- [ ] 结果统计显著性检验
- [ ] 可视化图表生成
- [ ] 实验报告撰写
- [ ] 代码和数据备份

## 🎯 预期挑战与解决方案

### 潜在挑战
1. **计算资源限制**: 大量实验需要长时间计算
2. **结果方差较大**: 强化学习结果可能不稳定
3. **超参数敏感性**: 不同配置可能需要不同超参数

### 解决方案
1. **并行化实验**: 使用多GPU并行训练
2. **增加重复次数**: 每个配置运行更多次实验
3. **超参数网格搜索**: 为每个配置找到最优超参数

---
*实验方案设计时间: 2025-08-21*
*预计实验周期: 5周*
*预计资源需求: GPU计算资源约150小时*
*实验复杂度: 中等*
*预期论文支撑: 充分*
