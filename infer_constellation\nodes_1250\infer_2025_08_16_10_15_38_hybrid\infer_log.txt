推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 135.2803
  收益率: 0.2759
  距离: 32.1980
  内存使用: 0.6118
  能量使用: 1.0295
  推理时间: 2.4513秒

批次 2:
  奖励值: 128.7213
  收益率: 0.2564
  距离: 32.6951
  内存使用: 0.6334
  能量使用: 1.0288
  推理时间: 2.3819秒

批次 3:
  奖励值: 124.0826
  收益率: 0.2485
  距离: 32.0598
  内存使用: 0.6511
  能量使用: 0.9239
  推理时间: 2.2918秒

批次 4:
  奖励值: 139.3375
  收益率: 0.2778
  距离: 33.9594
  内存使用: 0.6725
  能量使用: 1.0931
  推理时间: 2.8278秒

批次 5:
  奖励值: 128.8869
  收益率: 0.2522
  距离: 29.5424
  内存使用: 0.5947
  能量使用: 0.8890
  推理时间: 2.2767秒

批次 6:
  奖励值: 122.8260
  收益率: 0.2415
  距离: 29.1282
  内存使用: 0.5584
  能量使用: 0.8614
  推理时间: 2.2027秒

批次 7:
  奖励值: 133.5969
  收益率: 0.2682
  距离: 33.6864
  内存使用: 0.7222
  能量使用: 0.9976
  推理时间: 2.4000秒

批次 8:
  奖励值: 130.3722
  收益率: 0.2577
  距离: 29.0337
  内存使用: 0.6676
  能量使用: 0.9542
  推理时间: 2.3957秒

批次 9:
  奖励值: 143.7224
  收益率: 0.2853
  距离: 33.9699
  内存使用: 0.7234
  能量使用: 1.0619
  推理时间: 2.7738秒

批次 10:
  奖励值: 124.2886
  收益率: 0.2466
  距离: 29.1280
  内存使用: 0.5619
  能量使用: 0.9328
  推理时间: 2.1954秒

批次 11:
  奖励值: 131.5302
  收益率: 0.2631
  距离: 30.6277
  内存使用: 0.6937
  能量使用: 1.0180
  推理时间: 2.4083秒

批次 12:
  奖励值: 124.4873
  收益率: 0.2432
  距离: 31.1003
  内存使用: 0.5923
  能量使用: 0.9328
  推理时间: 2.4680秒

批次 13:
  奖励值: 122.0956
  收益率: 0.2445
  距离: 27.7302
  内存使用: 0.5928
  能量使用: 0.8856
  推理时间: 2.3652秒

批次 14:
  奖励值: 122.0067
  收益率: 0.2412
  距离: 30.0513
  内存使用: 0.6299
  能量使用: 0.9053
  推理时间: 2.2664秒

批次 15:
  奖励值: 132.8273
  收益率: 0.2656
  距离: 32.8354
  内存使用: 0.7347
  能量使用: 0.9752
  推理时间: 2.4054秒

批次 16:
  奖励值: 119.8683
  收益率: 0.2421
  距离: 30.1437
  内存使用: 0.5775
  能量使用: 0.9421
  推理时间: 2.1701秒

批次 17:
  奖励值: 119.5614
  收益率: 0.2430
  距离: 30.0218
  内存使用: 0.5705
  能量使用: 0.8849
  推理时间: 2.3585秒

批次 18:
  奖励值: 128.7189
  收益率: 0.2531
  距离: 30.4040
  内存使用: 0.6920
  能量使用: 1.0662
  推理时间: 2.5896秒

批次 19:
  奖励值: 135.9106
  收益率: 0.2621
  距离: 33.0899
  内存使用: 0.6898
  能量使用: 0.9991
  推理时间: 2.6660秒

批次 20:
  奖励值: 118.4578
  收益率: 0.2338
  距离: 28.6355
  内存使用: 0.5093
  能量使用: 0.9397
  推理时间: 2.1672秒

批次 21:
  奖励值: 121.3720
  收益率: 0.2403
  距离: 26.4930
  内存使用: 0.5999
  能量使用: 0.9631
  推理时间: 2.1584秒

批次 22:
  奖励值: 134.7164
  收益率: 0.2617
  距离: 30.4957
  内存使用: 0.6785
  能量使用: 0.9929
  推理时间: 2.7271秒

批次 23:
  奖励值: 131.1917
  收益率: 0.2623
  距离: 30.6999
  内存使用: 0.7161
  能量使用: 1.0006
  推理时间: 2.6090秒

批次 24:
  奖励值: 118.5034
  收益率: 0.2332
  距离: 26.7522
  内存使用: 0.5312
  能量使用: 0.9098
  推理时间: 2.1215秒

批次 25:
  奖励值: 123.0419
  收益率: 0.2461
  距离: 29.6173
  内存使用: 0.6007
  能量使用: 0.8981
  推理时间: 2.2480秒

批次 26:
  奖励值: 126.3274
  收益率: 0.2522
  距离: 29.6834
  内存使用: 0.6892
  能量使用: 0.9726
  推理时间: 2.3147秒

批次 27:
  奖励值: 113.5597
  收益率: 0.2284
  距离: 28.8786
  内存使用: 0.5843
  能量使用: 0.8176
  推理时间: 2.1102秒

批次 28:
  奖励值: 128.7518
  收益率: 0.2495
  距离: 30.4466
  内存使用: 0.6016
  能量使用: 0.8481
  推理时间: 2.2853秒

批次 29:
  奖励值: 139.3584
  收益率: 0.2749
  距离: 31.1984
  内存使用: 0.6904
  能量使用: 0.9943
  推理时间: 2.4889秒

批次 30:
  奖励值: 126.0058
  收益率: 0.2482
  距离: 28.4832
  内存使用: 0.6643
  能量使用: 1.0408
  推理时间: 2.3498秒

批次 31:
  奖励值: 131.8816
  收益率: 0.2676
  距离: 31.1382
  内存使用: 0.5910
  能量使用: 0.9076
  推理时间: 2.3650秒

批次 32:
  奖励值: 114.5918
  收益率: 0.2257
  距离: 25.7321
  内存使用: 0.6046
  能量使用: 0.8797
  推理时间: 2.2753秒

批次 33:
  奖励值: 128.7013
  收益率: 0.2560
  距离: 32.6163
  内存使用: 0.6458
  能量使用: 0.9352
  推理时间: 2.2976秒

批次 34:
  奖励值: 133.8848
  收益率: 0.2675
  距离: 29.9273
  内存使用: 0.6744
  能量使用: 1.0287
  推理时间: 2.3756秒

批次 35:
  奖励值: 136.9214
  收益率: 0.2698
  距离: 29.8877
  内存使用: 0.6824
  能量使用: 0.9872
  推理时间: 2.6270秒

批次 36:
  奖励值: 126.9387
  收益率: 0.2583
  距离: 31.2373
  内存使用: 0.6819
  能量使用: 1.0034
  推理时间: 2.3274秒

批次 37:
  奖励值: 125.6930
  收益率: 0.2492
  距离: 28.7080
  内存使用: 0.5943
  能量使用: 0.9117
  推理时间: 2.2190秒

批次 38:
  奖励值: 134.6527
  收益率: 0.2671
  距离: 35.8122
  内存使用: 0.6510
  能量使用: 1.0314
  推理时间: 2.4300秒

批次 39:
  奖励值: 129.0610
  收益率: 0.2556
  距离: 29.6179
  内存使用: 0.5841
  能量使用: 0.8758
  推理时间: 2.4846秒

批次 40:
  奖励值: 125.2884
  收益率: 0.2528
  距离: 32.4546
  内存使用: 0.6523
  能量使用: 0.9164
  推理时间: 2.3037秒

批次 41:
  奖励值: 118.2673
  收益率: 0.2352
  距离: 27.1241
  内存使用: 0.5098
  能量使用: 0.8315
  推理时间: 2.1454秒

批次 42:
  奖励值: 116.5154
  收益率: 0.2316
  距离: 31.1511
  内存使用: 0.5786
  能量使用: 0.8395
  推理时间: 2.1435秒

批次 43:
  奖励值: 125.6670
  收益率: 0.2533
  距离: 31.8426
  内存使用: 0.6628
  能量使用: 0.9605
  推理时间: 2.3060秒

批次 44:
  奖励值: 124.3697
  收益率: 0.2499
  距离: 32.1666
  内存使用: 0.5782
  能量使用: 0.9158
  推理时间: 2.3066秒

批次 45:
  奖励值: 122.7268
  收益率: 0.2501
  距离: 31.9762
  内存使用: 0.5997
  能量使用: 0.9705
  推理时间: 2.2376秒

批次 46:
  奖励值: 119.6736
  收益率: 0.2410
  距离: 29.5315
  内存使用: 0.5735
  能量使用: 0.9478
  推理时间: 2.4333秒

批次 47:
  奖励值: 123.6898
  收益率: 0.2411
  距离: 26.8953
  内存使用: 0.6317
  能量使用: 0.8933
  推理时间: 2.3778秒

批次 48:
  奖励值: 124.5652
  收益率: 0.2503
  距离: 28.4070
  内存使用: 0.5753
  能量使用: 0.8871
  推理时间: 2.2850秒

批次 49:
  奖励值: 141.8930
  收益率: 0.2861
  距离: 36.2393
  内存使用: 0.7070
  能量使用: 1.0678
  推理时间: 2.5910秒

批次 50:
  奖励值: 122.8547
  收益率: 0.2534
  距离: 34.4908
  内存使用: 0.6394
  能量使用: 0.9604
  推理时间: 2.3128秒

批次 51:
  奖励值: 137.9417
  收益率: 0.2802
  距离: 38.1171
  内存使用: 0.7192
  能量使用: 1.0802
  推理时间: 2.5804秒

批次 52:
  奖励值: 130.0800
  收益率: 0.2625
  距离: 32.2283
  内存使用: 0.6415
  能量使用: 0.9702
  推理时间: 2.6480秒

批次 53:
  奖励值: 123.9599
  收益率: 0.2477
  距离: 31.4212
  内存使用: 0.7233
  能量使用: 0.9357
  推理时间: 2.4479秒

批次 54:
  奖励值: 115.2941
  收益率: 0.2408
  距离: 31.9937
  内存使用: 0.5429
  能量使用: 0.8752
  推理时间: 2.0595秒

批次 55:
  奖励值: 123.8716
  收益率: 0.2468
  距离: 30.3756
  内存使用: 0.5982
  能量使用: 0.8831
  推理时间: 2.2814秒

批次 56:
  奖励值: 125.3666
  收益率: 0.2521
  距离: 31.9880
  内存使用: 0.6256
  能量使用: 0.8532
  推理时间: 2.2694秒

批次 57:
  奖励值: 121.3467
  收益率: 0.2381
  距离: 27.7749
  内存使用: 0.5783
  能量使用: 0.9533
  推理时间: 2.2506秒

批次 58:
  奖励值: 132.0979
  收益率: 0.2667
  距离: 30.6043
  内存使用: 0.6053
  能量使用: 0.9945
  推理时间: 2.3524秒

批次 59:
  奖励值: 122.5239
  收益率: 0.2398
  距离: 31.7927
  内存使用: 0.5841
  能量使用: 0.9753
  推理时间: 2.4186秒

批次 60:
  奖励值: 125.1901
  收益率: 0.2577
  距离: 31.1813
  内存使用: 0.6588
  能量使用: 0.9797
  推理时间: 2.3374秒

批次 61:
  奖励值: 126.5619
  收益率: 0.2450
  距离: 30.4223
  内存使用: 0.5882
  能量使用: 0.8656
  推理时间: 2.2515秒

批次 62:
  奖励值: 131.6019
  收益率: 0.2594
  距离: 28.8550
  内存使用: 0.6659
  能量使用: 0.8753
  推理时间: 2.3037秒

批次 63:
  奖励值: 129.1131
  收益率: 0.2600
  距离: 33.1791
  内存使用: 0.6790
  能量使用: 0.9156
  推理时间: 2.3394秒

批次 64:
  奖励值: 121.0329
  收益率: 0.2389
  距离: 30.7181
  内存使用: 0.5583
  能量使用: 0.8577
  推理时间: 2.1858秒

批次 65:
  奖励值: 127.0114
  收益率: 0.2495
  距离: 28.8103
  内存使用: 0.6322
  能量使用: 0.9346
  推理时间: 2.3136秒

批次 66:
  奖励值: 116.8320
  收益率: 0.2400
  距离: 30.3152
  内存使用: 0.5357
  能量使用: 0.8852
  推理时间: 2.1746秒

批次 67:
  奖励值: 113.5151
  收益率: 0.2238
  距离: 26.4759
  内存使用: 0.4894
  能量使用: 0.8045
  推理时间: 2.4904秒

批次 68:
  奖励值: 127.6224
  收益率: 0.2622
  距离: 31.1127
  内存使用: 0.6556
  能量使用: 0.9868
  推理时间: 2.3397秒

批次 69:
  奖励值: 128.7185
  收益率: 0.2581
  距离: 30.5428
  内存使用: 0.6249
  能量使用: 0.9952
  推理时间: 2.3092秒

批次 70:
  奖励值: 114.4300
  收益率: 0.2233
  距离: 29.2150
  内存使用: 0.5627
  能量使用: 0.7622
  推理时间: 2.0962秒

批次 71:
  奖励值: 132.1053
  收益率: 0.2665
  距离: 32.2395
  内存使用: 0.6108
  能量使用: 0.9391
  推理时间: 2.3990秒

批次 72:
  奖励值: 116.0216
  收益率: 0.2308
  距离: 27.3046
  内存使用: 0.5942
  能量使用: 0.8580
  推理时间: 2.0770秒

批次 73:
  奖励值: 119.9252
  收益率: 0.2369
  距离: 29.4908
  内存使用: 0.6050
  能量使用: 0.8537
  推理时间: 2.3178秒

批次 74:
  奖励值: 126.0249
  收益率: 0.2564
  距离: 33.6054
  内存使用: 0.5946
  能量使用: 0.9503
  推理时间: 2.3089秒

批次 75:
  奖励值: 133.9294
  收益率: 0.2638
  距离: 31.6346
  内存使用: 0.7084
  能量使用: 0.9980
  推理时间: 2.4344秒

批次 76:
  奖励值: 122.3254
  收益率: 0.2454
  距离: 29.6468
  内存使用: 0.6004
  能量使用: 0.9020
  推理时间: 2.2394秒

批次 77:
  奖励值: 111.5760
  收益率: 0.2304
  距离: 29.6370
  内存使用: 0.5569
  能量使用: 0.8542
  推理时间: 2.2128秒

批次 78:
  奖励值: 124.4743
  收益率: 0.2475
  距离: 28.3062
  内存使用: 0.6002
  能量使用: 0.9448
  推理时间: 2.2970秒

批次 79:
  奖励值: 123.7451
  收益率: 0.2465
  距离: 32.5501
  内存使用: 0.6129
  能量使用: 0.9469
  推理时间: 2.5014秒

批次 80:
  奖励值: 113.6040
  收益率: 0.2268
  距离: 26.3597
  内存使用: 0.5408
  能量使用: 0.8915
  推理时间: 2.2442秒

批次 81:
  奖励值: 125.3174
  收益率: 0.2504
  距离: 26.5578
  内存使用: 0.6197
  能量使用: 0.9877
  推理时间: 2.2260秒

批次 82:
  奖励值: 125.5599
  收益率: 0.2447
  距离: 30.1529
  内存使用: 0.6104
  能量使用: 0.9169
  推理时间: 2.2614秒

批次 83:
  奖励值: 130.3803
  收益率: 0.2594
  距离: 31.0553
  内存使用: 0.6323
  能量使用: 0.9517
  推理时间: 2.3492秒

批次 84:
  奖励值: 136.1607
  收益率: 0.2771
  距离: 33.3116
  内存使用: 0.7101
  能量使用: 1.0569
  推理时间: 2.4835秒

批次 85:
  奖励值: 131.2184
  收益率: 0.2658
  距离: 32.1343
  内存使用: 0.6369
  能量使用: 0.9919
  推理时间: 2.4107秒

批次 86:
  奖励值: 129.0349
  收益率: 0.2593
  距离: 33.9844
  内存使用: 0.6586
  能量使用: 0.8873
  推理时间: 2.5463秒

批次 87:
  奖励值: 124.6241
  收益率: 0.2544
  距离: 32.4841
  内存使用: 0.5993
  能量使用: 1.0684
  推理时间: 2.3040秒

批次 88:
  奖励值: 130.3652
  收益率: 0.2651
  距离: 28.4400
  内存使用: 0.6411
  能量使用: 0.9885
  推理时间: 2.5913秒

批次 89:
  奖励值: 126.5295
  收益率: 0.2488
  距离: 28.4756
  内存使用: 0.5546
  能量使用: 0.9107
  推理时间: 2.2752秒

批次 90:
  奖励值: 130.1950
  收益率: 0.2588
  距离: 30.9052
  内存使用: 0.6439
  能量使用: 1.0608
  推理时间: 2.5686秒

批次 91:
  奖励值: 119.9580
  收益率: 0.2312
  距离: 25.0784
  内存使用: 0.5326
  能量使用: 0.8729
  推理时间: 2.1996秒

批次 92:
  奖励值: 130.6266
  收益率: 0.2600
  距离: 27.7197
  内存使用: 0.6532
  能量使用: 0.9739
  推理时间: 2.3552秒

批次 93:
  奖励值: 110.9312
  收益率: 0.2191
  距离: 24.5668
  内存使用: 0.5595
  能量使用: 0.8113
  推理时间: 2.1529秒

批次 94:
  奖励值: 123.5505
  收益率: 0.2446
  距离: 27.2681
  内存使用: 0.6021
  能量使用: 0.8287
  推理时间: 2.4087秒

批次 95:
  奖励值: 127.4210
  收益率: 0.2472
  距离: 32.1779
  内存使用: 0.6226
  能量使用: 0.9574
  推理时间: 2.3749秒

批次 96:
  奖励值: 132.8657
  收益率: 0.2595
  距离: 30.8212
  内存使用: 0.6730
  能量使用: 0.9744
  推理时间: 2.3829秒

批次 97:
  奖励值: 124.1874
  收益率: 0.2534
  距离: 32.4851
  内存使用: 0.6273
  能量使用: 0.9128
  推理时间: 2.2374秒

批次 98:
  奖励值: 123.5841
  收益率: 0.2428
  距离: 30.3701
  内存使用: 0.6050
  能量使用: 0.8566
  推理时间: 2.2257秒

批次 99:
  奖励值: 123.4449
  收益率: 0.2464
  距离: 30.6396
  内存使用: 0.5785
  能量使用: 0.8921
  推理时间: 2.2728秒

批次 100:
  奖励值: 128.6891
  收益率: 0.2552
  距离: 30.4392
  内存使用: 0.5967
  能量使用: 0.9858
  推理时间: 2.5625秒


==================== 总结 ====================
平均收益率: 0.2515
平均能量使用: 0.9392
平均推理时间: 2.3499秒
