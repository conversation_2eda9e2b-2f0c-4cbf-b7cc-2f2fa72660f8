推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 39.9616
  收益率: 0.5112
  距离: 11.1216
  内存使用: 0.0595
  能量使用: 0.2958
  推理时间: 1.0314秒

批次 2:
  奖励值: 46.8493
  收益率: 0.5782
  距离: 13.0473
  内存使用: 0.0919
  能量使用: 0.3622
  推理时间: 1.0622秒

批次 3:
  奖励值: 40.0783
  收益率: 0.5068
  距离: 9.5216
  内存使用: 0.0147
  能量使用: 0.3070
  推理时间: 0.9549秒

批次 4:
  奖励值: 47.7497
  收益率: 0.5761
  距离: 12.9993
  内存使用: 0.1204
  能量使用: 0.3808
  推理时间: 1.2371秒

批次 5:
  奖励值: 44.1014
  收益率: 0.5577
  距离: 11.0079
  内存使用: 0.0741
  能量使用: 0.3151
  推理时间: 1.1032秒

批次 6:
  奖励值: 54.5353
  收益率: 0.6394
  距离: 13.7707
  内存使用: 0.1293
  能量使用: 0.4294
  推理时间: 1.2648秒

批次 7:
  奖励值: 53.3332
  收益率: 0.5996
  距离: 11.4970
  内存使用: 0.0691
  能量使用: 0.3675
  推理时间: 1.1245秒

批次 8:
  奖励值: 48.3408
  收益率: 0.5784
  距离: 11.2272
  内存使用: 0.0821
  能量使用: 0.4091
  推理时间: 1.2121秒

批次 9:
  奖励值: 44.5967
  收益率: 0.5472
  距离: 11.1724
  内存使用: 0.0810
  能量使用: 0.3531
  推理时间: 1.0575秒

批次 10:
  奖励值: 46.2441
  收益率: 0.5392
  距离: 11.6902
  内存使用: 0.0993
  能量使用: 0.3223
  推理时间: 1.0089秒

批次 11:
  奖励值: 42.8835
  收益率: 0.5200
  距离: 9.9338
  内存使用: 0.0111
  能量使用: 0.2881
  推理时间: 0.9689秒

批次 12:
  奖励值: 47.7245
  收益率: 0.5952
  距离: 12.8471
  内存使用: 0.1131
  能量使用: 0.3908
  推理时间: 1.1309秒

批次 13:
  奖励值: 45.0314
  收益率: 0.5586
  距离: 11.5407
  内存使用: 0.0428
  能量使用: 0.3537
  推理时间: 1.0302秒

批次 14:
  奖励值: 52.5892
  收益率: 0.5990
  距离: 11.1665
  内存使用: 0.0959
  能量使用: 0.3591
  推理时间: 1.1653秒

批次 15:
  奖励值: 45.1340
  收益率: 0.5655
  距离: 13.6913
  内存使用: 0.0897
  能量使用: 0.3511
  推理时间: 1.0890秒

批次 16:
  奖励值: 48.6830
  收益率: 0.5959
  距离: 13.9605
  内存使用: 0.0957
  能量使用: 0.3778
  推理时间: 1.2426秒

批次 17:
  奖励值: 45.4571
  收益率: 0.5286
  距离: 10.2653
  内存使用: 0.0861
  能量使用: 0.3344
  推理时间: 1.0101秒

批次 18:
  奖励值: 45.1797
  收益率: 0.5349
  距离: 11.9840
  内存使用: 0.0792
  能量使用: 0.3775
  推理时间: 1.0068秒

批次 19:
  奖励值: 43.6913
  收益率: 0.5420
  距离: 10.2149
  内存使用: 0.0389
  能量使用: 0.3637
  推理时间: 1.0636秒

批次 20:
  奖励值: 47.4925
  收益率: 0.5817
  距离: 12.7443
  内存使用: 0.0572
  能量使用: 0.4136
  推理时间: 1.2271秒

批次 21:
  奖励值: 49.8671
  收益率: 0.5899
  距离: 13.9773
  内存使用: 0.1112
  能量使用: 0.3772
  推理时间: 1.2239秒

批次 22:
  奖励值: 44.2604
  收益率: 0.5400
  距离: 11.1213
  内存使用: 0.0491
  能量使用: 0.3089
  推理时间: 1.0597秒

批次 23:
  奖励值: 45.2617
  收益率: 0.5640
  距离: 13.7899
  内存使用: 0.0952
  能量使用: 0.3914
  推理时间: 1.1768秒

批次 24:
  奖励值: 41.5518
  收益率: 0.5043
  距离: 11.3515
  内存使用: 0.0259
  能量使用: 0.3189
  推理时间: 1.0005秒

批次 25:
  奖励值: 42.9980
  收益率: 0.5391
  距离: 11.3504
  内存使用: 0.0928
  能量使用: 0.3608
  推理时间: 1.3425秒

批次 26:
  奖励值: 47.5426
  收益率: 0.5783
  距离: 12.2599
  内存使用: 0.0653
  能量使用: 0.3896
  推理时间: 1.0407秒

批次 27:
  奖励值: 43.6086
  收益率: 0.5397
  距离: 11.5431
  内存使用: 0.0676
  能量使用: 0.3722
  推理时间: 0.9229秒

批次 28:
  奖励值: 43.0626
  收益率: 0.5556
  距离: 12.4121
  内存使用: 0.0575
  能量使用: 0.4051
  推理时间: 1.1369秒

批次 29:
  奖励值: 47.3431
  收益率: 0.5499
  距离: 12.8872
  内存使用: 0.0708
  能量使用: 0.3559
  推理时间: 0.9877秒

批次 30:
  奖励值: 47.8424
  收益率: 0.5967
  距离: 11.2638
  内存使用: 0.1009
  能量使用: 0.3506
  推理时间: 0.9955秒

批次 31:
  奖励值: 44.8763
  收益率: 0.5683
  距离: 12.9638
  内存使用: 0.0605
  能量使用: 0.4232
  推理时间: 5.3445秒

批次 32:
  奖励值: 48.3588
  收益率: 0.6091
  距离: 12.7937
  内存使用: 0.0899
  能量使用: 0.3988
  推理时间: 0.9867秒

批次 33:
  奖励值: 45.9397
  收益率: 0.5724
  距离: 12.1158
  内存使用: 0.0356
  能量使用: 0.3693
  推理时间: 0.9235秒

批次 34:
  奖励值: 46.5059
  收益率: 0.5722
  距离: 11.1929
  内存使用: 0.0790
  能量使用: 0.3163
  推理时间: 0.9074秒

批次 35:
  奖励值: 44.3355
  收益率: 0.5518
  距离: 11.0764
  内存使用: 0.0431
  能量使用: 0.4096
  推理时间: 0.8996秒

批次 36:
  奖励值: 45.6589
  收益率: 0.5882
  距离: 12.2008
  内存使用: 0.0639
  能量使用: 0.4175
  推理时间: 0.9105秒

批次 37:
  奖励值: 47.6073
  收益率: 0.5974
  距离: 12.9164
  内存使用: 0.0488
  能量使用: 0.4202
  推理时间: 0.9885秒

批次 38:
  奖励值: 44.6861
  收益率: 0.5563
  距离: 12.0221
  内存使用: 0.0578
  能量使用: 0.3480
  推理时间: 0.9227秒

批次 39:
  奖励值: 42.1401
  收益率: 0.5364
  距离: 12.5003
  内存使用: 0.0573
  能量使用: 0.3622
  推理时间: 0.9108秒

批次 40:
  奖励值: 41.7128
  收益率: 0.5330
  距离: 11.5641
  内存使用: 0.0368
  能量使用: 0.3736
  推理时间: 0.9359秒

批次 41:
  奖励值: 45.7745
  收益率: 0.5755
  距离: 9.4770
  内存使用: 0.0158
  能量使用: 0.3582
  推理时间: 0.9706秒

批次 42:
  奖励值: 48.1328
  收益率: 0.5881
  距离: 12.0548
  内存使用: 0.1074
  能量使用: 0.3722
  推理时间: 1.0408秒

批次 43:
  奖励值: 47.8285
  收益率: 0.5894
  距离: 12.7003
  内存使用: 0.0658
  能量使用: 0.3768
  推理时间: 0.9980秒

批次 44:
  奖励值: 43.6323
  收益率: 0.5532
  距离: 13.1464
  内存使用: 0.0627
  能量使用: 0.3262
  推理时间: 1.0205秒

批次 45:
  奖励值: 40.0002
  收益率: 0.4951
  距离: 10.0746
  内存使用: 0.0126
  能量使用: 0.3124
  推理时间: 0.8384秒

批次 46:
  奖励值: 48.1853
  收益率: 0.5840
  距离: 12.4035
  内存使用: 0.0351
  能量使用: 0.3907
  推理时间: 1.0614秒

批次 47:
  奖励值: 42.3259
  收益率: 0.5416
  距离: 12.5473
  内存使用: 0.0223
  能量使用: 0.3786
  推理时间: 0.9332秒

批次 48:
  奖励值: 40.1710
  收益率: 0.5118
  距离: 10.1360
  内存使用: -0.0056
  能量使用: 0.3276
  推理时间: 0.9100秒

批次 49:
  奖励值: 41.0442
  收益率: 0.5245
  距离: 11.7216
  内存使用: 0.0095
  能量使用: 0.3805
  推理时间: 0.9415秒

批次 50:
  奖励值: 45.2655
  收益率: 0.5541
  距离: 10.4569
  内存使用: 0.0664
  能量使用: 0.3458
  推理时间: 1.0137秒

批次 51:
  奖励值: 46.7972
  收益率: 0.5667
  距离: 11.3757
  内存使用: 0.0732
  能量使用: 0.3567
  推理时间: 1.0056秒

批次 52:
  奖励值: 49.0718
  收益率: 0.6064
  距离: 15.3067
  内存使用: 0.1404
  能量使用: 0.4093
  推理时间: 1.0819秒

批次 53:
  奖励值: 58.3721
  收益率: 0.6527
  距离: 13.8062
  内存使用: 0.1685
  能量使用: 0.4812
  推理时间: 1.2106秒

批次 54:
  奖励值: 44.5145
  收益率: 0.5598
  距离: 12.4828
  内存使用: 0.0919
  能量使用: 0.3965
  推理时间: 1.0311秒

批次 55:
  奖励值: 44.0881
  收益率: 0.5486
  距离: 9.6496
  内存使用: 0.0576
  能量使用: 0.3166
  推理时间: 0.9545秒

批次 56:
  奖励值: 48.6934
  收益率: 0.5588
  距离: 10.5893
  内存使用: 0.0670
  能量使用: 0.3569
  推理时间: 1.0169秒

批次 57:
  奖励值: 41.3950
  收益率: 0.5181
  距离: 11.4187
  内存使用: 0.0501
  能量使用: 0.3412
  推理时间: 0.9371秒

批次 58:
  奖励值: 48.3636
  收益率: 0.5974
  距离: 13.1439
  内存使用: 0.0841
  能量使用: 0.4448
  推理时间: 1.0702秒

批次 59:
  奖励值: 46.5641
  收益率: 0.5760
  距离: 11.0750
  内存使用: 0.0378
  能量使用: 0.3590
  推理时间: 1.0523秒

批次 60:
  奖励值: 43.8440
  收益率: 0.5467
  距离: 12.5421
  内存使用: 0.0276
  能量使用: 0.3425
  推理时间: 0.9573秒

批次 61:
  奖励值: 44.4698
  收益率: 0.5474
  距离: 12.2730
  内存使用: 0.0329
  能量使用: 0.3544
  推理时间: 0.9993秒

批次 62:
  奖励值: 40.6148
  收益率: 0.5005
  距离: 11.0331
  内存使用: 0.0312
  能量使用: 0.3062
  推理时间: 0.8650秒

批次 63:
  奖励值: 41.5707
  收益率: 0.5398
  距离: 10.0841
  内存使用: 0.0135
  能量使用: 0.3611
  推理时间: 0.9246秒

批次 64:
  奖励值: 46.4878
  收益率: 0.5837
  距离: 13.9053
  内存使用: 0.1353
  能量使用: 0.3839
  推理时间: 1.0663秒

批次 65:
  奖励值: 51.2262
  收益率: 0.6237
  距离: 16.0403
  内存使用: 0.1281
  能量使用: 0.3793
  推理时间: 1.2317秒

批次 66:
  奖励值: 45.6819
  收益率: 0.5620
  距离: 12.4310
  内存使用: 0.0663
  能量使用: 0.3389
  推理时间: 0.9830秒

批次 67:
  奖励值: 44.2645
  收益率: 0.5475
  距离: 11.3094
  内存使用: 0.0371
  能量使用: 0.3246
  推理时间: 0.8720秒

批次 68:
  奖励值: 45.9921
  收益率: 0.5741
  距离: 12.1697
  内存使用: 0.0602
  能量使用: 0.3860
  推理时间: 1.0055秒

批次 69:
  奖励值: 45.1647
  收益率: 0.5531
  距离: 11.2372
  内存使用: 0.0441
  能量使用: 0.3894
  推理时间: 1.0683秒

批次 70:
  奖励值: 42.7971
  收益率: 0.5390
  距离: 13.4000
  内存使用: 0.0605
  能量使用: 0.3453
  推理时间: 1.1635秒

批次 71:
  奖励值: 46.1544
  收益率: 0.5714
  距离: 12.0621
  内存使用: 0.0370
  能量使用: 0.3920
  推理时间: 1.1520秒

批次 72:
  奖励值: 41.8081
  收益率: 0.5501
  距离: 12.5623
  内存使用: 0.0623
  能量使用: 0.3591
  推理时间: 1.1280秒

批次 73:
  奖励值: 50.6896
  收益率: 0.6207
  距离: 12.2784
  内存使用: 0.1141
  能量使用: 0.4284
  推理时间: 1.1856秒

批次 74:
  奖励值: 45.5981
  收益率: 0.5577
  距离: 10.7705
  内存使用: 0.0583
  能量使用: 0.3749
  推理时间: 1.0528秒

批次 75:
  奖励值: 48.3939
  收益率: 0.5872
  距离: 12.4736
  内存使用: 0.1331
  能量使用: 0.4055
  推理时间: 1.2977秒

批次 76:
  奖励值: 42.3759
  收益率: 0.5207
  距离: 10.3077
  内存使用: -0.0032
  能量使用: 0.3438
  推理时间: 1.0654秒

批次 77:
  奖励值: 41.0707
  收益率: 0.5160
  距离: 11.0893
  内存使用: 0.0397
  能量使用: 0.3296
  推理时间: 1.0524秒

批次 78:
  奖励值: 45.5385
  收益率: 0.5628
  距离: 8.9078
  内存使用: 0.0258
  能量使用: 0.3893
  推理时间: 1.1245秒

批次 79:
  奖励值: 45.4093
  收益率: 0.5719
  距离: 11.2813
  内存使用: 0.1065
  能量使用: 0.3936
  推理时间: 1.1694秒

批次 80:
  奖励值: 49.0317
  收益率: 0.5747
  距离: 12.2489
  内存使用: 0.0977
  能量使用: 0.3901
  推理时间: 1.1712秒

批次 81:
  奖励值: 39.9838
  收益率: 0.5239
  距离: 11.4051
  内存使用: 0.3192
  能量使用: 0.3081
  推理时间: 0.9743秒

批次 82:
  奖励值: 47.3783
  收益率: 0.5814
  距离: 13.3803
  内存使用: 0.1044
  能量使用: 0.3588
  推理时间: 1.1675秒

批次 83:
  奖励值: 49.9901
  收益率: 0.5979
  距离: 11.8818
  内存使用: 0.0986
  能量使用: 0.4395
  推理时间: 1.4807秒

批次 84:
  奖励值: 47.0027
  收益率: 0.5889
  距离: 11.5669
  内存使用: 0.0564
  能量使用: 0.3712
  推理时间: 1.0114秒

批次 85:
  奖励值: 45.2954
  收益率: 0.5764
  距离: 11.5474
  内存使用: 0.0605
  能量使用: 0.4013
  推理时间: 1.0280秒

批次 86:
  奖励值: 34.8894
  收益率: 0.4726
  距离: 9.3234
  内存使用: 0.0287
  能量使用: 0.2692
  推理时间: 0.9797秒

批次 87:
  奖励值: 51.3724
  收益率: 0.6182
  距离: 12.3285
  内存使用: 0.1308
  能量使用: 0.3960
  推理时间: 1.2588秒

批次 88:
  奖励值: 45.5697
  收益率: 0.5589
  距离: 12.7828
  内存使用: 0.0923
  能量使用: 0.3855
  推理时间: 1.0905秒

批次 89:
  奖励值: 48.0803
  收益率: 0.5859
  距离: 11.0261
  内存使用: 0.1175
  能量使用: 0.4958
  推理时间: 1.2855秒

批次 90:
  奖励值: 47.2889
  收益率: 0.5719
  距离: 13.9538
  内存使用: 0.0861
  能量使用: 0.3823
  推理时间: 1.1431秒

批次 91:
  奖励值: 42.6364
  收益率: 0.5385
  距离: 10.7564
  内存使用: 0.0371
  能量使用: 0.3337
  推理时间: 0.9710秒

批次 92:
  奖励值: 45.6370
  收益率: 0.5653
  距离: 11.3138
  内存使用: 0.0787
  能量使用: 0.3335
  推理时间: 1.1362秒

批次 93:
  奖励值: 41.2975
  收益率: 0.5328
  距离: 12.7804
  内存使用: 0.0692
  能量使用: 0.3555
  推理时间: 1.0426秒

批次 94:
  奖励值: 46.2429
  收益率: 0.5776
  距离: 13.4928
  内存使用: 0.0815
  能量使用: 0.3398
  推理时间: 1.0986秒

批次 95:
  奖励值: 45.0540
  收益率: 0.5403
  距离: 11.4559
  内存使用: 0.0539
  能量使用: 0.3823
  推理时间: 1.0465秒

批次 96:
  奖励值: 46.9780
  收益率: 0.5722
  距离: 10.9861
  内存使用: 0.0595
  能量使用: 0.3985
  推理时间: 1.1347秒

批次 97:
  奖励值: 50.0860
  收益率: 0.6094
  距离: 15.6595
  内存使用: 0.1403
  能量使用: 0.4282
  推理时间: 1.1590秒

批次 98:
  奖励值: 39.1822
  收益率: 0.5050
  距离: 11.6073
  内存使用: -0.0279
  能量使用: 0.2562
  推理时间: 0.9499秒

批次 99:
  奖励值: 49.9055
  收益率: 0.5895
  距离: 13.7146
  内存使用: 0.0911
  能量使用: 0.3833
  推理时间: 1.1999秒

批次 100:
  奖励值: 43.9653
  收益率: 0.5645
  距离: 11.4716
  内存使用: 0.0507
  能量使用: 0.3472
  推理时间: 1.0766秒


==================== 总结 ====================
平均收益率: 0.5622
平均能量使用: 0.3674
平均推理时间: 1.1047秒
