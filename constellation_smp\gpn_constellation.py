"""
定义星座任务规划的GPN模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from gpn import Encoder, GPN
from indrnn.indrnn import IndRNN, IndRNN_Net, IndRNNv2
from attention.attention import MultiHead_Additive_Attention
from transformer import ConstellationTransformer

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class ConstellationEncoder(nn.Module):
    """
    星座编码器，处理多颗卫星的状态
    """
    def __init__(self, input_size, hidden_size, num_satellites, constellation_mode='cooperative',
                 use_transformer=False, transformer_config=None):
        super(ConstellationEncoder, self).__init__()
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        self.use_transformer = use_transformer

        # 为每颗卫星创建一个编码器
        self.satellite_encoders = nn.ModuleList([
            Encoder(input_size, hidden_size) for _ in range(num_satellites)
        ])

        # 星座级别的特征融合
        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_size * num_satellites, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU()
        )

        # 卫星间注意力机制
        self.inter_satellite_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=0.1
        )

        # Transformer增强模块（可选）
        if use_transformer and transformer_config is not None:
            self.transformer = ConstellationTransformer(
                input_size=input_size,
                d_model=transformer_config.get('d_model', hidden_size),
                num_heads=transformer_config.get('num_heads', 8),
                d_ff=transformer_config.get('d_ff', hidden_size * 4),
                num_layers=transformer_config.get('num_layers', 4),
                num_satellites=num_satellites,
                max_len=transformer_config.get('max_len', 5000),
                dropout=transformer_config.get('dropout', 0.1),
                activation=transformer_config.get('activation', 'gelu')
            )

            # Transformer输出投影层
            self.transformer_projection = nn.Linear(
                transformer_config.get('d_model', hidden_size),
                hidden_size
            )
        else:
            self.transformer = None
            self.transformer_projection = None
        
        # 混合模式下的门控机制
        if self.constellation_mode == 'hybrid':
            self.gate = nn.Sequential(
                nn.Linear(hidden_size * num_satellites, hidden_size),
                nn.Sigmoid()
            )

        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_size)

    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        
        返回:
        constellation_features: (batch_size, hidden_size, seq_len) - 星座级别的特征
        satellite_features: (batch_size, num_satellites, hidden_size, seq_len) - 每颗卫星的特征
        """
        batch_size = static.size(0)
        seq_len = static.size(2)
        
        # 为每颗卫星编码特征
        satellite_features = []
        for sat_idx in range(self.num_satellites):
            # 获取当前卫星的动态状态
            sat_dynamic = dynamic[:, :, :, sat_idx]
            
            # 编码当前卫星的特征
            sat_features = self.satellite_encoders[sat_idx](
                torch.cat([static, sat_dynamic], dim=1)
            )
            
            satellite_features.append(sat_features)
        
        # 堆叠所有卫星的特征: (batch_size, num_satellites, hidden_size, seq_len)
        satellite_features_stack = torch.stack(satellite_features, dim=1)
        
        # 根据星座模式应用不同的信息交互策略
        if self.constellation_mode == 'cooperative':
            # 协同模式：完全信息交互
            sat_features_attn = self.apply_attention(satellite_features_stack, batch_size, seq_len)
            satellite_features_stack = satellite_features_stack + sat_features_attn
        elif self.constellation_mode == 'hybrid':
            # 混合模式：门控信息交互
            sat_features_attn = self.apply_attention(satellite_features_stack, batch_size, seq_len)
            
            # 计算门控权重
            flat_features = satellite_features_stack.permute(0, 3, 1, 2).reshape(batch_size, seq_len, -1)
            gate_weights = self.gate(flat_features).permute(0, 2, 1).unsqueeze(1) # (batch, 1, hidden, seq)
            
            # 应用门控
            satellite_features_stack = satellite_features_stack + gate_weights * sat_features_attn
        # 在竞争模式下，不进行信息交互 (sat_features_attn is not used)

        # 融合所有卫星的特征为星座级别的特征
        # 首先重塑: (batch_size, seq_len, num_satellites, hidden_size)
        sat_features_reshaped = satellite_features_stack.permute(0, 3, 1, 2)
        
        # 然后展平卫星维度: (batch_size, seq_len, num_satellites*hidden_size)
        sat_features_flat = sat_features_reshaped.reshape(
            batch_size, seq_len, -1
        )
        
        # 应用融合层: (batch_size, seq_len, hidden_size)
        constellation_features = self.fusion_layer(sat_features_flat)

        # 转置回原始形状: (batch_size, hidden_size, seq_len)
        constellation_features = constellation_features.permute(0, 2, 1)

        # 如果启用了Transformer，则进一步增强特征
        if self.use_transformer and self.transformer is not None:
            try:
                # 准备Transformer输入: (batch_size, seq_len, input_size, num_satellites)
                transformer_input = torch.cat([
                    static.unsqueeze(-1).expand(-1, -1, -1, self.num_satellites),
                    dynamic
                ], dim=1).permute(0, 2, 1, 3)  # (batch_size, seq_len, input_size, num_satellites)

                # 通过Transformer处理
                transformer_output, transformer_satellite_outputs = self.transformer(transformer_input)

                # 投影到目标维度
                transformer_output = self.transformer_projection(transformer_output)

                # 与原始特征融合 - 确保维度匹配
                constellation_features_transposed = constellation_features.permute(0, 2, 1)  # (batch_size, seq_len, hidden_size)

                # 检查维度是否匹配
                if constellation_features_transposed.shape == transformer_output.shape:
                    constellation_features_transposed = constellation_features_transposed + transformer_output
                    constellation_features = constellation_features_transposed.permute(0, 2, 1)  # (batch_size, hidden_size, seq_len)

                    # 更新卫星特征（如果需要）
                    if transformer_satellite_outputs is not None:
                        for sat_idx, sat_output in enumerate(transformer_satellite_outputs):
                            sat_output_proj = self.transformer_projection(sat_output)
                            # 确保维度匹配
                            sat_features_transposed = satellite_features_stack[:, sat_idx, :, :].permute(0, 2, 1)  # (batch_size, seq_len, hidden_size)
                            if sat_features_transposed.shape == sat_output_proj.shape:
                                sat_features_transposed = sat_features_transposed + sat_output_proj
                                satellite_features_stack[:, sat_idx, :, :] = sat_features_transposed.permute(0, 2, 1)  # (batch_size, hidden_size, seq_len)
                else:
                    print(f"警告: Transformer输出维度 {transformer_output.shape} 与星座特征维度 {constellation_features_transposed.shape} 不匹配，跳过融合")

            except Exception as e:
                print(f"警告: Transformer处理失败: {e}，使用传统模式")

        return constellation_features, satellite_features_stack

    def apply_attention(self, features, batch_size, seq_len):
        """
        应用卫星间自注意力机制
        """
        features_attn = features.permute(3, 0, 1, 2).reshape(
            seq_len, batch_size * self.num_satellites, -1
        )
        features_attn, _ = self.inter_satellite_attention(
            features_attn, features_attn, features_attn
        )
        features_attn = features_attn.reshape(
            seq_len, batch_size, self.num_satellites, -1
        ).permute(1, 2, 3, 0)
        return features_attn


class GPNConstellation(nn.Module):
    """
    星座任务规划的GPN模型
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='indrnn', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 use_transformer=False, transformer_config=None):
        super(GPNConstellation, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.use_transformer = use_transformer

        # 星座编码器
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer,
            transformer_config
        )
        
        # 任务选择器（决定执行哪个任务）
        self.task_selector = GPN(
            hidden_size,
            rnn,
            num_nodes,
            dropout=dropout,
            num_layers=num_layers
        )
        
        # 卫星选择器（决定哪颗卫星执行任务）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, num_satellites)
        )
        
        # 初始化参数
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_uniform_(m.weight, a=math.sqrt(5))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        
        返回:
        tour_indices: (batch_size, tour_len) - 选择的任务索引
        satellite_indices: (batch_size, tour_len) - 执行任务的卫星索引
        tour_logp: (batch_size, tour_len) - 任务选择的对数概率
        satellite_logp: (batch_size, tour_len) - 卫星选择的对数概率
        """
        batch_size = static.size(0)
        seq_len = static.size(2)
        
        # 获取星座级别的特征和每颗卫星的特征
        constellation_features, satellite_features = self.constellation_encoder(static, dynamic)
        
        # 初始化任务选择的隐藏状态
        task_hx = None
        task_cx = None
        
        # 初始化结果列表
        tour_indices = []
        satellite_indices = []
        tour_logp = []
        satellite_logp = []
        
        # 第一个任务是起始点
        first_idx = torch.zeros(batch_size, dtype=torch.long, device=static.device)
        first_sat_idx = torch.zeros(batch_size, dtype=torch.long, device=static.device)
        
        tour_indices.append(first_idx)
        satellite_indices.append(first_sat_idx)
        tour_logp.append(torch.zeros(batch_size, device=static.device))
        satellite_logp.append(torch.zeros(batch_size, device=static.device))
        
        # 获取初始掩码
        mask, satellite_masks = self.mask_fn(dynamic, static=static)
        
        # 循环选择任务和卫星
        for i in range(seq_len - 1):
            # 获取上一个任务的索引和卫星索引
            prev_task_idx = tour_indices[-1]
            prev_sat_idx = satellite_indices[-1]
            
            # 获取上一个任务的特征
            prev_task_features = torch.gather(
                constellation_features,
                2,
                prev_task_idx.unsqueeze(1).unsqueeze(2).expand(-1, constellation_features.size(1), 1)
            ).squeeze(2)
            
            # 选择下一个任务
            # 转置constellation_features以匹配GPN期望的输入格式: (batch_size, seq_len, hidden_size)
            constellation_features_transposed = constellation_features.permute(0, 2, 1)

            # 根据RNN类型处理不同的返回值
            if self.task_selector.rnn == 'lstm':
                task_logits, task_hx, task_cx, _ = self.task_selector(
                    prev_task_features,
                    constellation_features_transposed,
                    None,
                    task_hx,
                    task_cx
                )
            else:  # indrnn or indrnnv2
                task_logits, task_hx = self.task_selector(
                    prev_task_features,
                    constellation_features_transposed,
                    None,
                    task_hx,
                    task_cx
                )
            
            # 应用掩码
            task_logits[mask == 0] = -float('inf')
            
            # 计算任务选择概率
            task_probs = F.softmax(task_logits, dim=1)
            
            # 检查每个样本的概率和是否接近1，如果不是，则进行归一化
            probs_sum = task_probs.sum(dim=1, keepdim=True)
            valid_mask = (probs_sum > 1e-10).float()
            
            # 对于有效的样本，保持原样；对于无效的样本，创建一个均匀分布
            task_probs = torch.where(
                valid_mask.expand_as(task_probs) > 0,
                task_probs / torch.max(probs_sum, torch.ones_like(probs_sum) * 1e-10),
                torch.ones_like(task_probs) / task_probs.size(1)
            )
            
            task_dist = torch.distributions.Categorical(task_probs)
            task_idx = task_dist.sample()
            
            # 获取选定任务的特征
            task_features = torch.gather(
                constellation_features,
                2,
                task_idx.unsqueeze(1).unsqueeze(2).expand(-1, constellation_features.size(1), 1)
            ).squeeze(2)
            
            # 选择执行任务的卫星
            # 合并任务特征和上一个卫星的特征
            combined_features = torch.cat([
                task_features,
                prev_task_features
            ], dim=1)
            
            # 计算卫星选择概率
            sat_logits = self.satellite_selector(combined_features)
            
            # 应用卫星掩码（只有能够执行该任务的卫星才能被选择）
            sat_mask = satellite_masks[torch.arange(batch_size, device=static.device), task_idx]
            sat_logits[sat_mask == 0] = -float('inf')
            
            sat_probs = F.softmax(sat_logits, dim=1)
            
            # 检查卫星概率分布是否有效
            sat_probs_sum = sat_probs.sum(dim=1, keepdim=True)
            sat_valid_mask = (sat_probs_sum > 1e-10).float()
            
            # 对于有效的样本，保持原样；对于无效的样本，创建一个均匀分布
            sat_probs = torch.where(
                sat_valid_mask.expand_as(sat_probs) > 0,
                sat_probs / torch.max(sat_probs_sum, torch.ones_like(sat_probs_sum) * 1e-10),
                torch.ones_like(sat_probs) / sat_probs.size(1)
            )
            
            sat_dist = torch.distributions.Categorical(sat_probs)
            sat_idx = sat_dist.sample()
            
            # 记录结果
            tour_indices.append(task_idx)
            satellite_indices.append(sat_idx)
            tour_logp.append(task_dist.log_prob(task_idx))
            satellite_logp.append(sat_dist.log_prob(sat_idx))
            
            # 更新动态状态
            if self.update_fn is not None:
                dynamic = self.update_fn(static, dynamic, task_idx, sat_idx)
            
            # 更新掩码
            if self.mask_fn is not None:
                mask, satellite_masks = self.mask_fn(dynamic, static=static)
            
            # 如果没有可选的任务，则结束
            if torch.sum(mask) == 0:
                break
        
        # 堆叠结果
        tour_indices = torch.stack(tour_indices, dim=1)
        satellite_indices = torch.stack(satellite_indices, dim=1)
        tour_logp = torch.stack(tour_logp, dim=1)
        satellite_logp = torch.stack(satellite_logp, dim=1)

        return tour_indices, satellite_indices, tour_logp, satellite_logp


class ConstellationStateCritic(nn.Module):
    """
    星座状态评论家，评估星座级别的问题复杂度
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites, constellation_mode='cooperative'):
        super(ConstellationStateCritic, self).__init__()
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        
        # 使用与策略网络相同的编码器
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode
        )
        
        # 评论家网络
        self.fc1 = nn.Conv1d(hidden_size, 20, kernel_size=1)
        self.fc2 = nn.Conv1d(20, 20, kernel_size=1)
        self.fc3 = nn.Conv1d(20, 1, kernel_size=1)
        
        # 初始化参数
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static, dynamic):
        # 获取星座级别的特征
        constellation_features, _ = self.constellation_encoder(static, dynamic)
        
        # 评估问题复杂度
        output = F.relu(self.fc1(constellation_features))
        output = F.relu(self.fc2(output))
        output = self.fc3(output).sum(dim=2)
        
        return output 