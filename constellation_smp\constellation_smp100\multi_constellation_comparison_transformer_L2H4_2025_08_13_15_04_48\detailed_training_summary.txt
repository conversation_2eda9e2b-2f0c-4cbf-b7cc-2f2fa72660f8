多星座模式训练详细日志
================================================================================

实验时间: 2025-08-15 03:58:06
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练100000, 验证10000
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
训练结果:
  最佳验证奖励: 33.463000
测试性能:
  平均收益率: 0.823800
  平均距离: 10.080155
  平均内存使用: -0.023372
  平均功耗: 0.305172
  综合性能评分: 3.033649
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
训练结果:
  最佳验证奖励: 32.103694
测试性能:
  平均收益率: 0.790244
  平均距离: 9.501726
  平均内存使用: -0.038372
  平均功耗: 0.288952
  综合性能评分: 2.987913
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
训练结果:
  最佳验证奖励: 30.843378
测试性能:
  平均收益率: 0.752770
  平均距离: 8.906508
  平均内存使用: -0.053391
  平均功耗: 0.270996
  综合性能评分: 2.912251
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: cooperative (33.4630)
最佳收益率模式: cooperative (0.8238)
最短距离模式: hybrid (8.9065)
最低功耗模式: hybrid (0.2710)

推荐使用: cooperative 模式
推荐理由: 在关键性能指标上表现最佳
