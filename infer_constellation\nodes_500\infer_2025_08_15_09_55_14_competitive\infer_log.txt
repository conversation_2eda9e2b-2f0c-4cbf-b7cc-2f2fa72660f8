推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 80.1463
  收益率: 0.3889
  距离: 20.0665
  内存使用: 0.6076
  能量使用: 0.6334
  推理时间: 1.9180秒

批次 2:
  奖励值: 88.2636
  收益率: 0.4350
  距离: 23.3680
  内存使用: 0.8000
  能量使用: 0.7047
  推理时间: 1.8812秒

批次 3:
  奖励值: 94.5476
  收益率: 0.4711
  距离: 24.5371
  内存使用: 0.4456
  能量使用: 0.7220
  推理时间: 2.2007秒

批次 4:
  奖励值: 88.8769
  收益率: 0.4450
  距离: 24.0563
  内存使用: 0.4546
  能量使用: 0.7354
  推理时间: 2.0241秒

批次 5:
  奖励值: 73.1681
  收益率: 0.3753
  距离: 20.7463
  内存使用: 0.3039
  能量使用: 0.6136
  推理时间: 1.8477秒

批次 6:
  奖励值: 89.1940
  收益率: 0.4520
  距离: 20.8894
  内存使用: 0.4264
  能量使用: 0.6962
  推理时间: 2.2049秒

批次 7:
  奖励值: 86.6502
  收益率: 0.4377
  距离: 22.1557
  内存使用: 0.4366
  能量使用: 0.7072
  推理时间: 1.8080秒

批次 8:
  奖励值: 84.6039
  收益率: 0.4278
  距离: 19.3642
  内存使用: 0.3962
  能量使用: 0.6317
  推理时间: 1.8788秒

批次 9:
  奖励值: 70.1060
  收益率: 0.3690
  距离: 19.4766
  内存使用: 0.3087
  能量使用: 0.6027
  推理时间: 1.7163秒

批次 10:
  奖励值: 79.7103
  收益率: 0.3973
  距离: 22.9405
  内存使用: 0.3903
  能量使用: 0.6487
  推理时间: 1.7797秒

批次 11:
  奖励值: 79.8194
  收益率: 0.3933
  距离: 22.5653
  内存使用: 0.3676
  能量使用: 0.6283
  推理时间: 1.8015秒

批次 12:
  奖励值: 78.6214
  收益率: 0.4000
  距离: 23.7666
  内存使用: 0.6883
  能量使用: 0.7022
  推理时间: 1.8308秒

批次 13:
  奖励值: 73.8664
  收益率: 0.3857
  距离: 20.3781
  内存使用: 0.2895
  能量使用: 0.5996
  推理时间: 1.7043秒

批次 14:
  奖励值: 77.4335
  收益率: 0.3823
  距离: 19.2468
  内存使用: 0.3461
  能量使用: 0.5402
  推理时间: 1.8501秒

批次 15:
  奖励值: 85.4182
  收益率: 0.4253
  距离: 22.1562
  内存使用: 0.3492
  能量使用: 0.6583
  推理时间: 1.9072秒

批次 16:
  奖励值: 78.7503
  收益率: 0.3953
  距离: 18.9394
  内存使用: 0.3214
  能量使用: 0.6385
  推理时间: 1.7126秒

批次 17:
  奖励值: 84.8033
  收益率: 0.4243
  距离: 23.3815
  内存使用: 0.3996
  能量使用: 0.6122
  推理时间: 1.8497秒

批次 18:
  奖励值: 81.4857
  收益率: 0.4214
  距离: 19.8417
  内存使用: 0.3649
  能量使用: 0.6220
  推理时间: 1.7871秒

批次 19:
  奖励值: 88.2038
  收益率: 0.4336
  距离: 24.2463
  内存使用: 0.4951
  能量使用: 0.6508
  推理时间: 1.9398秒

批次 20:
  奖励值: 81.3600
  收益率: 0.4122
  距离: 22.2676
  内存使用: 0.3763
  能量使用: 0.6360
  推理时间: 1.8588秒

批次 21:
  奖励值: 82.5658
  收益率: 0.4155
  距离: 21.9563
  内存使用: 0.3847
  能量使用: 0.7275
  推理时间: 1.8726秒

批次 22:
  奖励值: 88.6249
  收益率: 0.4549
  距离: 22.3210
  内存使用: 0.4893
  能量使用: 0.7398
  推理时间: 2.0241秒

批次 23:
  奖励值: 88.3980
  收益率: 0.4329
  距离: 20.6777
  内存使用: 0.4265
  能量使用: 0.6998
  推理时间: 1.9716秒

批次 24:
  奖励值: 80.0342
  收益率: 0.4188
  距离: 21.1711
  内存使用: 0.6400
  能量使用: 0.7204
  推理时间: 1.8896秒

批次 25:
  奖励值: 90.9501
  收益率: 0.4508
  距离: 24.1374
  内存使用: 0.4472
  能量使用: 0.7151
  推理时间: 1.9784秒

批次 26:
  奖励值: 89.9619
  收益率: 0.4494
  距离: 24.1494
  内存使用: 0.4532
  能量使用: 0.6737
  推理时间: 1.9705秒

批次 27:
  奖励值: 86.1517
  收益率: 0.4291
  距离: 20.7078
  内存使用: 0.4463
  能量使用: 0.7425
  推理时间: 1.9138秒

批次 28:
  奖励值: 74.4758
  收益率: 0.3747
  距离: 20.4162
  内存使用: 0.2292
  能量使用: 0.6029
  推理时间: 1.6427秒

批次 29:
  奖励值: 81.6329
  收益率: 0.4077
  距离: 19.9490
  内存使用: 0.3179
  能量使用: 0.6674
  推理时间: 1.7550秒

批次 30:
  奖励值: 86.0314
  收益率: 0.4349
  距离: 23.2357
  内存使用: 0.4030
  能量使用: 0.6986
  推理时间: 1.8996秒

批次 31:
  奖励值: 80.8204
  收益率: 0.4077
  距离: 22.2267
  内存使用: 0.6550
  能量使用: 0.6775
  推理时间: 1.9069秒

批次 32:
  奖励值: 89.3694
  收益率: 0.4436
  距离: 21.3646
  内存使用: 0.3932
  能量使用: 0.7033
  推理时间: 1.9538秒

批次 33:
  奖励值: 86.1379
  收益率: 0.4275
  距离: 21.9091
  内存使用: 0.3248
  能量使用: 0.6902
  推理时间: 1.9138秒

批次 34:
  奖励值: 88.4678
  收益率: 0.4478
  距离: 23.5456
  内存使用: 0.4296
  能量使用: 0.6893
  推理时间: 1.9337秒

批次 35:
  奖励值: 76.2298
  收益率: 0.4020
  距离: 24.2402
  内存使用: 0.2804
  能量使用: 0.6198
  推理时间: 1.8520秒

批次 36:
  奖励值: 86.9939
  收益率: 0.4269
  距离: 21.1161
  内存使用: 0.3795
  能量使用: 0.7167
  推理时间: 1.9360秒

批次 37:
  奖励值: 79.5656
  收益率: 0.3989
  距离: 20.2430
  内存使用: 0.3647
  能量使用: 0.6612
  推理时间: 1.8128秒

批次 38:
  奖励值: 85.0467
  收益率: 0.4256
  距离: 22.7484
  内存使用: 0.4538
  能量使用: 0.7043
  推理时间: 1.9753秒

批次 39:
  奖励值: 82.7415
  收益率: 0.4105
  距离: 22.4632
  内存使用: 0.3785
  能量使用: 0.6754
  推理时间: 1.9087秒

批次 40:
  奖励值: 83.0400
  收益率: 0.4374
  距离: 21.4971
  内存使用: 0.3706
  能量使用: 0.6524
  推理时间: 1.9768秒

批次 41:
  奖励值: 84.2857
  收益率: 0.4339
  距离: 26.7488
  内存使用: 0.3979
  能量使用: 0.6821
  推理时间: 1.9676秒

批次 42:
  奖励值: 83.4714
  收益率: 0.4177
  距离: 23.4190
  内存使用: 0.3431
  能量使用: 0.6952
  推理时间: 2.0304秒

批次 43:
  奖励值: 87.4475
  收益率: 0.4310
  距离: 22.4815
  内存使用: 0.6853
  能量使用: 0.7383
  推理时间: 2.1082秒

批次 44:
  奖励值: 94.4261
  收益率: 0.4842
  距离: 27.9366
  内存使用: 0.4708
  能量使用: 0.7403
  推理时间: 2.2266秒

批次 45:
  奖励值: 85.6565
  收益率: 0.4128
  距离: 23.9269
  内存使用: 0.4393
  能量使用: 0.7208
  推理时间: 1.8911秒

批次 46:
  奖励值: 73.9192
  收益率: 0.3650
  距离: 17.4809
  内存使用: 0.2547
  能量使用: 0.6260
  推理时间: 1.6285秒

批次 47:
  奖励值: 86.0681
  收益率: 0.4372
  距离: 21.1829
  内存使用: 0.3757
  能量使用: 0.7053
  推理时间: 1.9651秒

批次 48:
  奖励值: 85.1471
  收益率: 0.4305
  距离: 23.6109
  内存使用: 0.3927
  能量使用: 0.7022
  推理时间: 2.0857秒

批次 49:
  奖励值: 93.6671
  收益率: 0.4594
  距离: 23.3186
  内存使用: 0.4686
  能量使用: 0.6786
  推理时间: 2.0192秒

批次 50:
  奖励值: 83.7282
  收益率: 0.4097
  距离: 21.5281
  内存使用: 0.3663
  能量使用: 0.6492
  推理时间: 1.8808秒

批次 51:
  奖励值: 87.6791
  收益率: 0.4516
  距离: 24.1440
  内存使用: 0.3979
  能量使用: 0.6998
  推理时间: 2.0830秒

批次 52:
  奖励值: 82.6205
  收益率: 0.4190
  距离: 21.4671
  内存使用: 0.3878
  能量使用: 0.7041
  推理时间: 1.9524秒

批次 53:
  奖励值: 82.8821
  收益率: 0.4244
  距离: 23.8912
  内存使用: 0.4136
  能量使用: 0.7310
  推理时间: 1.8978秒

批次 54:
  奖励值: 83.5581
  收益率: 0.4134
  距离: 22.5347
  内存使用: 0.6684
  能量使用: 0.6807
  推理时间: 1.9262秒

批次 55:
  奖励值: 84.2366
  收益率: 0.4193
  距离: 20.1375
  内存使用: 0.3586
  能量使用: 0.6308
  推理时间: 1.8827秒

批次 56:
  奖励值: 91.1002
  收益率: 0.4532
  距离: 23.0423
  内存使用: 0.4474
  能量使用: 0.7099
  推理时间: 1.9410秒

批次 57:
  奖励值: 91.3517
  收益率: 0.4537
  距离: 21.7348
  内存使用: 0.4351
  能量使用: 0.7267
  推理时间: 2.1886秒

批次 58:
  奖励值: 81.3247
  收益率: 0.4017
  距离: 21.1976
  内存使用: 0.3564
  能量使用: 0.5965
  推理时间: 1.8226秒

批次 59:
  奖励值: 83.4692
  收益率: 0.4211
  距离: 21.6427
  内存使用: 0.3755
  能量使用: 0.6434
  推理时间: 1.9268秒

批次 60:
  奖励值: 78.9071
  收益率: 0.3853
  距离: 19.0391
  内存使用: 0.2977
  能量使用: 0.6490
  推理时间: 1.8243秒

批次 61:
  奖励值: 91.5438
  收益率: 0.4606
  距离: 25.1212
  内存使用: 0.4444
  能量使用: 0.7468
  推理时间: 2.1006秒

批次 62:
  奖励值: 74.2964
  收益率: 0.3893
  距离: 22.5029
  内存使用: 0.3012
  能量使用: 0.6031
  推理时间: 1.6941秒

批次 63:
  奖励值: 81.8631
  收益率: 0.3981
  距离: 22.8709
  内存使用: 0.6869
  能量使用: 0.7161
  推理时间: 2.0068秒

批次 64:
  奖励值: 77.2624
  收益率: 0.3878
  距离: 20.8122
  内存使用: 0.2507
  能量使用: 0.6123
  推理时间: 1.7122秒

批次 65:
  奖励值: 82.2330
  收益率: 0.4129
  距离: 22.6827
  内存使用: 0.3841
  能量使用: 0.6673
  推理时间: 1.8661秒

批次 66:
  奖励值: 86.2209
  收益率: 0.4339
  距离: 22.8529
  内存使用: 0.3880
  能量使用: 0.7173
  推理时间: 2.0262秒

批次 67:
  奖励值: 80.6591
  收益率: 0.3963
  距离: 20.0609
  内存使用: 0.3263
  能量使用: 0.6947
  推理时间: 1.7887秒

批次 68:
  奖励值: 88.9259
  收益率: 0.4389
  距离: 22.8872
  内存使用: 0.4067
  能量使用: 0.6542
  推理时间: 2.0701秒

批次 69:
  奖励值: 79.8651
  收益率: 0.4090
  距离: 21.6567
  内存使用: 0.3772
  能量使用: 0.6359
  推理时间: 1.8909秒

批次 70:
  奖励值: 94.2236
  收益率: 0.4654
  距离: 23.8639
  内存使用: 0.4996
  能量使用: 0.7892
  推理时间: 2.1823秒

批次 71:
  奖励值: 83.6498
  收益率: 0.4157
  距离: 22.4381
  内存使用: 0.4193
  能量使用: 0.6429
  推理时间: 1.9925秒

批次 72:
  奖励值: 80.2245
  收益率: 0.4357
  距离: 22.7023
  内存使用: 0.3772
  能量使用: 0.6715
  推理时间: 1.9511秒

批次 73:
  奖励值: 77.6918
  收益率: 0.3935
  距离: 21.6720
  内存使用: 0.3659
  能量使用: 0.5784
  推理时间: 1.7949秒

批次 74:
  奖励值: 91.1519
  收益率: 0.4501
  距离: 23.9878
  内存使用: 0.4286
  能量使用: 0.6798
  推理时间: 2.1418秒

批次 75:
  奖励值: 84.1189
  收益率: 0.4195
  距离: 23.0937
  内存使用: 0.3881
  能量使用: 0.6803
  推理时间: 1.9373秒

批次 76:
  奖励值: 83.9999
  收益率: 0.4261
  距离: 23.4903
  内存使用: 0.4167
  能量使用: 0.6932
  推理时间: 1.9600秒

批次 77:
  奖励值: 79.2879
  收益率: 0.3980
  距离: 20.2045
  内存使用: 0.3588
  能量使用: 0.6608
  推理时间: 1.8708秒

批次 78:
  奖励值: 87.7474
  收益率: 0.4171
  距离: 21.1299
  内存使用: 0.4124
  能量使用: 0.7631
  推理时间: 1.9903秒

批次 79:
  奖励值: 83.9836
  收益率: 0.4331
  距离: 23.1093
  内存使用: 0.4069
  能量使用: 0.7165
  推理时间: 1.8687秒

批次 80:
  奖励值: 82.0506
  收益率: 0.4308
  距离: 20.4888
  内存使用: 0.3503
  能量使用: 0.6598
  推理时间: 1.8514秒

批次 81:
  奖励值: 79.4372
  收益率: 0.3967
  距离: 22.9226
  内存使用: 0.3760
  能量使用: 0.6270
  推理时间: 1.8014秒

批次 82:
  奖励值: 84.8256
  收益率: 0.4213
  距离: 22.1017
  内存使用: 0.4014
  能量使用: 0.6458
  推理时间: 1.9166秒

批次 83:
  奖励值: 85.1636
  收益率: 0.4215
  距离: 22.3943
  内存使用: 0.3694
  能量使用: 0.7091
  推理时间: 1.9339秒

批次 84:
  奖励值: 86.5933
  收益率: 0.4366
  距离: 24.1546
  内存使用: 0.3468
  能量使用: 0.7119
  推理时间: 1.9921秒

批次 85:
  奖励值: 84.5921
  收益率: 0.4201
  距离: 20.1955
  内存使用: 0.3575
  能量使用: 0.6628
  推理时间: 1.8909秒

批次 86:
  奖励值: 86.3288
  收益率: 0.4277
  距离: 22.6485
  内存使用: 0.3831
  能量使用: 0.7108
  推理时间: 2.0667秒

批次 87:
  奖励值: 86.3157
  收益率: 0.4439
  距离: 21.0182
  内存使用: 0.4740
  能量使用: 0.6764
  推理时间: 2.0194秒

批次 88:
  奖励值: 82.7167
  收益率: 0.4284
  距离: 20.9205
  内存使用: 0.3641
  能量使用: 0.6819
  推理时间: 1.8953秒

批次 89:
  奖励值: 86.0695
  收益率: 0.4394
  距离: 22.6455
  内存使用: 0.3836
  能量使用: 0.6319
  推理时间: 1.9144秒

批次 90:
  奖励值: 74.0938
  收益率: 0.3745
  距离: 18.2915
  内存使用: 0.3066
  能量使用: 0.6143
  推理时间: 1.7005秒

批次 91:
  奖励值: 84.2950
  收益率: 0.4251
  距离: 23.8306
  内存使用: 0.4093
  能量使用: 0.7701
  推理时间: 2.0921秒

批次 92:
  奖励值: 79.6506
  收益率: 0.4225
  距离: 24.4721
  内存使用: 0.3714
  能量使用: 0.6553
  推理时间: 1.8792秒

批次 93:
  奖励值: 81.4895
  收益率: 0.4097
  距离: 19.8673
  内存使用: 0.3031
  能量使用: 0.6505
  推理时间: 1.9506秒

批次 94:
  奖励值: 77.2778
  收益率: 0.4075
  距离: 19.5077
  内存使用: 0.3324
  能量使用: 0.6042
  推理时间: 1.8533秒

批次 95:
  奖励值: 81.2447
  收益率: 0.4056
  距离: 21.7875
  内存使用: 0.3147
  能量使用: 0.6084
  推理时间: 1.8233秒

批次 96:
  奖励值: 75.5574
  收益率: 0.3906
  距离: 20.3429
  内存使用: 0.2963
  能量使用: 0.6461
  推理时间: 1.7473秒

批次 97:
  奖励值: 83.7208
  收益率: 0.4185
  距离: 24.0590
  内存使用: 0.4501
  能量使用: 0.6492
  推理时间: 1.9447秒

批次 98:
  奖励值: 80.3137
  收益率: 0.4190
  距离: 18.7673
  内存使用: 0.3798
  能量使用: 0.6255
  推理时间: 1.8614秒

批次 99:
  奖励值: 87.8685
  收益率: 0.4309
  距离: 20.3826
  内存使用: 0.3933
  能量使用: 0.6871
  推理时间: 1.9950秒

批次 100:
  奖励值: 86.3699
  收益率: 0.4241
  距离: 22.3537
  内存使用: 0.3807
  能量使用: 0.7258
  推理时间: 2.0310秒


==================== 总结 ====================
平均收益率: 0.4207
平均能量使用: 0.6735
平均推理时间: 1.9184秒
