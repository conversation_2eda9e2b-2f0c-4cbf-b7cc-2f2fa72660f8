推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 117.2451
  收益率: 0.3888
  距离: 28.6040
  内存使用: 0.6061
  能量使用: 0.8996
  推理时间: 2.7509秒

批次 2:
  奖励值: 122.1823
  收益率: 0.4038
  距离: 28.6003
  内存使用: 0.6423
  能量使用: 0.9928
  推理时间: 2.8570秒

批次 3:
  奖励值: 127.2626
  收益率: 0.4276
  距离: 31.3178
  内存使用: 0.6932
  能量使用: 1.0640
  推理时间: 2.8491秒

批次 4:
  奖励值: 123.7555
  收益率: 0.4203
  距离: 31.1327
  内存使用: 0.6471
  能量使用: 0.9092
  推理时间: 2.9032秒

批次 5:
  奖励值: 128.7458
  收益率: 0.4407
  距离: 31.9511
  内存使用: 0.6972
  能量使用: 1.0600
  推理时间: 2.9921秒

批次 6:
  奖励值: 122.2111
  收益率: 0.4214
  距离: 33.1503
  内存使用: 0.6349
  能量使用: 0.9925
  推理时间: 2.8277秒

批次 7:
  奖励值: 120.2267
  收益率: 0.4071
  距离: 33.4720
  内存使用: 0.7277
  能量使用: 0.9470
  推理时间: 2.8921秒

批次 8:
  奖励值: 124.6928
  收益率: 0.4192
  距离: 34.1246
  内存使用: 0.6609
  能量使用: 1.0289
  推理时间: 2.8246秒

批次 9:
  奖励值: 126.4515
  收益率: 0.4422
  距离: 31.4350
  内存使用: 0.6136
  能量使用: 1.0483
  推理时间: 2.7463秒

批次 10:
  奖励值: 120.8345
  收益率: 0.3985
  距离: 33.8078
  内存使用: 0.6946
  能量使用: 1.0286
  推理时间: 2.6241秒

批次 11:
  奖励值: 118.3927
  收益率: 0.4072
  距离: 28.4419
  内存使用: 0.6464
  能量使用: 0.9264
  推理时间: 2.6364秒

批次 12:
  奖励值: 113.3967
  收益率: 0.3765
  距离: 29.4433
  内存使用: 0.6157
  能量使用: 0.9475
  推理时间: 2.5484秒

批次 13:
  奖励值: 115.3004
  收益率: 0.3764
  距离: 28.6840
  内存使用: 0.6143
  能量使用: 0.9008
  推理时间: 2.4186秒

批次 14:
  奖励值: 122.8746
  收益率: 0.4239
  距离: 33.2940
  内存使用: 0.6823
  能量使用: 1.0745
  推理时间: 2.6144秒

批次 15:
  奖励值: 125.9139
  收益率: 0.4181
  距离: 29.9534
  内存使用: 0.6448
  能量使用: 0.9738
  推理时间: 2.5491秒

批次 16:
  奖励值: 124.1668
  收益率: 0.4081
  距离: 31.2058
  内存使用: 0.6628
  能量使用: 0.9977
  推理时间: 2.7006秒

批次 17:
  奖励值: 121.5988
  收益率: 0.3902
  距离: 29.8326
  内存使用: 0.6278
  能量使用: 0.9420
  推理时间: 2.6154秒

批次 18:
  奖励值: 100.0125
  收益率: 0.3417
  距离: 28.4733
  内存使用: 0.4841
  能量使用: 0.8175
  推理时间: 2.3791秒

批次 19:
  奖励值: 119.7403
  收益率: 0.3883
  距离: 29.7874
  内存使用: 0.6447
  能量使用: 0.8873
  推理时间: 2.9274秒

批次 20:
  奖励值: 125.8225
  收益率: 0.4214
  距离: 33.4159
  内存使用: 0.6852
  能量使用: 1.0538
  推理时间: 2.7116秒

批次 21:
  奖励值: 121.9407
  收益率: 0.3995
  距离: 34.0277
  内存使用: 0.6678
  能量使用: 1.0231
  推理时间: 2.7054秒

批次 22:
  奖励值: 120.5588
  收益率: 0.4073
  距离: 33.6850
  内存使用: 0.6317
  能量使用: 0.9656
  推理时间: 2.7601秒

批次 23:
  奖励值: 120.6051
  收益率: 0.3999
  距离: 29.7110
  内存使用: 0.7126
  能量使用: 0.9661
  推理时间: 2.7494秒

批次 24:
  奖励值: 115.5286
  收益率: 0.3916
  距离: 31.5949
  内存使用: 0.6328
  能量使用: 0.9151
  推理时间: 2.6630秒

批次 25:
  奖励值: 125.0493
  收益率: 0.4092
  距离: 34.3284
  内存使用: 0.7604
  能量使用: 1.0125
  推理时间: 2.7624秒

批次 26:
  奖励值: 128.5393
  收益率: 0.4374
  距离: 34.6459
  内存使用: 0.7736
  能量使用: 1.1019
  推理时间: 3.0508秒

批次 27:
  奖励值: 117.1415
  收益率: 0.3783
  距离: 29.3271
  内存使用: 0.5944
  能量使用: 0.9956
  推理时间: 2.7303秒

批次 28:
  奖励值: 119.2875
  收益率: 0.3900
  距离: 28.6527
  内存使用: 0.6744
  能量使用: 0.8980
  推理时间: 2.6829秒

批次 29:
  奖励值: 114.3470
  收益率: 0.3870
  距离: 32.2763
  内存使用: 0.6168
  能量使用: 0.8129
  推理时间: 2.6345秒

批次 30:
  奖励值: 134.1828
  收益率: 0.4306
  距离: 33.1919
  内存使用: 0.7592
  能量使用: 1.0833
  推理时间: 3.0174秒

批次 31:
  奖励值: 115.2828
  收益率: 0.3773
  距离: 29.0982
  内存使用: 0.5883
  能量使用: 0.9479
  推理时间: 2.8233秒

批次 32:
  奖励值: 127.3964
  收益率: 0.4133
  距离: 32.1626
  内存使用: 0.6537
  能量使用: 0.9910
  推理时间: 2.7788秒

批次 33:
  奖励值: 128.3750
  收益率: 0.4276
  距离: 32.5182
  内存使用: 0.7254
  能量使用: 1.0063
  推理时间: 2.7889秒

批次 34:
  奖励值: 119.9403
  收益率: 0.4081
  距离: 28.9647
  内存使用: 0.6033
  能量使用: 0.9515
  推理时间: 2.5853秒

批次 35:
  奖励值: 114.6832
  收益率: 0.3873
  距离: 32.3660
  内存使用: 0.8977
  能量使用: 0.9698
  推理时间: 2.5811秒

批次 36:
  奖励值: 134.4912
  收益率: 0.4528
  距离: 34.5906
  内存使用: 0.7224
  能量使用: 1.1046
  推理时间: 3.0105秒

批次 37:
  奖励值: 121.6442
  收益率: 0.4099
  距离: 29.5529
  内存使用: 0.6962
  能量使用: 1.0624
  推理时间: 2.4757秒

批次 38:
  奖励值: 124.3314
  收益率: 0.4118
  距离: 32.0479
  内存使用: 0.6488
  能量使用: 1.0406
  推理时间: 2.8907秒

批次 39:
  奖励值: 122.0039
  收益率: 0.3985
  距离: 25.9276
  内存使用: 0.6963
  能量使用: 0.9684
  推理时间: 2.6612秒

批次 40:
  奖励值: 125.9680
  收益率: 0.4230
  距离: 32.4363
  内存使用: 0.6457
  能量使用: 0.9820
  推理时间: 2.8282秒

批次 41:
  奖励值: 117.7580
  收益率: 0.3963
  距离: 28.0044
  内存使用: 0.6745
  能量使用: 1.0051
  推理时间: 2.6069秒

批次 42:
  奖励值: 126.5346
  收益率: 0.4020
  距离: 31.8495
  内存使用: 0.7193
  能量使用: 0.9621
  推理时间: 2.8682秒

批次 43:
  奖励值: 120.2921
  收益率: 0.3996
  距离: 30.8043
  内存使用: 0.6545
  能量使用: 0.9569
  推理时间: 2.6228秒

批次 44:
  奖励值: 120.6036
  收益率: 0.3952
  距离: 30.0198
  内存使用: 0.6085
  能量使用: 0.9645
  推理时间: 2.8532秒

批次 45:
  奖励值: 128.1978
  收益率: 0.4310
  距离: 30.6895
  内存使用: 0.6521
  能量使用: 1.0629
  推理时间: 2.9264秒

批次 46:
  奖励值: 108.4437
  收益率: 0.3564
  距离: 27.9037
  内存使用: 0.6166
  能量使用: 0.7923
  推理时间: 2.5530秒

批次 47:
  奖励值: 121.4953
  收益率: 0.3999
  距离: 29.7041
  内存使用: 0.6273
  能量使用: 0.8780
  推理时间: 2.7348秒

批次 48:
  奖励值: 119.4163
  收益率: 0.4017
  距离: 30.2000
  内存使用: 0.6308
  能量使用: 0.9366
  推理时间: 2.7121秒

批次 49:
  奖励值: 120.3359
  收益率: 0.4026
  距离: 32.5457
  内存使用: 0.6842
  能量使用: 0.8983
  推理时间: 3.0128秒

批次 50:
  奖励值: 127.3687
  收益率: 0.4121
  距离: 32.1359
  内存使用: 0.7089
  能量使用: 1.0177
  推理时间: 3.0319秒

批次 51:
  奖励值: 118.7102
  收益率: 0.4116
  距离: 28.6060
  内存使用: 0.5883
  能量使用: 0.9853
  推理时间: 2.8574秒

批次 52:
  奖励值: 128.0195
  收益率: 0.4206
  距离: 31.5155
  内存使用: 0.7317
  能量使用: 1.1138
  推理时间: 2.8165秒

批次 53:
  奖励值: 112.3911
  收益率: 0.3770
  距离: 27.2156
  内存使用: 0.5496
  能量使用: 0.8526
  推理时间: 2.5833秒

批次 54:
  奖励值: 119.4321
  收益率: 0.4080
  距离: 33.4512
  内存使用: 0.6843
  能量使用: 1.0002
  推理时间: 2.8859秒

批次 55:
  奖励值: 118.3501
  收益率: 0.4123
  距离: 34.9288
  内存使用: 0.6330
  能量使用: 0.9378
  推理时间: 2.8971秒

批次 56:
  奖励值: 115.0360
  收益率: 0.3916
  距离: 30.9834
  内存使用: 0.6643
  能量使用: 0.8803
  推理时间: 2.6817秒

批次 57:
  奖励值: 125.2119
  收益率: 0.4143
  距离: 31.7553
  内存使用: 0.6640
  能量使用: 0.9569
  推理时间: 2.9788秒

批次 58:
  奖励值: 118.5070
  收益率: 0.3882
  距离: 31.1799
  内存使用: 0.6775
  能量使用: 0.9286
  推理时间: 2.7077秒

批次 59:
  奖励值: 116.6104
  收益率: 0.3860
  距离: 30.7037
  内存使用: 0.6525
  能量使用: 1.0228
  推理时间: 2.7531秒

批次 60:
  奖励值: 120.9968
  收益率: 0.4276
  距离: 35.6188
  内存使用: 0.6140
  能量使用: 0.9736
  推理时间: 3.0556秒

批次 61:
  奖励值: 112.3799
  收益率: 0.3834
  距离: 34.5088
  内存使用: 0.5866
  能量使用: 0.9447
  推理时间: 2.8181秒

批次 62:
  奖励值: 117.6917
  收益率: 0.3985
  距离: 31.6111
  内存使用: 0.6478
  能量使用: 0.9578
  推理时间: 2.7373秒

批次 63:
  奖励值: 121.9961
  收益率: 0.4192
  距离: 33.8764
  内存使用: 0.6978
  能量使用: 1.0337
  推理时间: 2.8078秒

批次 64:
  奖励值: 122.1047
  收益率: 0.4127
  距离: 35.7768
  内存使用: 0.6788
  能量使用: 0.9350
  推理时间: 2.7497秒

批次 65:
  奖励值: 126.0998
  收益率: 0.4332
  距离: 30.9753
  内存使用: 0.6043
  能量使用: 1.0209
  推理时间: 2.6993秒

批次 66:
  奖励值: 119.9115
  收益率: 0.4100
  距离: 33.2010
  内存使用: 0.6481
  能量使用: 0.9279
  推理时间: 2.6616秒

批次 67:
  奖励值: 117.3232
  收益率: 0.3980
  距离: 29.5811
  内存使用: 0.5979
  能量使用: 0.9746
  推理时间: 2.6163秒

批次 68:
  奖励值: 110.8224
  收益率: 0.3696
  距离: 30.6304
  内存使用: 0.6318
  能量使用: 0.8930
  推理时间: 2.5243秒

批次 69:
  奖励值: 117.8427
  收益率: 0.3845
  距离: 28.6486
  内存使用: 0.6570
  能量使用: 1.0090
  推理时间: 2.5884秒

批次 70:
  奖励值: 115.7418
  收益率: 0.3920
  距离: 31.5011
  内存使用: 0.6389
  能量使用: 0.9764
  推理时间: 2.5838秒

批次 71:
  奖励值: 108.7181
  收益率: 0.3744
  距离: 33.1005
  内存使用: 0.5335
  能量使用: 0.9298
  推理时间: 2.4763秒

批次 72:
  奖励值: 121.3335
  收益率: 0.3976
  距离: 30.3993
  内存使用: 0.6341
  能量使用: 0.8995
  推理时间: 3.3455秒

批次 73:
  奖励值: 126.5450
  收益率: 0.4230
  距离: 33.8633
  内存使用: 0.7204
  能量使用: 0.9263
  推理时间: 2.9442秒

批次 74:
  奖励值: 116.0291
  收益率: 0.4008
  距离: 35.1567
  内存使用: 0.7031
  能量使用: 0.8821
  推理时间: 2.9307秒

批次 75:
  奖励值: 120.0456
  收益率: 0.4120
  距离: 33.5380
  内存使用: 0.7641
  能量使用: 1.0066
  推理时间: 3.0155秒

批次 76:
  奖励值: 120.0649
  收益率: 0.4044
  距离: 31.7156
  内存使用: 0.6384
  能量使用: 0.9161
  推理时间: 3.2626秒

批次 77:
  奖励值: 129.5008
  收益率: 0.4213
  距离: 32.1362
  内存使用: 0.6490
  能量使用: 1.0131
  推理时间: 3.0033秒

批次 78:
  奖励值: 126.5796
  收益率: 0.4120
  距离: 31.7496
  内存使用: 0.6959
  能量使用: 0.9953
  推理时间: 2.8293秒

批次 79:
  奖励值: 121.9890
  收益率: 0.4032
  距离: 34.0841
  内存使用: 0.6865
  能量使用: 1.0513
  推理时间: 2.8344秒

批次 80:
  奖励值: 116.0996
  收益率: 0.3906
  距离: 29.0233
  内存使用: 0.6664
  能量使用: 0.9111
  推理时间: 2.7835秒

批次 81:
  奖励值: 111.2661
  收益率: 0.3844
  距离: 28.3686
  内存使用: 0.6049
  能量使用: 0.8944
  推理时间: 2.3975秒

批次 82:
  奖励值: 119.2103
  收益率: 0.4030
  距离: 30.5862
  内存使用: 0.6689
  能量使用: 0.9242
  推理时间: 2.7133秒

批次 83:
  奖励值: 118.1310
  收益率: 0.4042
  距离: 29.1607
  内存使用: 0.5882
  能量使用: 0.9891
  推理时间: 2.6667秒

批次 84:
  奖励值: 116.9604
  收益率: 0.3855
  距离: 30.6051
  内存使用: 0.6995
  能量使用: 0.9746
  推理时间: 2.8846秒

批次 85:
  奖励值: 116.3845
  收益率: 0.3949
  距离: 30.7013
  内存使用: 0.6252
  能量使用: 0.9044
  推理时间: 2.5911秒

批次 86:
  奖励值: 126.8601
  收益率: 0.4266
  距离: 34.5632
  内存使用: 0.6620
  能量使用: 1.0546
  推理时间: 2.8082秒

批次 87:
  奖励值: 129.7614
  收益率: 0.4339
  距离: 34.3518
  内存使用: 0.6907
  能量使用: 1.0345
  推理时间: 2.6830秒

批次 88:
  奖励值: 127.0182
  收益率: 0.4203
  距离: 31.3026
  内存使用: 0.6535
  能量使用: 1.0074
  推理时间: 2.7402秒

批次 89:
  奖励值: 114.0075
  收益率: 0.3843
  距离: 27.9152
  内存使用: 0.5564
  能量使用: 0.9134
  推理时间: 2.5064秒

批次 90:
  奖励值: 117.0536
  收益率: 0.3966
  距离: 32.5413
  内存使用: 0.6429
  能量使用: 0.9087
  推理时间: 2.5041秒

批次 91:
  奖励值: 118.9529
  收益率: 0.3956
  距离: 34.2588
  内存使用: 0.6973
  能量使用: 0.9381
  推理时间: 3.0099秒

批次 92:
  奖励值: 128.8695
  收益率: 0.4148
  距离: 33.5503
  内存使用: 0.7030
  能量使用: 1.0078
  推理时间: 2.7886秒

批次 93:
  奖励值: 119.9991
  收益率: 0.3869
  距离: 30.8003
  内存使用: 0.6382
  能量使用: 0.9633
  推理时间: 2.6596秒

批次 94:
  奖励值: 120.6101
  收益率: 0.4210
  距离: 30.4002
  内存使用: 0.6366
  能量使用: 0.9429
  推理时间: 2.7182秒

批次 95:
  奖励值: 124.5914
  收益率: 0.4210
  距离: 30.3841
  内存使用: 0.6676
  能量使用: 0.9654
  推理时间: 2.7305秒

批次 96:
  奖励值: 123.9691
  收益率: 0.4195
  距离: 31.9456
  内存使用: 0.6976
  能量使用: 1.0185
  推理时间: 2.7281秒

批次 97:
  奖励值: 118.2882
  收益率: 0.4014
  距离: 29.0857
  内存使用: 0.6460
  能量使用: 0.8918
  推理时间: 2.6569秒

批次 98:
  奖励值: 113.0295
  收益率: 0.3764
  距离: 27.8338
  内存使用: 0.6186
  能量使用: 0.8885
  推理时间: 2.5365秒

批次 99:
  奖励值: 119.0061
  收益率: 0.3873
  距离: 30.3640
  内存使用: 0.6681
  能量使用: 0.9557
  推理时间: 2.6522秒

批次 100:
  奖励值: 127.5147
  收益率: 0.4155
  距离: 33.2780
  内存使用: 0.6918
  能量使用: 1.0008
  推理时间: 2.8469秒


==================== 总结 ====================
平均收益率: 0.4041
平均能量使用: 0.9680
平均推理时间: 2.7572秒
