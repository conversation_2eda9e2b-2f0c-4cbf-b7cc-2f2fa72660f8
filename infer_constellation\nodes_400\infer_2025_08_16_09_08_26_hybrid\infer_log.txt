推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 79.5571
  收益率: 0.4904
  距离: 23.5297
  内存使用: 0.3281
  能量使用: 0.6280
  推理时间: 1.6776秒

批次 2:
  奖励值: 69.9569
  收益率: 0.4343
  距离: 16.0743
  内存使用: 0.2594
  能量使用: 0.5526
  推理时间: 1.4773秒

批次 3:
  奖励值: 62.0023
  收益率: 0.4024
  距离: 14.7193
  内存使用: 0.2181
  能量使用: 0.4579
  推理时间: 1.3053秒

批次 4:
  奖励值: 74.6746
  收益率: 0.4753
  距离: 21.0592
  内存使用: 0.2382
  能量使用: 0.5534
  推理时间: 1.5971秒

批次 5:
  奖励值: 68.1581
  收益率: 0.4242
  距离: 16.8696
  内存使用: 0.2569
  能量使用: 0.5573
  推理时间: 1.4740秒

批次 6:
  奖励值: 78.0928
  收益率: 0.4603
  距离: 17.9579
  内存使用: 0.2749
  能量使用: 0.5815
  推理时间: 1.6041秒

批次 7:
  奖励值: 65.5622
  收益率: 0.4204
  距离: 18.1843
  内存使用: 0.2389
  能量使用: 0.4524
  推理时间: 1.4171秒

批次 8:
  奖励值: 77.4311
  收益率: 0.4687
  距离: 17.9323
  内存使用: 0.3374
  能量使用: 0.6681
  推理时间: 1.5986秒

批次 9:
  奖励值: 69.1294
  收益率: 0.4284
  距离: 14.7898
  内存使用: 0.2200
  能量使用: 0.5315
  推理时间: 1.4626秒

批次 10:
  奖励值: 69.5953
  收益率: 0.4390
  距离: 20.5606
  内存使用: 0.3183
  能量使用: 0.5305
  推理时间: 1.5064秒

批次 11:
  奖励值: 76.3165
  收益率: 0.4606
  距离: 20.8137
  内存使用: 0.3434
  能量使用: 0.6099
  推理时间: 1.5915秒

批次 12:
  奖励值: 73.6299
  收益率: 0.4604
  距离: 20.5178
  内存使用: 0.2466
  能量使用: 0.5758
  推理时间: 1.5830秒

批次 13:
  奖励值: 68.8637
  收益率: 0.4375
  距离: 20.3167
  内存使用: 0.2414
  能量使用: 0.5145
  推理时间: 1.4767秒

批次 14:
  奖励值: 75.9643
  收益率: 0.4759
  距离: 20.9856
  内存使用: 0.3114
  能量使用: 0.5745
  推理时间: 1.6148秒

批次 15:
  奖励值: 70.6170
  收益率: 0.4503
  距离: 19.3176
  内存使用: 0.2624
  能量使用: 0.5860
  推理时间: 1.5627秒

批次 16:
  奖励值: 63.4785
  收益率: 0.4159
  距离: 17.1695
  内存使用: 0.1584
  能量使用: 0.5151
  推理时间: 1.3401秒

批次 17:
  奖励值: 75.5018
  收益率: 0.4638
  距离: 19.7687
  内存使用: 0.2345
  能量使用: 0.5932
  推理时间: 1.5902秒

批次 18:
  奖励值: 69.8947
  收益率: 0.4534
  距离: 16.2489
  内存使用: 0.2396
  能量使用: 0.5221
  推理时间: 1.4288秒

批次 19:
  奖励值: 74.7732
  收益率: 0.4540
  距离: 18.2209
  内存使用: 0.2890
  能量使用: 0.5437
  推理时间: 1.4821秒

批次 20:
  奖励值: 72.1483
  收益率: 0.4645
  距离: 15.6590
  内存使用: 0.2642
  能量使用: 0.5802
  推理时间: 1.4782秒

批次 21:
  奖励值: 71.7915
  收益率: 0.4537
  距离: 17.1546
  内存使用: 0.2830
  能量使用: 0.5795
  推理时间: 1.4966秒

批次 22:
  奖励值: 79.4395
  收益率: 0.4878
  距离: 20.1889
  内存使用: 0.3226
  能量使用: 0.6096
  推理时间: 1.6264秒

批次 23:
  奖励值: 76.3037
  收益率: 0.4649
  距离: 19.8807
  内存使用: 0.3136
  能量使用: 0.5984
  推理时间: 1.5642秒

批次 24:
  奖励值: 76.7891
  收益率: 0.4733
  距离: 17.3032
  内存使用: 0.3015
  能量使用: 0.6172
  推理时间: 1.5871秒

批次 25:
  奖励值: 73.0452
  收益率: 0.4570
  距离: 19.0999
  内存使用: 0.2386
  能量使用: 0.5711
  推理时间: 1.5437秒

批次 26:
  奖励值: 72.1776
  收益率: 0.4451
  距离: 16.0682
  内存使用: 0.2422
  能量使用: 0.5345
  推理时间: 1.5098秒

批次 27:
  奖励值: 76.9218
  收益率: 0.4613
  距离: 17.8925
  内存使用: 0.2802
  能量使用: 0.5837
  推理时间: 1.6127秒

批次 28:
  奖励值: 70.6508
  收益率: 0.4549
  距离: 17.0460
  内存使用: 0.2437
  能量使用: 0.6085
  推理时间: 1.4738秒

批次 29:
  奖励值: 67.6856
  收益率: 0.4325
  距离: 18.8920
  内存使用: 0.2277
  能量使用: 0.5090
  推理时间: 1.4403秒

批次 30:
  奖励值: 83.3340
  收益率: 0.5045
  距离: 22.9509
  内存使用: 0.3184
  能量使用: 0.6626
  推理时间: 1.7555秒

批次 31:
  奖励值: 68.6063
  收益率: 0.4325
  距离: 17.7282
  内存使用: 0.2989
  能量使用: 0.5603
  推理时间: 1.4046秒

批次 32:
  奖励值: 74.8786
  收益率: 0.4548
  距离: 17.6167
  内存使用: 0.2568
  能量使用: 0.5693
  推理时间: 1.5353秒

批次 33:
  奖励值: 70.3278
  收益率: 0.4487
  距离: 19.3570
  内存使用: 0.2216
  能量使用: 0.5658
  推理时间: 1.4843秒

批次 34:
  奖励值: 73.8287
  收益率: 0.4619
  距离: 18.6770
  内存使用: 0.3254
  能量使用: 0.5401
  推理时间: 1.5387秒

批次 35:
  奖励值: 72.6010
  收益率: 0.4495
  距离: 18.4275
  内存使用: 0.3517
  能量使用: 0.6187
  推理时间: 1.5378秒

批次 36:
  奖励值: 73.5550
  收益率: 0.4602
  距离: 18.4425
  内存使用: 0.2759
  能量使用: 0.5716
  推理时间: 1.5805秒

批次 37:
  奖励值: 65.8330
  收益率: 0.4264
  距离: 16.5273
  内存使用: 0.2629
  能量使用: 0.5438
  推理时间: 1.4397秒

批次 38:
  奖励值: 68.7324
  收益率: 0.4301
  距离: 18.7721
  内存使用: 0.2112
  能量使用: 0.5519
  推理时间: 1.4659秒

批次 39:
  奖励值: 75.9997
  收益率: 0.4798
  距离: 17.8961
  内存使用: 0.3040
  能量使用: 0.6006
  推理时间: 1.6037秒

批次 40:
  奖励值: 71.5392
  收益率: 0.4542
  距离: 16.8578
  内存使用: 0.2973
  能量使用: 0.5665
  推理时间: 1.5376秒

批次 41:
  奖励值: 62.7737
  收益率: 0.4084
  距离: 18.1845
  内存使用: 0.1492
  能量使用: 0.5189
  推理时间: 1.3397秒

批次 42:
  奖励值: 79.7960
  收益率: 0.4817
  距离: 21.4739
  内存使用: 0.3200
  能量使用: 0.6141
  推理时间: 1.6697秒

批次 43:
  奖励值: 77.1567
  收益率: 0.4619
  距离: 18.2057
  内存使用: 0.3212
  能量使用: 0.5597
  推理时间: 1.6591秒

批次 44:
  奖励值: 76.8803
  收益率: 0.4844
  距离: 19.3006
  内存使用: 0.2925
  能量使用: 0.6462
  推理时间: 1.6140秒

批次 45:
  奖励值: 66.3928
  收益率: 0.4149
  距离: 15.2596
  内存使用: 0.1899
  能量使用: 0.5649
  推理时间: 1.3507秒

批次 46:
  奖励值: 74.8895
  收益率: 0.4765
  距离: 23.8185
  内存使用: 0.3244
  能量使用: 0.6780
  推理时间: 1.6540秒

批次 47:
  奖励值: 67.3666
  收益率: 0.4197
  距离: 15.5756
  内存使用: 0.2208
  能量使用: 0.4702
  推理时间: 1.3674秒

批次 48:
  奖励值: 77.9151
  收益率: 0.4756
  距离: 18.4685
  内存使用: 0.3074
  能量使用: 0.5939
  推理时间: 1.6510秒

批次 49:
  奖励值: 67.8541
  收益率: 0.4431
  距离: 19.2881
  内存使用: 0.2616
  能量使用: 0.5421
  推理时间: 1.4286秒

批次 50:
  奖励值: 77.8298
  收益率: 0.4715
  距离: 19.0440
  内存使用: 0.3181
  能量使用: 0.5784
  推理时间: 1.6130秒

批次 51:
  奖励值: 74.0494
  收益率: 0.4593
  距离: 17.8650
  内存使用: 0.2685
  能量使用: 0.5678
  推理时间: 1.5457秒

批次 52:
  奖励值: 69.2079
  收益率: 0.4540
  距离: 19.7873
  内存使用: 0.2512
  能量使用: 0.5458
  推理时间: 1.4695秒

批次 53:
  奖励值: 67.7966
  收益率: 0.4330
  距离: 16.0818
  内存使用: 0.2377
  能量使用: 0.4873
  推理时间: 1.4134秒

批次 54:
  奖励值: 74.1448
  收益率: 0.4521
  距离: 18.7643
  内存使用: 0.3161
  能量使用: 0.6299
  推理时间: 1.5291秒

批次 55:
  奖励值: 70.2751
  收益率: 0.4191
  距离: 17.5692
  内存使用: 0.2939
  能量使用: 0.5867
  推理时间: 1.4732秒

批次 56:
  奖励值: 76.0975
  收益率: 0.4804
  距离: 18.8891
  内存使用: 0.2816
  能量使用: 0.6344
  推理时间: 1.6056秒

批次 57:
  奖励值: 70.9330
  收益率: 0.4472
  距离: 19.9503
  内存使用: 0.2301
  能量使用: 0.5537
  推理时间: 1.4603秒

批次 58:
  奖励值: 79.4898
  收益率: 0.5065
  距离: 18.0506
  内存使用: 0.3564
  能量使用: 0.6135
  推理时间: 1.6543秒

批次 59:
  奖励值: 72.3321
  收益率: 0.4675
  距离: 21.0661
  内存使用: 0.2964
  能量使用: 0.5734
  推理时间: 1.5930秒

批次 60:
  奖励值: 74.9599
  收益率: 0.4694
  距离: 19.2221
  内存使用: 0.2401
  能量使用: 0.6181
  推理时间: 1.5741秒

批次 61:
  奖励值: 73.4839
  收益率: 0.4549
  距离: 19.7878
  内存使用: 0.2923
  能量使用: 0.5521
  推理时间: 1.5627秒

批次 62:
  奖励值: 71.9670
  收益率: 0.4427
  距离: 19.8136
  内存使用: 0.3201
  能量使用: 0.5322
  推理时间: 1.5373秒

批次 63:
  奖励值: 79.5894
  收益率: 0.4777
  距离: 20.0670
  内存使用: 0.3317
  能量使用: 0.6227
  推理时间: 1.6785秒

批次 64:
  奖励值: 74.3282
  收益率: 0.4769
  距离: 17.8816
  内存使用: 0.2894
  能量使用: 0.5727
  推理时间: 1.5664秒

批次 65:
  奖励值: 69.9895
  收益率: 0.4417
  距离: 17.4891
  内存使用: 0.1954
  能量使用: 0.5363
  推理时间: 1.4530秒

批次 66:
  奖励值: 80.5050
  收益率: 0.4885
  距离: 20.8254
  内存使用: 0.3493
  能量使用: 0.6153
  推理时间: 1.6477秒

批次 67:
  奖励值: 80.8666
  收益率: 0.4763
  距离: 19.9938
  内存使用: 0.2778
  能量使用: 0.5953
  推理时间: 1.6900秒

批次 68:
  奖励值: 74.3065
  收益率: 0.4497
  距离: 18.5639
  内存使用: 0.2480
  能量使用: 0.5750
  推理时间: 1.4976秒

批次 69:
  奖励值: 65.1143
  收益率: 0.4230
  距离: 17.4050
  内存使用: 0.2014
  能量使用: 0.5650
  推理时间: 1.3983秒

批次 70:
  奖励值: 68.5418
  收益率: 0.4325
  距离: 17.1052
  内存使用: 0.2635
  能量使用: 0.5476
  推理时间: 1.4718秒

批次 71:
  奖励值: 71.1115
  收益率: 0.4330
  距离: 16.5883
  内存使用: 0.2472
  能量使用: 0.5414
  推理时间: 1.4463秒

批次 72:
  奖励值: 74.6184
  收益率: 0.4351
  距离: 16.7200
  内存使用: 0.2418
  能量使用: 0.6013
  推理时间: 1.4855秒

批次 73:
  奖励值: 68.9553
  收益率: 0.4202
  距离: 16.7377
  内存使用: 0.2293
  能量使用: 0.5224
  推理时间: 1.4241秒

批次 74:
  奖励值: 65.4472
  收益率: 0.4278
  距离: 17.1104
  内存使用: 0.2105
  能量使用: 0.5847
  推理时间: 1.4426秒

批次 75:
  奖励值: 76.5037
  收益率: 0.4631
  距离: 19.3398
  内存使用: 0.2894
  能量使用: 0.5635
  推理时间: 1.5885秒

批次 76:
  奖励值: 71.4373
  收益率: 0.4459
  距离: 20.3039
  内存使用: 0.2523
  能量使用: 0.5691
  推理时间: 1.5050秒

批次 77:
  奖励值: 74.1806
  收益率: 0.4534
  距离: 18.0385
  内存使用: 0.2193
  能量使用: 0.5811
  推理时间: 1.5084秒

批次 78:
  奖励值: 73.6982
  收益率: 0.4584
  距离: 18.0154
  内存使用: 0.2449
  能量使用: 0.5432
  推理时间: 1.5107秒

批次 79:
  奖励值: 72.2380
  收益率: 0.4416
  距离: 18.8381
  内存使用: 0.3121
  能量使用: 0.5739
  推理时间: 1.5185秒

批次 80:
  奖励值: 74.3036
  收益率: 0.4704
  距离: 20.7084
  内存使用: 0.3304
  能量使用: 0.5540
  推理时间: 1.6277秒

批次 81:
  奖励值: 69.4431
  收益率: 0.4245
  距离: 15.5027
  内存使用: 0.2797
  能量使用: 0.5314
  推理时间: 1.4392秒

批次 82:
  奖励值: 70.7633
  收益率: 0.4476
  距离: 17.3105
  内存使用: 0.2692
  能量使用: 0.4937
  推理时间: 1.4932秒

批次 83:
  奖励值: 72.2203
  收益率: 0.4470
  距离: 18.3634
  内存使用: 0.2493
  能量使用: 0.5468
  推理时间: 1.5575秒

批次 84:
  奖励值: 69.6445
  收益率: 0.4514
  距离: 18.8829
  内存使用: 0.2344
  能量使用: 0.5937
  推理时间: 1.5180秒

批次 85:
  奖励值: 78.2065
  收益率: 0.4849
  距离: 18.8994
  内存使用: 0.3093
  能量使用: 0.5784
  推理时间: 1.6360秒

批次 86:
  奖励值: 70.5957
  收益率: 0.4506
  距离: 18.3842
  内存使用: 0.2441
  能量使用: 0.5804
  推理时间: 1.5111秒

批次 87:
  奖励值: 79.7679
  收益率: 0.4894
  距离: 18.9207
  内存使用: 0.3145
  能量使用: 0.6290
  推理时间: 1.6149秒

批次 88:
  奖励值: 71.8566
  收益率: 0.4526
  距离: 21.4319
  内存使用: 0.2473
  能量使用: 0.6076
  推理时间: 1.5702秒

批次 89:
  奖励值: 72.5630
  收益率: 0.4462
  距离: 19.1210
  内存使用: 0.2417
  能量使用: 0.5113
  推理时间: 1.5074秒

批次 90:
  奖励值: 74.1017
  收益率: 0.4564
  距离: 18.2982
  内存使用: 0.2675
  能量使用: 0.6308
  推理时间: 1.5936秒

批次 91:
  奖励值: 68.7348
  收益率: 0.4424
  距离: 18.2336
  内存使用: 0.3007
  能量使用: 0.5961
  推理时间: 1.5196秒

批次 92:
  奖励值: 71.6734
  收益率: 0.4574
  距离: 17.8131
  内存使用: 0.2716
  能量使用: 0.5684
  推理时间: 1.4395秒

批次 93:
  奖励值: 74.0401
  收益率: 0.4613
  距离: 19.3227
  内存使用: 0.2555
  能量使用: 0.5415
  推理时间: 1.5297秒

批次 94:
  奖励值: 76.6090
  收益率: 0.4678
  距离: 21.3881
  内存使用: 0.3476
  能量使用: 0.6463
  推理时间: 1.5866秒

批次 95:
  奖励值: 82.6062
  收益率: 0.5057
  距离: 20.8603
  内存使用: 0.3230
  能量使用: 0.6043
  推理时间: 1.6611秒

批次 96:
  奖励值: 68.0867
  收益率: 0.4341
  距离: 18.5202
  内存使用: 0.2339
  能量使用: 0.4726
  推理时间: 1.3920秒

批次 97:
  奖励值: 78.6479
  收益率: 0.4694
  距离: 16.3946
  内存使用: 0.3265
  能量使用: 0.5832
  推理时间: 1.5720秒

批次 98:
  奖励值: 75.8450
  收益率: 0.4853
  距离: 22.5497
  内存使用: 0.3643
  能量使用: 0.5959
  推理时间: 1.6364秒

批次 99:
  奖励值: 68.1498
  收益率: 0.4398
  距离: 17.0860
  内存使用: 0.2795
  能量使用: 0.5349
  推理时间: 1.4867秒

批次 100:
  奖励值: 78.3547
  收益率: 0.4820
  距离: 19.5285
  内存使用: 0.3202
  能量使用: 0.6421
  推理时间: 1.6194秒


==================== 总结 ====================
平均收益率: 0.4545
平均能量使用: 0.5711
平均推理时间: 1.5312秒
