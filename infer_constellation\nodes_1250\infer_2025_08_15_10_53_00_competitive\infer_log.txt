推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 131.2334
  收益率: 0.2675
  距离: 31.1311
  内存使用: 0.6050
  能量使用: 0.9969
  推理时间: 2.5435秒

批次 2:
  奖励值: 122.9797
  收益率: 0.2455
  距离: 31.4457
  内存使用: 0.8964
  能量使用: 1.0986
  推理时间: 2.4488秒

批次 3:
  奖励值: 126.1345
  收益率: 0.2488
  距离: 28.3583
  内存使用: 0.6484
  能量使用: 0.9242
  推理时间: 2.4042秒

批次 4:
  奖励值: 123.9572
  收益率: 0.2466
  距离: 29.5162
  内存使用: 0.5813
  能量使用: 0.9882
  推理时间: 2.3510秒

批次 5:
  奖励值: 126.2253
  收益率: 0.2476
  距离: 29.5748
  内存使用: 0.6959
  能量使用: 0.8786
  推理时间: 2.6056秒

批次 6:
  奖励值: 120.4912
  收益率: 0.2375
  距离: 29.1875
  内存使用: 0.5754
  能量使用: 0.8548
  推理时间: 2.3397秒

批次 7:
  奖励值: 126.1322
  收益率: 0.2523
  距离: 30.6351
  内存使用: 0.6727
  能量使用: 1.0277
  推理时间: 2.6904秒

批次 8:
  奖励值: 126.6944
  收益率: 0.2508
  距离: 28.6472
  内存使用: 0.6304
  能量使用: 0.9335
  推理时间: 2.7288秒

批次 9:
  奖励值: 118.6135
  收益率: 0.2390
  距离: 31.7654
  内存使用: 0.8991
  能量使用: 0.9086
  推理时间: 2.4656秒

批次 10:
  奖励值: 118.6996
  收益率: 0.2380
  距离: 30.1206
  内存使用: 0.8887
  能量使用: 0.9915
  推理时间: 2.4181秒

批次 11:
  奖励值: 132.7024
  收益率: 0.2681
  距离: 33.8431
  内存使用: 0.6963
  能量使用: 1.0558
  推理时间: 2.5817秒

批次 12:
  奖励值: 118.0604
  收益率: 0.2297
  距离: 28.3465
  内存使用: 0.5906
  能量使用: 0.9421
  推理时间: 2.3378秒

批次 13:
  奖励值: 117.4841
  收益率: 0.2352
  距离: 26.5747
  内存使用: 0.5670
  能量使用: 0.8900
  推理时间: 2.2819秒

批次 14:
  奖励值: 113.8456
  收益率: 0.2270
  距离: 30.1445
  内存使用: 0.6415
  能量使用: 0.9227
  推理时间: 2.1990秒

批次 15:
  奖励值: 126.9789
  收益率: 0.2573
  距离: 35.1688
  内存使用: 0.6623
  能量使用: 1.0111
  推理时间: 2.5850秒

批次 16:
  奖励值: 120.5028
  收益率: 0.2409
  距离: 27.4483
  内存使用: 0.6193
  能量使用: 0.9266
  推理时间: 2.6196秒

批次 17:
  奖励值: 128.4924
  收益率: 0.2626
  距离: 33.8087
  内存使用: 0.6202
  能量使用: 0.9863
  推理时间: 2.6727秒

批次 18:
  奖励值: 120.7541
  收益率: 0.2431
  距离: 34.9792
  内存使用: 0.6966
  能量使用: 1.0159
  推理时间: 2.6601秒

批次 19:
  奖励值: 116.8046
  收益率: 0.2260
  距离: 28.9729
  内存使用: 0.8773
  能量使用: 0.9405
  推理时间: 2.3862秒

批次 20:
  奖励值: 121.0910
  收益率: 0.2406
  距离: 31.0518
  内存使用: 0.5599
  能量使用: 1.0062
  推理时间: 2.6345秒

批次 21:
  奖励值: 125.3775
  收益率: 0.2536
  距离: 33.3168
  内存使用: 0.6946
  能量使用: 1.0337
  推理时间: 2.5164秒

批次 22:
  奖励值: 129.1142
  收益率: 0.2518
  距离: 30.4389
  内存使用: 0.6566
  能量使用: 0.8762
  推理时间: 2.6287秒

批次 23:
  奖励值: 131.1623
  收益率: 0.2642
  距离: 32.8733
  内存使用: 0.7284
  能量使用: 1.0735
  推理时间: 2.6285秒

批次 24:
  奖励值: 120.6754
  收益率: 0.2409
  距离: 31.0978
  内存使用: 0.5942
  能量使用: 0.9572
  推理时间: 2.4818秒

批次 25:
  奖励值: 112.7922
  收益率: 0.2273
  距离: 29.0093
  内存使用: 0.5822
  能量使用: 0.8240
  推理时间: 2.1983秒

批次 26:
  奖励值: 115.1345
  收益率: 0.2306
  距离: 27.8068
  内存使用: 0.6642
  能量使用: 0.8599
  推理时间: 2.4925秒

批次 27:
  奖励值: 121.5585
  收益率: 0.2439
  距离: 30.1098
  内存使用: 0.6165
  能量使用: 0.9566
  推理时间: 2.3456秒

批次 28:
  奖励值: 118.7340
  收益率: 0.2300
  距离: 27.5804
  内存使用: 0.8993
  能量使用: 0.8522
  推理时间: 2.4943秒

批次 29:
  奖励值: 115.9638
  收益率: 0.2336
  距离: 31.1663
  内存使用: 0.8908
  能量使用: 0.8153
  推理时间: 2.4760秒

批次 30:
  奖励值: 127.9199
  收益率: 0.2551
  距离: 32.4660
  内存使用: 0.6470
  能量使用: 1.0471
  推理时间: 2.9410秒

批次 31:
  奖励值: 128.8758
  收益率: 0.2612
  距离: 30.1343
  内存使用: 0.5774
  能量使用: 0.9366
  推理时间: 2.7343秒

批次 32:
  奖励值: 118.3432
  收益率: 0.2352
  距离: 28.6416
  内存使用: 0.8967
  能量使用: 0.9753
  推理时间: 2.2770秒

批次 33:
  奖励值: 119.9572
  收益率: 0.2409
  距离: 32.7448
  内存使用: 0.8286
  能量使用: 0.9122
  推理时间: 2.2684秒

批次 34:
  奖励值: 129.8776
  收益率: 0.2613
  距离: 31.0325
  内存使用: 0.6074
  能量使用: 1.0049
  推理时间: 2.5147秒

批次 35:
  奖励值: 110.0258
  收益率: 0.2191
  距离: 26.6546
  内存使用: 0.4828
  能量使用: 0.8109
  推理时间: 2.1428秒

批次 36:
  奖励值: 122.3207
  收益率: 0.2487
  距离: 29.9628
  内存使用: 0.6145
  能量使用: 0.9811
  推理时间: 2.4190秒

批次 37:
  奖励值: 122.4944
  收益率: 0.2419
  距离: 26.5781
  内存使用: 0.8868
  能量使用: 0.9463
  推理时间: 2.5296秒

批次 38:
  奖励值: 129.8961
  收益率: 0.2549
  距离: 31.3297
  内存使用: 0.6867
  能量使用: 0.9914
  推理时间: 2.6271秒

批次 39:
  奖励值: 118.9288
  收益率: 0.2395
  距离: 31.3688
  内存使用: 0.8750
  能量使用: 0.8987
  推理时间: 2.3615秒

批次 40:
  奖励值: 125.6318
  收益率: 0.2517
  距离: 30.5395
  内存使用: 0.6863
  能量使用: 0.9106
  推理时间: 2.4944秒

批次 41:
  奖励值: 128.5866
  收益率: 0.2571
  距离: 30.8277
  内存使用: 0.6444
  能量使用: 0.9546
  推理时间: 2.4746秒

批次 42:
  奖励值: 114.4385
  收益率: 0.2299
  距离: 32.8939
  内存使用: 0.8997
  能量使用: 0.9293
  推理时间: 2.4482秒

批次 43:
  奖励值: 115.7190
  收益率: 0.2318
  距离: 27.3699
  内存使用: 0.8936
  能量使用: 0.8984
  推理时间: 3.4083秒

批次 44:
  奖励值: 131.2366
  收益率: 0.2614
  距离: 31.2420
  内存使用: 0.7031
  能量使用: 0.9818
  推理时间: 2.6097秒

批次 45:
  奖励值: 115.3848
  收益率: 0.2354
  距离: 30.2232
  内存使用: 0.5880
  能量使用: 1.0192
  推理时间: 2.3528秒

批次 46:
  奖励值: 115.8339
  收益率: 0.2312
  距离: 25.9258
  内存使用: 0.8992
  能量使用: 0.9071
  推理时间: 2.4148秒

批次 47:
  奖励值: 109.9930
  收益率: 0.2149
  距离: 24.4287
  内存使用: 0.5489
  能量使用: 0.8362
  推理时间: 2.2631秒

批次 48:
  奖励值: 115.0805
  收益率: 0.2340
  距离: 29.1699
  内存使用: 0.5960
  能量使用: 0.8731
  推理时间: 2.5053秒

批次 49:
  奖励值: 128.7334
  收益率: 0.2591
  距离: 32.2801
  内存使用: 0.6247
  能量使用: 1.0283
  推理时间: 3.0124秒

批次 50:
  奖励值: 103.7037
  收益率: 0.2127
  距离: 27.8842
  内存使用: 0.4970
  能量使用: 0.8114
  推理时间: 2.1913秒

批次 51:
  奖励值: 124.9655
  收益率: 0.2508
  距离: 31.0487
  内存使用: 0.6464
  能量使用: 0.9738
  推理时间: 2.6354秒

批次 52:
  奖励值: 117.1563
  收益率: 0.2348
  距离: 27.1583
  内存使用: 0.6185
  能量使用: 0.9105
  推理时间: 2.4177秒

批次 53:
  奖励值: 111.1089
  收益率: 0.2227
  距离: 28.6204
  内存使用: 0.8996
  能量使用: 0.8621
  推理时间: 2.4515秒

批次 54:
  奖励值: 128.0340
  收益率: 0.2619
  距离: 29.5024
  内存使用: 0.6542
  能量使用: 0.9754
  推理时间: 2.7508秒

批次 55:
  奖励值: 119.8886
  收益率: 0.2373
  距离: 27.2559
  内存使用: 0.8992
  能量使用: 0.8677
  推理时间: 2.4257秒

批次 56:
  奖励值: 124.7623
  收益率: 0.2500
  距离: 30.7392
  内存使用: 0.6543
  能量使用: 0.8818
  推理时间: 2.5005秒

批次 57:
  奖励值: 114.9985
  收益率: 0.2293
  距离: 30.1482
  内存使用: 0.8994
  能量使用: 0.9553
  推理时间: 2.4013秒

批次 58:
  奖励值: 121.5468
  收益率: 0.2466
  距离: 29.3986
  内存使用: 0.5540
  能量使用: 0.9538
  推理时间: 2.1934秒

批次 59:
  奖励值: 117.6371
  收益率: 0.2285
  距离: 28.3316
  内存使用: 0.5774
  能量使用: 1.0166
  推理时间: 2.2875秒

批次 60:
  奖励值: 117.1698
  收益率: 0.2428
  距离: 30.9461
  内存使用: 0.6067
  能量使用: 0.9075
  推理时间: 2.2916秒

批次 61:
  奖励值: 122.3841
  收益率: 0.2372
  距离: 29.4007
  内存使用: 0.8997
  能量使用: 0.9313
  推理时间: 2.6225秒

批次 62:
  奖励值: 115.0066
  收益率: 0.2265
  距离: 24.6532
  内存使用: 0.8992
  能量使用: 0.8343
  推理时间: 2.1824秒

批次 63:
  奖励值: 140.3548
  收益率: 0.2831
  距离: 36.4417
  内存使用: 0.7765
  能量使用: 1.0721
  推理时间: 2.9578秒

批次 64:
  奖励值: 114.0233
  收益率: 0.2269
  距离: 30.9562
  内存使用: 0.5668
  能量使用: 0.8304
  推理时间: 2.5081秒

批次 65:
  奖励值: 119.4689
  收益率: 0.2379
  距离: 30.7267
  内存使用: 0.6040
  能量使用: 0.9315
  推理时间: 2.8197秒

批次 66:
  奖励值: 134.0768
  收益率: 0.2758
  距离: 35.1532
  内存使用: 0.6872
  能量使用: 1.0059
  推理时间: 2.6191秒

批次 67:
  奖励值: 123.4050
  收益率: 0.2435
  距离: 28.8622
  内存使用: 0.6156
  能量使用: 0.9121
  推理时间: 2.5593秒

批次 68:
  奖励值: 115.6627
  收益率: 0.2377
  距离: 28.1781
  内存使用: 0.6072
  能量使用: 0.8892
  推理时间: 2.2310秒

批次 69:
  奖励值: 125.4012
  收益率: 0.2522
  距离: 30.5980
  内存使用: 0.6426
  能量使用: 0.9570
  推理时间: 2.6442秒

批次 70:
  奖励值: 118.4715
  收益率: 0.2300
  距离: 28.8292
  内存使用: 0.5838
  能量使用: 0.8440
  推理时间: 2.5406秒

批次 71:
  奖励值: 117.4678
  收益率: 0.2395
  距离: 31.0544
  内存使用: 0.8921
  能量使用: 0.9179
  推理时间: 2.3645秒

批次 72:
  奖励值: 131.8317
  收益率: 0.2636
  距离: 32.3687
  内存使用: 0.7062
  能量使用: 1.0617
  推理时间: 2.5546秒

批次 73:
  奖励值: 114.4501
  收益率: 0.2238
  距离: 25.1669
  内存使用: 0.8992
  能量使用: 0.8337
  推理时间: 2.2040秒

批次 74:
  奖励值: 120.0875
  收益率: 0.2435
  距离: 31.0823
  内存使用: 0.6189
  能量使用: 0.9237
  推理时间: 2.6334秒

批次 75:
  奖励值: 123.4195
  收益率: 0.2442
  距离: 30.3493
  内存使用: 0.6374
  能量使用: 0.9744
  推理时间: 2.5512秒

批次 76:
  奖励值: 124.1143
  收益率: 0.2477
  距离: 28.5777
  内存使用: 0.6486
  能量使用: 0.9451
  推理时间: 2.4189秒

批次 77:
  奖励值: 124.8627
  收益率: 0.2525
  距离: 27.3164
  内存使用: 0.6034
  能量使用: 0.9548
  推理时间: 2.4257秒

批次 78:
  奖励值: 126.0478
  收益率: 0.2526
  距离: 30.7912
  内存使用: 0.6505
  能量使用: 1.0627
  推理时间: 2.4341秒

批次 79:
  奖励值: 126.8383
  收益率: 0.2498
  距离: 30.0616
  内存使用: 0.6316
  能量使用: 1.0302
  推理时间: 2.4427秒

批次 80:
  奖励值: 123.8801
  收益率: 0.2501
  距离: 31.7929
  内存使用: 0.6827
  能量使用: 0.9642
  推理时间: 2.4040秒

批次 81:
  奖励值: 117.8184
  收益率: 0.2393
  距离: 29.2310
  内存使用: 0.6211
  能量使用: 0.9109
  推理时间: 2.3102秒

批次 82:
  奖励值: 128.7016
  收益率: 0.2532
  距离: 33.5997
  内存使用: 0.6562
  能量使用: 0.9409
  推理时间: 2.4715秒

批次 83:
  奖励值: 124.3236
  收益率: 0.2476
  距离: 29.7721
  内存使用: 0.6514
  能量使用: 0.9292
  推理时间: 2.3515秒

批次 84:
  奖励值: 122.0160
  收益率: 0.2517
  距离: 33.4825
  内存使用: 0.6613
  能量使用: 1.0241
  推理时间: 2.6613秒

批次 85:
  奖励值: 122.7291
  收益率: 0.2471
  距离: 28.3901
  内存使用: 0.5675
  能量使用: 0.9841
  推理时间: 2.5966秒

批次 86:
  奖励值: 118.7217
  收益率: 0.2379
  距离: 30.4343
  内存使用: 0.6099
  能量使用: 0.9123
  推理时间: 2.5253秒

批次 87:
  奖励值: 107.2324
  收益率: 0.2156
  距离: 24.3548
  内存使用: 0.5417
  能量使用: 0.8386
  推理时间: 2.0510秒

批次 88:
  奖励值: 105.6586
  收益率: 0.2188
  距离: 26.9795
  内存使用: 0.8921
  能量使用: 0.8111
  推理时间: 2.1018秒

批次 89:
  奖励值: 121.8961
  收益率: 0.2404
  距离: 28.0948
  内存使用: 0.5601
  能量使用: 0.9528
  推理时间: 2.3526秒

批次 90:
  奖励值: 113.2650
  收益率: 0.2284
  距离: 30.1782
  内存使用: 0.8987
  能量使用: 0.9931
  推理时间: 2.6916秒

批次 91:
  奖励值: 111.6834
  收益率: 0.2189
  距离: 27.4546
  内存使用: 0.5573
  能量使用: 0.8213
  推理时间: 2.2327秒

批次 92:
  奖励值: 122.4233
  收益率: 0.2466
  距离: 29.2124
  内存使用: 0.6163
  能量使用: 0.9247
  推理时间: 2.6626秒

批次 93:
  奖励值: 109.2125
  收益率: 0.2184
  距离: 26.8653
  内存使用: 0.8994
  能量使用: 0.8103
  推理时间: 2.3973秒

批次 94:
  奖励值: 120.2622
  收益率: 0.2418
  距离: 30.5639
  内存使用: 0.6217
  能量使用: 0.8900
  推理时间: 2.3695秒

批次 95:
  奖励值: 115.7517
  收益率: 0.2251
  距离: 29.4354
  内存使用: 0.8989
  能量使用: 0.9623
  推理时间: 2.5347秒

批次 96:
  奖励值: 115.9642
  收益率: 0.2275
  距离: 28.0428
  内存使用: 0.6000
  能量使用: 0.8287
  推理时间: 2.2456秒

批次 97:
  奖励值: 117.8053
  收益率: 0.2387
  距离: 28.9135
  内存使用: 0.5442
  能量使用: 0.9317
  推理时间: 2.2626秒

批次 98:
  奖励值: 115.6112
  收益率: 0.2268
  距离: 27.6826
  内存使用: 0.8675
  能量使用: 0.8275
  推理时间: 2.4145秒

批次 99:
  奖励值: 130.9258
  收益率: 0.2651
  距离: 36.6705
  内存使用: 0.6689
  能量使用: 1.0088
  推理时间: 2.8214秒

批次 100:
  奖励值: 112.4115
  收益率: 0.2315
  距离: 35.9946
  内存使用: 0.8430
  能量使用: 0.9394
  推理时间: 2.2917秒


==================== 总结 ====================
平均收益率: 0.2420
平均能量使用: 0.9363
平均推理时间: 2.4803秒
