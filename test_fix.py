#!/usr/bin/env python3
"""
测试修复后的GPNConstellation模型
"""
import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperparameter import args
from constellation_smp.constellation_smp import ConstellationSMPDataset
from constellation_smp.gpn_constellation import GPNConstellation

def test_model():
    """测试模型的前向传播"""
    print("🚀 开始测试GPNConstellation模型...")
    
    # 创建小规模测试数据
    print("📊 创建测试数据...")
    train_data = ConstellationSMPDataset(
        args.num_nodes,
        5,  # 小批量测试
        args.seed,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )
    
    print(f"✓ 数据集创建成功: {len(train_data)} 个样本")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")

    # 创建模型
    print("🏗️ 创建模型...")
    actor = GPNConstellation(
        args.static_size,
        args.dynamic_size,
        args.hidden_size,
        args.num_satellites,
        args.rnn,
        args.num_layers,
        train_data.update_dynamic,
        train_data.update_mask,
        args.num_nodes,
        args.dropout,
        args.constellation_mode,
        args.use_transformer,
        None
    ).to(device)
    
    total_params = sum(p.numel() for p in actor.parameters())
    print(f"✓ 模型创建成功: {total_params:,} 个参数")
    print(f"  - RNN类型: {args.rnn}")
    print(f"  - 星座模式: {args.constellation_mode}")
    print(f"  - 使用Transformer: {args.use_transformer}")
    
    # 创建数据加载器
    from torch.utils.data import DataLoader
    test_loader = DataLoader(train_data, batch_size=2, shuffle=False)
    
    # 测试前向传播
    print("🔄 测试前向传播...")
    try:
        static, dynamic, _ = next(iter(test_loader))
        # 将数据移动到正确的设备
        static = static.to(device)
        dynamic = dynamic.to(device)

        print(f"  - Static shape: {static.shape}")
        print(f"  - Dynamic shape: {dynamic.shape}")

        # 前向传播
        with torch.no_grad():
            tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
        
        print("✅ 前向传播成功!")
        print(f"  - Tour indices shape: {tour_indices.shape}")
        print(f"  - Satellite indices shape: {satellite_indices.shape}")
        print(f"  - Tour log prob shape: {tour_log_prob.shape}")
        print(f"  - Satellite log prob shape: {satellite_log_prob.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_modes():
    """测试不同的星座模式"""
    modes = ['cooperative', 'competitive', 'hybrid']
    
    for mode in modes:
        print(f"\n🔍 测试 {mode.upper()} 模式...")
        
        # 临时修改星座模式
        original_mode = args.constellation_mode
        args.constellation_mode = mode
        
        try:
            success = test_model()
            if success:
                print(f"✅ {mode.upper()} 模式测试通过")
            else:
                print(f"❌ {mode.upper()} 模式测试失败")
        except Exception as e:
            print(f"❌ {mode.upper()} 模式测试异常: {e}")
        finally:
            # 恢复原始模式
            args.constellation_mode = original_mode

if __name__ == '__main__':
    print("=" * 60)
    print("GPNConstellation 模型修复测试")
    print("=" * 60)
    
    # 基本测试
    success = test_model()
    
    if success:
        print("\n🎉 基本测试通过！开始测试不同模式...")
        test_different_modes()
        print("\n🎊 所有测试完成！")
    else:
        print("\n💥 基本测试失败，请检查修复！")
