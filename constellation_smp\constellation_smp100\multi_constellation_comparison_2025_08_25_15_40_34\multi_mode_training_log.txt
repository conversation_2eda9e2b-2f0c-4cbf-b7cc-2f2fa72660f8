多星座模式训练实验
================================================================================
实验时间: 2025_08_25_15_40_34
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: False

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 2,342,920
  Critic参数数量: 494,285
  总参数数量: 2,837,205
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-25 15:40:39
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
❌ cooperative 模式训练失败: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\train_multi_constellation_modes.py", line 832, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\train_multi_constellation_modes.py", line 348, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\train_multi_constellation_modes.py", line 132, in train_constellation_with_detailed_logging
    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\constellation_smp\gpn_constellation.py", line 302, in forward
    task_logits, task_hx = self.task_selector(
ValueError: too many values to unpack (expected 2)


================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 2,342,920
  Critic参数数量: 494,285
  总参数数量: 2,837,205
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-25 15:40:51
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
❌ competitive 模式训练失败: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\train_multi_constellation_modes.py", line 832, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\train_multi_constellation_modes.py", line 348, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\train_multi_constellation_modes.py", line 132, in train_constellation_with_detailed_logging
    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0825single_smp\constellation_smp\gpn_constellation.py", line 302, in forward
    task_logits, task_hx = self.task_selector(
ValueError: too many values to unpack (expected 2)


================================================================================
开始训练星座模式: HYBRID
================================================================================
