推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 26.4142
  收益率: 0.6718
  距离: 7.5857
  内存使用: -0.0421
  能量使用: 0.2279
  推理时间: 0.9144秒

批次 2:
  奖励值: 28.6124
  收益率: 0.7091
  距离: 8.4269
  内存使用: -0.0506
  能量使用: 0.1989
  推理时间: 0.5712秒

批次 3:
  奖励值: 29.9214
  收益率: 0.7560
  距离: 9.2969
  内存使用: -0.0055
  能量使用: 0.2657
  推理时间: 0.6414秒

批次 4:
  奖励值: 28.1600
  收益率: 0.6926
  距离: 7.4499
  内存使用: -0.0456
  能量使用: 0.2758
  推理时间: 0.6312秒

批次 5:
  奖励值: 26.8524
  收益率: 0.6909
  距离: 6.9609
  内存使用: -0.0878
  能量使用: 0.2221
  推理时间: 0.5943秒

批次 6:
  奖励值: 32.1499
  收益率: 0.7393
  距离: 8.0822
  内存使用: -0.0438
  能量使用: 0.2618
  推理时间: 0.7120秒

批次 7:
  奖励值: 23.0483
  收益率: 0.6036
  距离: 7.9392
  内存使用: -0.0767
  能量使用: 0.2409
  推理时间: 0.6038秒

批次 8:
  奖励值: 29.7084
  收益率: 0.7312
  距离: 8.3788
  内存使用: -0.0620
  能量使用: 0.2663
  推理时间: 0.7467秒

批次 9:
  奖励值: 27.5348
  收益率: 0.6688
  距离: 6.9955
  内存使用: -0.1075
  能量使用: 0.2424
  推理时间: 0.7193秒

批次 10:
  奖励值: 30.8847
  收益率: 0.7542
  距离: 9.4514
  内存使用: 0.2756
  能量使用: 0.3037
  推理时间: 0.8377秒

批次 11:
  奖励值: 30.2765
  收益率: 0.7638
  距离: 8.4535
  内存使用: -0.0825
  能量使用: 0.2760
  推理时间: 0.7911秒

批次 12:
  奖励值: 33.3690
  收益率: 0.7845
  距离: 8.0081
  内存使用: -0.0306
  能量使用: 0.2845
  推理时间: 0.8300秒

批次 13:
  奖励值: 30.4066
  收益率: 0.7252
  距离: 9.1337
  内存使用: -0.0137
  能量使用: 0.2667
  推理时间: 0.8963秒

批次 14:
  奖励值: 29.3908
  收益率: 0.7428
  距离: 7.9697
  内存使用: -0.0347
  能量使用: 0.2834
  推理时间: 0.7162秒

批次 15:
  奖励值: 28.7687
  收益率: 0.7002
  距离: 9.0595
  内存使用: -0.0529
  能量使用: 0.2560
  推理时间: 0.7339秒

批次 16:
  奖励值: 32.5595
  收益率: 0.7697
  距离: 8.8167
  内存使用: -0.0042
  能量使用: 0.2684
  推理时间: 0.7866秒

批次 17:
  奖励值: 28.2181
  收益率: 0.6867
  距离: 7.3213
  内存使用: -0.0496
  能量使用: 0.2921
  推理时间: 0.6755秒

批次 18:
  奖励值: 26.1916
  收益率: 0.6830
  距离: 8.6188
  内存使用: -0.0504
  能量使用: 0.2484
  推理时间: 0.6430秒

批次 19:
  奖励值: 30.9328
  收益率: 0.7453
  距离: 9.8788
  内存使用: -0.0422
  能量使用: 0.2978
  推理时间: 0.7253秒

批次 20:
  奖励值: 26.7385
  收益率: 0.6719
  距离: 6.5769
  内存使用: -0.0680
  能量使用: 0.2141
  推理时间: 0.6036秒

批次 21:
  奖励值: 29.5566
  收益率: 0.7106
  距离: 7.4789
  内存使用: -0.0594
  能量使用: 0.2672
  推理时间: 0.6781秒

批次 22:
  奖励值: 30.9006
  收益率: 0.7495
  距离: 8.4945
  内存使用: 0.0410
  能量使用: 0.2632
  推理时间: 0.7348秒

批次 23:
  奖励值: 27.6904
  收益率: 0.6560
  距离: 5.8339
  内存使用: -0.0630
  能量使用: 0.2483
  推理时间: 0.5893秒

批次 24:
  奖励值: 27.3227
  收益率: 0.7185
  距离: 7.9200
  内存使用: -0.0286
  能量使用: 0.2632
  推理时间: 0.6408秒

批次 25:
  奖励值: 32.5778
  收益率: 0.7505
  距离: 7.4252
  内存使用: -0.0695
  能量使用: 0.2887
  推理时间: 0.7468秒

批次 26:
  奖励值: 26.9381
  收益率: 0.6667
  距离: 7.5443
  内存使用: -0.0585
  能量使用: 0.2379
  推理时间: 0.6381秒

批次 27:
  奖励值: 30.9735
  收益率: 0.7357
  距离: 9.5819
  内存使用: -0.0037
  能量使用: 0.2749
  推理时间: 0.7440秒

批次 28:
  奖励值: 27.3223
  收益率: 0.6631
  距离: 6.5841
  内存使用: -0.0268
  能量使用: 0.1982
  推理时间: 0.6498秒

批次 29:
  奖励值: 31.1284
  收益率: 0.7751
  距离: 7.1623
  内存使用: -0.0668
  能量使用: 0.2476
  推理时间: 0.6997秒

批次 30:
  奖励值: 29.5454
  收益率: 0.7273
  距离: 7.8475
  内存使用: -0.0252
  能量使用: 0.2870
  推理时间: 0.7796秒

批次 31:
  奖励值: 31.9563
  收益率: 0.7371
  距离: 9.8553
  内存使用: -0.0306
  能量使用: 0.2626
  推理时间: 0.9081秒

批次 32:
  奖励值: 28.6922
  收益率: 0.7100
  距离: 8.9686
  内存使用: -0.0276
  能量使用: 0.2747
  推理时间: 0.7878秒

批次 33:
  奖励值: 28.7525
  收益率: 0.7201
  距离: 9.7049
  内存使用: -0.0638
  能量使用: 0.2539
  推理时间: 0.8132秒

批次 34:
  奖励值: 30.7627
  收益率: 0.7441
  距离: 7.4953
  内存使用: -0.0500
  能量使用: 0.2294
  推理时间: 0.7967秒

批次 35:
  奖励值: 30.9924
  收益率: 0.7521
  距离: 8.7693
  内存使用: -0.0342
  能量使用: 0.2802
  推理时间: 0.7933秒

批次 36:
  奖励值: 34.7399
  收益率: 0.7904
  距离: 9.4800
  内存使用: -0.0634
  能量使用: 0.3037
  推理时间: 0.8825秒

批次 37:
  奖励值: 25.4212
  收益率: 0.6860
  距离: 5.8303
  内存使用: -0.1046
  能量使用: 0.2319
  推理时间: 0.6741秒

批次 38:
  奖励值: 23.7473
  收益率: 0.6313
  距离: 7.2584
  内存使用: -0.1422
  能量使用: 0.1893
  推理时间: 0.6328秒

批次 39:
  奖励值: 31.2758
  收益率: 0.7521
  距离: 7.6302
  内存使用: -0.0466
  能量使用: 0.2648
  推理时间: 0.7822秒

批次 40:
  奖励值: 26.6639
  收益率: 0.6622
  距离: 6.1242
  内存使用: -0.0885
  能量使用: 0.2366
  推理时间: 0.6893秒

批次 41:
  奖励值: 26.2632
  收益率: 0.6652
  距离: 6.1553
  内存使用: -0.0954
  能量使用: 0.2138
  推理时间: 0.6606秒

批次 42:
  奖励值: 31.5029
  收益率: 0.7495
  距离: 9.1756
  内存使用: -0.0343
  能量使用: 0.2530
  推理时间: 0.8202秒

批次 43:
  奖励值: 27.7018
  收益率: 0.6645
  距离: 5.8489
  内存使用: -0.0491
  能量使用: 0.1966
  推理时间: 0.6973秒

批次 44:
  奖励值: 30.1290
  收益率: 0.7259
  距离: 8.9328
  内存使用: -0.0692
  能量使用: 0.2783
  推理时间: 0.7893秒

批次 45:
  奖励值: 29.5993
  收益率: 0.7511
  距离: 10.0173
  内存使用: -0.0447
  能量使用: 0.2288
  推理时间: 0.7939秒

批次 46:
  奖励值: 29.3073
  收益率: 0.7165
  距离: 7.4801
  内存使用: -0.1041
  能量使用: 0.2094
  推理时间: 0.6996秒

批次 47:
  奖励值: 27.2645
  收益率: 0.6851
  距离: 7.1316
  内存使用: -0.1025
  能量使用: 0.2418
  推理时间: 0.7060秒

批次 48:
  奖励值: 29.9785
  收益率: 0.7383
  距离: 8.9148
  内存使用: 0.2620
  能量使用: 0.2661
  推理时间: 0.8291秒

批次 49:
  奖励值: 31.2680
  收益率: 0.7276
  距离: 8.9053
  内存使用: -0.0713
  能量使用: 0.2301
  推理时间: 0.7291秒

批次 50:
  奖励值: 25.7430
  收益率: 0.6200
  距离: 7.7745
  内存使用: -0.0952
  能量使用: 0.2347
  推理时间: 0.6762秒

批次 51:
  奖励值: 30.4593
  收益率: 0.7543
  距离: 8.2515
  内存使用: -0.0365
  能量使用: 0.2664
  推理时间: 0.7797秒

批次 52:
  奖励值: 27.8656
  收益率: 0.6926
  距离: 8.0925
  内存使用: -0.0395
  能量使用: 0.2159
  推理时间: 0.7180秒

批次 53:
  奖励值: 30.5974
  收益率: 0.7307
  距离: 8.5742
  内存使用: -0.0365
  能量使用: 0.2676
  推理时间: 0.8131秒

批次 54:
  奖励值: 27.5315
  收益率: 0.6769
  距离: 6.7687
  内存使用: -0.0532
  能量使用: 0.2215
  推理时间: 0.7382秒

批次 55:
  奖励值: 28.9725
  收益率: 0.6941
  距离: 7.6533
  内存使用: -0.0554
  能量使用: 0.2572
  推理时间: 0.7518秒

批次 56:
  奖励值: 28.9726
  收益率: 0.7339
  距离: 8.0435
  内存使用: -0.0372
  能量使用: 0.2484
  推理时间: 0.7780秒

批次 57:
  奖励值: 28.1947
  收益率: 0.7406
  距离: 10.1548
  内存使用: -0.0318
  能量使用: 0.2876
  推理时间: 0.8430秒

批次 58:
  奖励值: 29.5906
  收益率: 0.7550
  距离: 8.3869
  内存使用: -0.0526
  能量使用: 0.2845
  推理时间: 0.8753秒

批次 59:
  奖励值: 32.8349
  收益率: 0.7411
  距离: 9.1095
  内存使用: -0.0304
  能量使用: 0.2510
  推理时间: 0.8884秒

批次 60:
  奖励值: 28.9944
  收益率: 0.7084
  距离: 7.4105
  内存使用: -0.0341
  能量使用: 0.2348
  推理时间: 0.7340秒

批次 61:
  奖励值: 27.3124
  收益率: 0.6856
  距离: 7.9952
  内存使用: -0.0640
  能量使用: 0.2440
  推理时间: 0.6757秒

批次 62:
  奖励值: 25.7893
  收益率: 0.6830
  距离: 6.9088
  内存使用: -0.1023
  能量使用: 0.2148
  推理时间: 0.6542秒

批次 63:
  奖励值: 27.0145
  收益率: 0.7127
  距离: 8.7572
  内存使用: -0.0399
  能量使用: 0.2537
  推理时间: 0.7688秒

批次 64:
  奖励值: 25.7913
  收益率: 0.6815
  距离: 6.4244
  内存使用: -0.0509
  能量使用: 0.2439
  推理时间: 0.7103秒

批次 65:
  奖励值: 31.9255
  收益率: 0.7566
  距离: 9.9691
  内存使用: -0.0604
  能量使用: 0.2404
  推理时间: 0.8825秒

批次 66:
  奖励值: 29.0085
  收益率: 0.7131
  距离: 9.3457
  内存使用: -0.0271
  能量使用: 0.2644
  推理时间: 0.8293秒

批次 67:
  奖励值: 29.0876
  收益率: 0.7118
  距离: 7.7997
  内存使用: -0.0455
  能量使用: 0.2707
  推理时间: 0.7721秒

批次 68:
  奖励值: 28.2224
  收益率: 0.7143
  距离: 7.3693
  内存使用: -0.0547
  能量使用: 0.2406
  推理时间: 0.7571秒

批次 69:
  奖励值: 28.3995
  收益率: 0.7325
  距离: 9.8121
  内存使用: -0.0619
  能量使用: 0.2509
  推理时间: 0.7216秒

批次 70:
  奖励值: 29.7996
  收益率: 0.7181
  距离: 10.0054
  内存使用: -0.0502
  能量使用: 0.2457
  推理时间: 0.7678秒

批次 71:
  奖励值: 29.8345
  收益率: 0.7296
  距离: 8.1480
  内存使用: -0.0731
  能量使用: 0.2562
  推理时间: 0.7204秒

批次 72:
  奖励值: 28.5692
  收益率: 0.7291
  距离: 8.8619
  内存使用: -0.0601
  能量使用: 0.2597
  推理时间: 0.8000秒

批次 73:
  奖励值: 34.1309
  收益率: 0.7654
  距离: 8.4187
  内存使用: -0.0034
  能量使用: 0.3228
  推理时间: 0.8417秒

批次 74:
  奖励值: 33.0412
  收益率: 0.7684
  距离: 8.6546
  内存使用: -0.0291
  能量使用: 0.2922
  推理时间: 0.7534秒

批次 75:
  奖励值: 28.6924
  收益率: 0.7024
  距离: 6.6136
  内存使用: -0.0489
  能量使用: 0.2505
  推理时间: 0.7098秒

批次 76:
  奖励值: 25.0063
  收益率: 0.6651
  距离: 7.4578
  内存使用: -0.0803
  能量使用: 0.2099
  推理时间: 0.6206秒

批次 77:
  奖励值: 30.5234
  收益率: 0.6973
  距离: 6.9891
  内存使用: -0.0619
  能量使用: 0.2260
  推理时间: 0.6896秒

批次 78:
  奖励值: 27.1874
  收益率: 0.6872
  距离: 7.8798
  内存使用: -0.0870
  能量使用: 0.2323
  推理时间: 0.7246秒

批次 79:
  奖励值: 26.8202
  收益率: 0.7307
  距离: 8.6082
  内存使用: -0.0601
  能量使用: 0.2114
  推理时间: 0.7744秒

批次 80:
  奖励值: 26.3499
  收益率: 0.6689
  距离: 7.3496
  内存使用: -0.0654
  能量使用: 0.2160
  推理时间: 0.6988秒

批次 81:
  奖励值: 22.5502
  收益率: 0.6559
  距离: 7.3942
  内存使用: -0.0978
  能量使用: 0.2031
  推理时间: 0.6730秒

批次 82:
  奖励值: 31.6079
  收益率: 0.7267
  距离: 8.3058
  内存使用: -0.0075
  能量使用: 0.2860
  推理时间: 0.8132秒

批次 83:
  奖励值: 33.2339
  收益率: 0.7616
  距离: 8.6512
  内存使用: -0.0326
  能量使用: 0.3135
  推理时间: 0.8120秒

批次 84:
  奖励值: 28.8015
  收益率: 0.6925
  距离: 8.4607
  内存使用: -0.0670
  能量使用: 0.2033
  推理时间: 0.7160秒

批次 85:
  奖励值: 31.2109
  收益率: 0.7343
  距离: 7.5820
  内存使用: -0.0632
  能量使用: 0.2593
  推理时间: 0.7535秒

批次 86:
  奖励值: 26.8148
  收益率: 0.6947
  距离: 8.9755
  内存使用: -0.0607
  能量使用: 0.2556
  推理时间: 0.8121秒

批次 87:
  奖励值: 28.6621
  收益率: 0.7251
  距离: 7.8430
  内存使用: -0.0645
  能量使用: 0.2973
  推理时间: 0.7552秒

批次 88:
  奖励值: 31.5517
  收益率: 0.7301
  距离: 6.5533
  内存使用: -0.0507
  能量使用: 0.1940
  推理时间: 0.7537秒

批次 89:
  奖励值: 25.4308
  收益率: 0.6607
  距离: 8.2251
  内存使用: -0.0742
  能量使用: 0.1874
  推理时间: 0.7146秒

批次 90:
  奖励值: 27.9087
  收益率: 0.7454
  距离: 8.9728
  内存使用: -0.0448
  能量使用: 0.2546
  推理时间: 0.7152秒

批次 91:
  奖励值: 29.3519
  收益率: 0.7316
  距离: 8.6664
  内存使用: -0.0430
  能量使用: 0.2728
  推理时间: 0.7167秒

批次 92:
  奖励值: 29.5725
  收益率: 0.7023
  距离: 9.0582
  内存使用: -0.0414
  能量使用: 0.2382
  推理时间: 0.6923秒

批次 93:
  奖励值: 33.1761
  收益率: 0.7405
  距离: 7.5839
  内存使用: -0.0115
  能量使用: 0.2754
  推理时间: 0.7390秒

批次 94:
  奖励值: 26.0629
  收益率: 0.6839
  距离: 8.7093
  内存使用: -0.0625
  能量使用: 0.2273
  推理时间: 0.7990秒

批次 95:
  奖励值: 31.0905
  收益率: 0.7382
  距离: 9.8045
  内存使用: -0.0292
  能量使用: 0.2437
  推理时间: 0.7464秒

批次 96:
  奖励值: 29.3094
  收益率: 0.7306
  距离: 9.0129
  内存使用: -0.0642
  能量使用: 0.2324
  推理时间: 0.6645秒

批次 97:
  奖励值: 25.6388
  收益率: 0.6236
  距离: 5.5805
  内存使用: -0.0722
  能量使用: 0.2141
  推理时间: 0.6966秒

批次 98:
  奖励值: 31.5576
  收益率: 0.7120
  距离: 7.8682
  内存使用: -0.0661
  能量使用: 0.2828
  推理时间: 0.7618秒

批次 99:
  奖励值: 26.6251
  收益率: 0.7176
  距离: 7.5911
  内存使用: -0.0878
  能量使用: 0.2466
  推理时间: 0.7607秒

批次 100:
  奖励值: 31.8580
  收益率: 0.7602
  距离: 10.8973
  内存使用: -0.0627
  能量使用: 0.2493
  推理时间: 0.7630秒


==================== 总结 ====================
平均收益率: 0.7142
平均能量使用: 0.2507
平均推理时间: 0.7409秒
