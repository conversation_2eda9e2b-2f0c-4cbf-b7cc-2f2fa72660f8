多星座模式训练实验
================================================================================
实验时间: 2025_08_13_15_04_48
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-13 15:11:19
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 704.217, reward: 12.801, critic_reward: 16.630, revenue_rate: 0.3215, distance: 4.6473, memory: -0.0974, power: 0.1399, lr: 0.000100, took: 45.787s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 41.482, reward: 17.886, critic_reward: 17.342, revenue_rate: 0.4471, distance: 6.2890, memory: -0.1414, power: 0.1904, lr: 0.000100, took: 53.402s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 12.732, reward: 20.117, critic_reward: 20.841, revenue_rate: 0.5010, distance: 6.9045, memory: -0.1132, power: 0.2082, lr: 0.000100, took: 56.331s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 12.447, reward: 24.419, critic_reward: 24.849, revenue_rate: 0.6104, distance: 8.2402, memory: -0.0895, power: 0.2478, lr: 0.000100, took: 66.460s
[COOPERATIVE] Epoch 1, Batch 50/1563, loss: 13.623, reward: 27.552, critic_reward: 27.729, revenue_rate: 0.6862, distance: 9.1284, memory: -0.0556, power: 0.2775, lr: 0.000100, took: 74.503s
[COOPERATIVE] Epoch 1, Batch 60/1563, loss: 9.068, reward: 26.612, critic_reward: 27.193, revenue_rate: 0.6594, distance: 8.6424, memory: -0.0683, power: 0.2592, lr: 0.000100, took: 70.018s
[COOPERATIVE] Epoch 1, Batch 70/1563, loss: 7.218, reward: 23.221, critic_reward: 22.993, revenue_rate: 0.5708, distance: 7.1532, memory: -0.1086, power: 0.2155, lr: 0.000100, took: 59.950s
[COOPERATIVE] Epoch 1, Batch 80/1563, loss: 6.382, reward: 25.632, critic_reward: 25.455, revenue_rate: 0.6312, distance: 8.0721, memory: -0.0883, power: 0.2427, lr: 0.000100, took: 65.403s
[COOPERATIVE] Epoch 1, Batch 90/1563, loss: 8.338, reward: 27.420, critic_reward: 26.738, revenue_rate: 0.6808, distance: 8.9909, memory: -0.0597, power: 0.2727, lr: 0.000100, took: 73.227s
[COOPERATIVE] Epoch 1, Batch 100/1563, loss: 4.338, reward: 28.379, critic_reward: 28.411, revenue_rate: 0.7031, distance: 9.0955, memory: -0.0561, power: 0.2725, lr: 0.000100, took: 74.557s
[COOPERATIVE] Epoch 1, Batch 110/1563, loss: 5.002, reward: 27.720, critic_reward: 28.076, revenue_rate: 0.6838, distance: 8.6555, memory: -0.0701, power: 0.2600, lr: 0.000100, took: 75.345s
[COOPERATIVE] Epoch 1, Batch 120/1563, loss: 7.275, reward: 27.700, critic_reward: 27.948, revenue_rate: 0.6810, distance: 8.5290, memory: -0.0724, power: 0.2600, lr: 0.000100, took: 72.802s
[COOPERATIVE] Epoch 1, Batch 130/1563, loss: 6.419, reward: 26.169, critic_reward: 25.030, revenue_rate: 0.6448, distance: 8.1055, memory: -0.0848, power: 0.2430, lr: 0.000100, took: 65.664s
[COOPERATIVE] Epoch 1, Batch 140/1563, loss: 5.465, reward: 28.696, critic_reward: 28.659, revenue_rate: 0.7091, distance: 9.0496, memory: -0.0502, power: 0.2736, lr: 0.000100, took: 72.333s
[COOPERATIVE] Epoch 1, Batch 150/1563, loss: 8.412, reward: 28.189, critic_reward: 27.409, revenue_rate: 0.7002, distance: 8.9336, memory: -0.0563, power: 0.2695, lr: 0.000100, took: 73.038s
[COOPERATIVE] Epoch 1, Batch 160/1563, loss: 7.693, reward: 28.288, critic_reward: 29.458, revenue_rate: 0.6959, distance: 8.6898, memory: -0.0625, power: 0.2649, lr: 0.000100, took: 73.103s
[COOPERATIVE] Epoch 1, Batch 170/1563, loss: 4.754, reward: 30.564, critic_reward: 30.243, revenue_rate: 0.7545, distance: 9.5059, memory: -0.0395, power: 0.2856, lr: 0.000100, took: 79.632s
[COOPERATIVE] Epoch 1, Batch 180/1563, loss: 3.741, reward: 29.758, critic_reward: 29.938, revenue_rate: 0.7334, distance: 9.1440, memory: -0.0487, power: 0.2756, lr: 0.000100, took: 79.679s
[COOPERATIVE] Epoch 1, Batch 190/1563, loss: 4.147, reward: 28.365, critic_reward: 28.196, revenue_rate: 0.6941, distance: 8.5222, memory: -0.0744, power: 0.2563, lr: 0.000100, took: 74.198s
[COOPERATIVE] Epoch 1, Batch 200/1563, loss: 5.605, reward: 29.307, critic_reward: 29.895, revenue_rate: 0.7206, distance: 8.9256, memory: -0.0604, power: 0.2692, lr: 0.000100, took: 74.610s
[COOPERATIVE] Epoch 1, Batch 210/1563, loss: 9.004, reward: 30.930, critic_reward: 31.190, revenue_rate: 0.7623, distance: 9.6180, memory: -0.0432, power: 0.2893, lr: 0.000100, took: 83.668s
[COOPERATIVE] Epoch 1, Batch 220/1563, loss: 17.512, reward: 30.616, critic_reward: 30.343, revenue_rate: 0.7546, distance: 9.5228, memory: -0.0368, power: 0.2870, lr: 0.000100, took: 85.954s
[COOPERATIVE] Epoch 1, Batch 230/1563, loss: 8.259, reward: 30.617, critic_reward: 29.992, revenue_rate: 0.7565, distance: 9.4514, memory: -0.0417, power: 0.2876, lr: 0.000100, took: 84.687s
[COOPERATIVE] Epoch 1, Batch 240/1563, loss: 3.429, reward: 30.240, critic_reward: 30.519, revenue_rate: 0.7442, distance: 9.3413, memory: -0.0421, power: 0.2810, lr: 0.000100, took: 80.240s
[COOPERATIVE] Epoch 1, Batch 250/1563, loss: 4.904, reward: 29.569, critic_reward: 29.917, revenue_rate: 0.7263, distance: 8.9125, memory: -0.0572, power: 0.2708, lr: 0.000100, took: 76.837s
[COOPERATIVE] Epoch 1, Batch 260/1563, loss: 17.512, reward: 27.830, critic_reward: 27.191, revenue_rate: 0.6816, distance: 8.3606, memory: -0.0716, power: 0.2515, lr: 0.000100, took: 69.080s
[COOPERATIVE] Epoch 1, Batch 270/1563, loss: 21.293, reward: 29.844, critic_reward: 28.916, revenue_rate: 0.7316, distance: 9.0850, memory: -0.0599, power: 0.2731, lr: 0.000100, took: 78.111s
[COOPERATIVE] Epoch 1, Batch 280/1563, loss: 11.233, reward: 30.510, critic_reward: 30.505, revenue_rate: 0.7523, distance: 9.6610, memory: -0.0318, power: 0.2918, lr: 0.000100, took: 81.170s
[COOPERATIVE] Epoch 1, Batch 290/1563, loss: 7.388, reward: 31.293, critic_reward: 31.553, revenue_rate: 0.7759, distance: 10.0688, memory: -0.0192, power: 0.3064, lr: 0.000100, took: 85.214s
[COOPERATIVE] Epoch 1, Batch 300/1563, loss: 8.451, reward: 27.842, critic_reward: 28.679, revenue_rate: 0.6841, distance: 8.7378, memory: -0.0482, power: 0.2652, lr: 0.000100, took: 74.799s
[COOPERATIVE] Epoch 1, Batch 310/1563, loss: 4.790, reward: 26.794, critic_reward: 26.040, revenue_rate: 0.6577, distance: 8.1495, memory: -0.0737, power: 0.2470, lr: 0.000100, took: 72.526s
[COOPERATIVE] Epoch 1, Batch 320/1563, loss: 3.728, reward: 30.572, critic_reward: 30.436, revenue_rate: 0.7533, distance: 9.4814, memory: -0.0346, power: 0.2878, lr: 0.000100, took: 81.836s
[COOPERATIVE] Epoch 1, Batch 330/1563, loss: 6.649, reward: 31.841, critic_reward: 30.786, revenue_rate: 0.7894, distance: 9.9536, memory: -0.0289, power: 0.3017, lr: 0.000100, took: 86.714s
[COOPERATIVE] Epoch 1, Batch 340/1563, loss: 6.823, reward: 30.251, critic_reward: 30.057, revenue_rate: 0.7451, distance: 9.2058, memory: -0.0385, power: 0.2817, lr: 0.000100, took: 78.535s
[COOPERATIVE] Epoch 1, Batch 350/1563, loss: 10.389, reward: 31.369, critic_reward: 31.344, revenue_rate: 0.7736, distance: 9.8367, memory: -0.0295, power: 0.2980, lr: 0.000100, took: 83.181s
[COOPERATIVE] Epoch 1, Batch 360/1563, loss: 18.670, reward: 32.465, critic_reward: 34.114, revenue_rate: 0.8036, distance: 10.3565, memory: -0.0130, power: 0.3139, lr: 0.000100, took: 91.050s
[COOPERATIVE] Epoch 1, Batch 370/1563, loss: 7.678, reward: 29.744, critic_reward: 30.017, revenue_rate: 0.7350, distance: 9.2870, memory: -0.0447, power: 0.2811, lr: 0.000100, took: 82.460s
[COOPERATIVE] Epoch 1, Batch 380/1563, loss: 5.876, reward: 31.368, critic_reward: 32.194, revenue_rate: 0.7715, distance: 9.6439, memory: -0.0382, power: 0.2911, lr: 0.000100, took: 82.405s
[COOPERATIVE] Epoch 1, Batch 390/1563, loss: 4.833, reward: 28.671, critic_reward: 28.358, revenue_rate: 0.7057, distance: 8.8325, memory: -0.0634, power: 0.2687, lr: 0.000100, took: 75.582s
[COOPERATIVE] Epoch 1, Batch 400/1563, loss: 3.797, reward: 30.367, critic_reward: 30.255, revenue_rate: 0.7481, distance: 9.4796, memory: -0.0426, power: 0.2857, lr: 0.000100, took: 75.750s
[COOPERATIVE] Epoch 1, Batch 410/1563, loss: 4.119, reward: 29.930, critic_reward: 30.019, revenue_rate: 0.7339, distance: 9.1228, memory: -0.0532, power: 0.2750, lr: 0.000100, took: 71.675s
[COOPERATIVE] Epoch 1, Batch 420/1563, loss: 11.076, reward: 30.196, critic_reward: 30.310, revenue_rate: 0.7418, distance: 9.2298, memory: -0.0458, power: 0.2798, lr: 0.000100, took: 74.500s
[COOPERATIVE] Epoch 1, Batch 430/1563, loss: 8.883, reward: 30.455, critic_reward: 31.394, revenue_rate: 0.7455, distance: 9.2081, memory: -0.0508, power: 0.2807, lr: 0.000100, took: 72.835s
[COOPERATIVE] Epoch 1, Batch 440/1563, loss: 7.682, reward: 29.860, critic_reward: 30.228, revenue_rate: 0.7343, distance: 9.0391, memory: -0.0532, power: 0.2732, lr: 0.000100, took: 73.959s
[COOPERATIVE] Epoch 1, Batch 450/1563, loss: 8.335, reward: 30.063, critic_reward: 30.916, revenue_rate: 0.7411, distance: 9.2253, memory: -0.0557, power: 0.2804, lr: 0.000100, took: 72.724s
[COOPERATIVE] Epoch 1, Batch 460/1563, loss: 3.627, reward: 29.688, critic_reward: 29.623, revenue_rate: 0.7294, distance: 9.0873, memory: -0.0525, power: 0.2748, lr: 0.000100, took: 71.531s
[COOPERATIVE] Epoch 1, Batch 470/1563, loss: 5.653, reward: 31.363, critic_reward: 31.780, revenue_rate: 0.7707, distance: 9.6329, memory: -0.0318, power: 0.2924, lr: 0.000100, took: 76.434s
[COOPERATIVE] Epoch 1, Batch 480/1563, loss: 6.275, reward: 29.488, critic_reward: 28.732, revenue_rate: 0.7261, distance: 9.1024, memory: -0.0468, power: 0.2750, lr: 0.000100, took: 71.839s
[COOPERATIVE] Epoch 1, Batch 490/1563, loss: 4.177, reward: 31.740, critic_reward: 32.068, revenue_rate: 0.7864, distance: 10.2576, memory: -0.0154, power: 0.3086, lr: 0.000100, took: 80.477s
[COOPERATIVE] Epoch 1, Batch 500/1563, loss: 4.021, reward: 31.983, critic_reward: 31.717, revenue_rate: 0.7900, distance: 10.0812, memory: -0.0267, power: 0.3072, lr: 0.000100, took: 82.030s
[COOPERATIVE] Epoch 1, Batch 510/1563, loss: 3.852, reward: 30.919, critic_reward: 31.155, revenue_rate: 0.7622, distance: 9.5264, memory: -0.0365, power: 0.2895, lr: 0.000100, took: 80.573s
[COOPERATIVE] Epoch 1, Batch 520/1563, loss: 3.733, reward: 26.726, critic_reward: 26.238, revenue_rate: 0.6529, distance: 7.8544, memory: -0.0862, power: 0.2392, lr: 0.000100, took: 66.671s
[COOPERATIVE] Epoch 1, Batch 530/1563, loss: 3.539, reward: 29.928, critic_reward: 30.259, revenue_rate: 0.7342, distance: 9.0141, memory: -0.0446, power: 0.2743, lr: 0.000100, took: 79.065s
[COOPERATIVE] Epoch 1, Batch 540/1563, loss: 7.062, reward: 30.404, critic_reward: 31.475, revenue_rate: 0.7454, distance: 9.1247, memory: -0.0502, power: 0.2763, lr: 0.000100, took: 79.024s
[COOPERATIVE] Epoch 1, Batch 550/1563, loss: 6.836, reward: 29.420, critic_reward: 30.393, revenue_rate: 0.7202, distance: 8.7896, memory: -0.0596, power: 0.2645, lr: 0.000100, took: 74.188s
[COOPERATIVE] Epoch 1, Batch 560/1563, loss: 6.884, reward: 30.867, critic_reward: 31.220, revenue_rate: 0.7586, distance: 9.3316, memory: -0.0501, power: 0.2839, lr: 0.000100, took: 81.502s
[COOPERATIVE] Epoch 1, Batch 570/1563, loss: 4.475, reward: 30.796, critic_reward: 30.545, revenue_rate: 0.7543, distance: 9.2365, memory: -0.0457, power: 0.2807, lr: 0.000100, took: 76.974s
[COOPERATIVE] Epoch 1, Batch 580/1563, loss: 14.886, reward: 31.545, critic_reward: 32.229, revenue_rate: 0.7772, distance: 9.7756, memory: -0.0273, power: 0.2949, lr: 0.000100, took: 84.233s
[COOPERATIVE] Epoch 1, Batch 590/1563, loss: 19.990, reward: 30.778, critic_reward: 30.090, revenue_rate: 0.7564, distance: 9.3079, memory: -0.0464, power: 0.2814, lr: 0.000100, took: 78.942s
[COOPERATIVE] Epoch 1, Batch 600/1563, loss: 6.645, reward: 30.878, critic_reward: 31.090, revenue_rate: 0.7543, distance: 9.2806, memory: -0.0439, power: 0.2822, lr: 0.000100, took: 78.769s
[COOPERATIVE] Epoch 1, Batch 610/1563, loss: 5.679, reward: 30.766, critic_reward: 30.942, revenue_rate: 0.7547, distance: 9.2727, memory: -0.0312, power: 0.2852, lr: 0.000100, took: 79.635s
[COOPERATIVE] Epoch 1, Batch 620/1563, loss: 5.622, reward: 31.071, critic_reward: 29.941, revenue_rate: 0.7630, distance: 9.3938, memory: -0.0440, power: 0.2833, lr: 0.000100, took: 79.406s
[COOPERATIVE] Epoch 1, Batch 630/1563, loss: 10.542, reward: 30.971, critic_reward: 32.543, revenue_rate: 0.7583, distance: 9.2199, memory: -0.0527, power: 0.2802, lr: 0.000100, took: 77.872s
[COOPERATIVE] Epoch 1, Batch 640/1563, loss: 3.293, reward: 29.871, critic_reward: 29.504, revenue_rate: 0.7324, distance: 8.8983, memory: -0.0503, power: 0.2693, lr: 0.000100, took: 75.013s
[COOPERATIVE] Epoch 1, Batch 650/1563, loss: 3.686, reward: 30.927, critic_reward: 31.545, revenue_rate: 0.7626, distance: 9.3864, memory: -0.0475, power: 0.2815, lr: 0.000100, took: 80.967s
[COOPERATIVE] Epoch 1, Batch 660/1563, loss: 3.739, reward: 30.231, critic_reward: 30.207, revenue_rate: 0.7383, distance: 8.8780, memory: -0.0628, power: 0.2702, lr: 0.000100, took: 74.953s
[COOPERATIVE] Epoch 1, Batch 670/1563, loss: 4.733, reward: 29.013, critic_reward: 28.875, revenue_rate: 0.7113, distance: 8.7121, memory: -0.0603, power: 0.2644, lr: 0.000100, took: 73.154s
[COOPERATIVE] Epoch 1, Batch 680/1563, loss: 4.430, reward: 29.172, critic_reward: 29.107, revenue_rate: 0.7157, distance: 8.6681, memory: -0.0611, power: 0.2627, lr: 0.000100, took: 75.436s
[COOPERATIVE] Epoch 1, Batch 690/1563, loss: 6.057, reward: 29.978, critic_reward: 28.970, revenue_rate: 0.7383, distance: 9.1458, memory: -0.0083, power: 0.2780, lr: 0.000100, took: 77.391s
[COOPERATIVE] Epoch 1, Batch 700/1563, loss: 10.838, reward: 30.483, critic_reward: 30.985, revenue_rate: 0.7466, distance: 9.0581, memory: -0.0519, power: 0.2759, lr: 0.000100, took: 76.535s
[COOPERATIVE] Epoch 1, Batch 710/1563, loss: 8.251, reward: 30.495, critic_reward: 31.958, revenue_rate: 0.7490, distance: 9.1365, memory: -0.0454, power: 0.2758, lr: 0.000100, took: 77.150s
[COOPERATIVE] Epoch 1, Batch 720/1563, loss: 5.225, reward: 30.325, critic_reward: 29.853, revenue_rate: 0.7439, distance: 8.9608, memory: -0.0604, power: 0.2720, lr: 0.000100, took: 75.432s
[COOPERATIVE] Epoch 1, Batch 730/1563, loss: 4.525, reward: 29.549, critic_reward: 30.133, revenue_rate: 0.7200, distance: 8.6082, memory: -0.0715, power: 0.2638, lr: 0.000100, took: 72.885s
[COOPERATIVE] Epoch 1, Batch 740/1563, loss: 6.232, reward: 28.885, critic_reward: 28.869, revenue_rate: 0.7064, distance: 8.4894, memory: -0.0733, power: 0.2552, lr: 0.000100, took: 71.431s
[COOPERATIVE] Epoch 1, Batch 750/1563, loss: 4.349, reward: 31.488, critic_reward: 31.564, revenue_rate: 0.7721, distance: 9.5411, memory: -0.0288, power: 0.2932, lr: 0.000100, took: 81.629s
[COOPERATIVE] Epoch 1, Batch 760/1563, loss: 12.415, reward: 33.421, critic_reward: 35.319, revenue_rate: 0.8276, distance: 10.6269, memory: 0.0025, power: 0.3202, lr: 0.000100, took: 91.234s
[COOPERATIVE] Epoch 1, Batch 770/1563, loss: 3.540, reward: 30.436, critic_reward: 29.984, revenue_rate: 0.7466, distance: 9.0743, memory: -0.0442, power: 0.2755, lr: 0.000100, took: 78.841s
[COOPERATIVE] Epoch 1, Batch 780/1563, loss: 3.185, reward: 29.843, critic_reward: 29.803, revenue_rate: 0.7292, distance: 8.8249, memory: -0.0600, power: 0.2669, lr: 0.000100, took: 73.783s
[COOPERATIVE] Epoch 1, Batch 790/1563, loss: 3.267, reward: 28.824, critic_reward: 28.328, revenue_rate: 0.7019, distance: 8.3645, memory: -0.0670, power: 0.2545, lr: 0.000100, took: 73.783s
[COOPERATIVE] Epoch 1, Batch 800/1563, loss: 2.749, reward: 30.285, critic_reward: 30.270, revenue_rate: 0.7428, distance: 9.0729, memory: -0.0401, power: 0.2728, lr: 0.000100, took: 75.980s
[COOPERATIVE] Epoch 1, Batch 810/1563, loss: 6.226, reward: 32.201, critic_reward: 32.325, revenue_rate: 0.7905, distance: 9.9216, memory: -0.0266, power: 0.2974, lr: 0.000100, took: 84.472s
[COOPERATIVE] Epoch 1, Batch 820/1563, loss: 9.510, reward: 32.254, critic_reward: 33.451, revenue_rate: 0.7917, distance: 9.8818, memory: -0.0179, power: 0.2984, lr: 0.000100, took: 84.759s
[COOPERATIVE] Epoch 1, Batch 830/1563, loss: 3.588, reward: 32.780, critic_reward: 32.884, revenue_rate: 0.8068, distance: 10.0999, memory: -0.0184, power: 0.3053, lr: 0.000100, took: 85.757s
[COOPERATIVE] Epoch 1, Batch 840/1563, loss: 4.697, reward: 29.913, critic_reward: 29.811, revenue_rate: 0.7329, distance: 8.8518, memory: -0.0534, power: 0.2687, lr: 0.000100, took: 74.490s
[COOPERATIVE] Epoch 1, Batch 850/1563, loss: 6.644, reward: 29.544, critic_reward: 29.824, revenue_rate: 0.7264, distance: 8.8128, memory: -0.0568, power: 0.2657, lr: 0.000100, took: 73.853s
[COOPERATIVE] Epoch 1, Batch 860/1563, loss: 9.091, reward: 32.624, critic_reward: 34.570, revenue_rate: 0.8065, distance: 10.1857, memory: -0.0217, power: 0.3093, lr: 0.000100, took: 87.386s
[COOPERATIVE] Epoch 1, Batch 870/1563, loss: 3.680, reward: 32.148, critic_reward: 32.496, revenue_rate: 0.7898, distance: 9.8565, memory: -0.0258, power: 0.2954, lr: 0.000100, took: 83.518s
[COOPERATIVE] Epoch 1, Batch 880/1563, loss: 7.370, reward: 31.731, critic_reward: 31.302, revenue_rate: 0.7779, distance: 9.4338, memory: -0.0475, power: 0.2871, lr: 0.000100, took: 82.394s
[COOPERATIVE] Epoch 1, Batch 890/1563, loss: 6.713, reward: 32.094, critic_reward: 32.404, revenue_rate: 0.7865, distance: 9.6498, memory: -0.0423, power: 0.2924, lr: 0.000100, took: 82.199s
[COOPERATIVE] Epoch 1, Batch 900/1563, loss: 2.849, reward: 31.198, critic_reward: 31.285, revenue_rate: 0.7647, distance: 9.2723, memory: -0.0593, power: 0.2803, lr: 0.000100, took: 81.201s
[COOPERATIVE] Epoch 1, Batch 910/1563, loss: 3.370, reward: 27.611, critic_reward: 27.906, revenue_rate: 0.6730, distance: 7.9412, memory: -0.0845, power: 0.2392, lr: 0.000100, took: 66.820s
[COOPERATIVE] Epoch 1, Batch 920/1563, loss: 5.652, reward: 29.102, critic_reward: 30.276, revenue_rate: 0.7105, distance: 8.5372, memory: -0.0632, power: 0.2592, lr: 0.000100, took: 72.877s
[COOPERATIVE] Epoch 1, Batch 930/1563, loss: 3.296, reward: 32.453, critic_reward: 32.560, revenue_rate: 0.7971, distance: 9.8738, memory: -0.0209, power: 0.2992, lr: 0.000100, took: 84.385s
[COOPERATIVE] Epoch 1, Batch 940/1563, loss: 4.798, reward: 34.038, critic_reward: 33.646, revenue_rate: 0.8414, distance: 10.6432, memory: 0.0002, power: 0.3222, lr: 0.000100, took: 92.075s
[COOPERATIVE] Epoch 1, Batch 950/1563, loss: 3.672, reward: 32.196, critic_reward: 32.156, revenue_rate: 0.7903, distance: 9.6961, memory: -0.0384, power: 0.2959, lr: 0.000100, took: 82.854s
[COOPERATIVE] Epoch 1, Batch 960/1563, loss: 5.782, reward: 31.351, critic_reward: 32.154, revenue_rate: 0.7667, distance: 9.4365, memory: -0.0453, power: 0.2858, lr: 0.000100, took: 79.976s
[COOPERATIVE] Epoch 1, Batch 970/1563, loss: 9.234, reward: 32.554, critic_reward: 31.357, revenue_rate: 0.7988, distance: 9.8685, memory: -0.0290, power: 0.2971, lr: 0.000100, took: 84.575s
[COOPERATIVE] Epoch 1, Batch 980/1563, loss: 4.462, reward: 31.719, critic_reward: 31.540, revenue_rate: 0.7789, distance: 9.4473, memory: -0.0333, power: 0.2887, lr: 0.000100, took: 81.029s
[COOPERATIVE] Epoch 1, Batch 990/1563, loss: 5.859, reward: 30.715, critic_reward: 31.007, revenue_rate: 0.7556, distance: 9.2318, memory: -0.0472, power: 0.2789, lr: 0.000100, took: 79.691s
[COOPERATIVE] Epoch 1, Batch 1000/1563, loss: 9.895, reward: 27.940, critic_reward: 27.318, revenue_rate: 0.6824, distance: 8.1749, memory: -0.0816, power: 0.2461, lr: 0.000100, took: 67.744s
[COOPERATIVE] Epoch 1, Batch 1010/1563, loss: 6.507, reward: 29.655, critic_reward: 29.412, revenue_rate: 0.7291, distance: 8.9948, memory: -0.0288, power: 0.2735, lr: 0.000100, took: 76.246s
[COOPERATIVE] Epoch 1, Batch 1020/1563, loss: 4.231, reward: 31.906, critic_reward: 32.528, revenue_rate: 0.7869, distance: 9.8710, memory: -0.0105, power: 0.3001, lr: 0.000100, took: 86.996s
[COOPERATIVE] Epoch 1, Batch 1030/1563, loss: 4.978, reward: 31.909, critic_reward: 31.978, revenue_rate: 0.7828, distance: 9.6169, memory: -0.0344, power: 0.2905, lr: 0.000100, took: 81.524s
[COOPERATIVE] Epoch 1, Batch 1040/1563, loss: 6.053, reward: 30.996, critic_reward: 29.746, revenue_rate: 0.7592, distance: 9.2163, memory: -0.0483, power: 0.2792, lr: 0.000100, took: 77.966s
[COOPERATIVE] Epoch 1, Batch 1050/1563, loss: 6.308, reward: 30.367, critic_reward: 30.826, revenue_rate: 0.7426, distance: 8.8606, memory: -0.0509, power: 0.2730, lr: 0.000100, took: 76.165s
[COOPERATIVE] Epoch 1, Batch 1060/1563, loss: 6.236, reward: 32.006, critic_reward: 33.191, revenue_rate: 0.7832, distance: 9.5384, memory: -0.0299, power: 0.2898, lr: 0.000100, took: 81.103s
[COOPERATIVE] Epoch 1, Batch 1070/1563, loss: 3.744, reward: 32.532, critic_reward: 32.658, revenue_rate: 0.8006, distance: 10.0394, memory: -0.0189, power: 0.3038, lr: 0.000100, took: 84.990s
[COOPERATIVE] Epoch 1, Batch 1080/1563, loss: 6.533, reward: 32.991, critic_reward: 33.793, revenue_rate: 0.8085, distance: 10.1079, memory: -0.0127, power: 0.3067, lr: 0.000100, took: 86.321s
[COOPERATIVE] Epoch 1, Batch 1090/1563, loss: 6.040, reward: 32.185, critic_reward: 32.686, revenue_rate: 0.7924, distance: 9.8614, memory: -0.0253, power: 0.2972, lr: 0.000100, took: 82.740s
[COOPERATIVE] Epoch 1, Batch 1100/1563, loss: 4.289, reward: 30.967, critic_reward: 30.868, revenue_rate: 0.7559, distance: 9.1874, memory: -0.0485, power: 0.2796, lr: 0.000100, took: 76.948s
[COOPERATIVE] Epoch 1, Batch 1110/1563, loss: 5.997, reward: 29.473, critic_reward: 29.721, revenue_rate: 0.7204, distance: 8.6063, memory: -0.0713, power: 0.2605, lr: 0.000100, took: 74.195s
[COOPERATIVE] Epoch 1, Batch 1120/1563, loss: 4.416, reward: 29.634, critic_reward: 29.847, revenue_rate: 0.7201, distance: 8.6358, memory: -0.0616, power: 0.2620, lr: 0.000100, took: 72.165s
[COOPERATIVE] Epoch 1, Batch 1130/1563, loss: 4.076, reward: 30.284, critic_reward: 29.644, revenue_rate: 0.7418, distance: 9.0856, memory: -0.0474, power: 0.2743, lr: 0.000100, took: 77.595s
[COOPERATIVE] Epoch 1, Batch 1140/1563, loss: 3.997, reward: 31.424, critic_reward: 31.655, revenue_rate: 0.7703, distance: 9.4684, memory: -0.0281, power: 0.2854, lr: 0.000100, took: 79.548s
[COOPERATIVE] Epoch 1, Batch 1150/1563, loss: 4.084, reward: 29.127, critic_reward: 29.520, revenue_rate: 0.7125, distance: 8.5128, memory: -0.0667, power: 0.2593, lr: 0.000100, took: 71.351s
[COOPERATIVE] Epoch 1, Batch 1160/1563, loss: 5.824, reward: 27.126, critic_reward: 25.984, revenue_rate: 0.6659, distance: 7.9673, memory: -0.0860, power: 0.2417, lr: 0.000100, took: 65.865s
[COOPERATIVE] Epoch 1, Batch 1170/1563, loss: 3.349, reward: 33.796, critic_reward: 34.174, revenue_rate: 0.8311, distance: 10.4255, memory: -0.0090, power: 0.3157, lr: 0.000100, took: 88.797s
[COOPERATIVE] Epoch 1, Batch 1180/1563, loss: 3.623, reward: 32.978, critic_reward: 33.184, revenue_rate: 0.8164, distance: 10.1387, memory: -0.0151, power: 0.3078, lr: 0.000100, took: 86.708s
[COOPERATIVE] Epoch 1, Batch 1190/1563, loss: 4.082, reward: 32.031, critic_reward: 31.755, revenue_rate: 0.7838, distance: 9.6821, memory: -0.0382, power: 0.2936, lr: 0.000100, took: 81.185s
[COOPERATIVE] Epoch 1, Batch 1200/1563, loss: 4.052, reward: 32.175, critic_reward: 32.951, revenue_rate: 0.7904, distance: 9.7543, memory: -0.0312, power: 0.2981, lr: 0.000100, took: 82.812s
[COOPERATIVE] Epoch 1, Batch 1210/1563, loss: 2.919, reward: 31.866, critic_reward: 31.416, revenue_rate: 0.7848, distance: 9.8048, memory: -0.0320, power: 0.3008, lr: 0.000100, took: 83.450s
[COOPERATIVE] Epoch 1, Batch 1220/1563, loss: 3.551, reward: 33.897, critic_reward: 34.506, revenue_rate: 0.8389, distance: 10.7279, memory: 0.0003, power: 0.3226, lr: 0.000100, took: 93.942s
[COOPERATIVE] Epoch 1, Batch 1230/1563, loss: 3.804, reward: 32.927, critic_reward: 33.351, revenue_rate: 0.8109, distance: 10.0675, memory: -0.0169, power: 0.3052, lr: 0.000100, took: 85.484s
[COOPERATIVE] Epoch 1, Batch 1240/1563, loss: 3.404, reward: 31.761, critic_reward: 31.231, revenue_rate: 0.7811, distance: 9.5435, memory: -0.0390, power: 0.2888, lr: 0.000100, took: 82.791s
[COOPERATIVE] Epoch 1, Batch 1250/1563, loss: 3.533, reward: 32.434, critic_reward: 32.877, revenue_rate: 0.7959, distance: 9.8165, memory: -0.0334, power: 0.2974, lr: 0.000100, took: 83.611s
[COOPERATIVE] Epoch 1, Batch 1260/1563, loss: 2.341, reward: 31.750, critic_reward: 31.840, revenue_rate: 0.7825, distance: 9.6037, memory: -0.0286, power: 0.2915, lr: 0.000100, took: 81.081s
[COOPERATIVE] Epoch 1, Batch 1270/1563, loss: 5.237, reward: 31.368, critic_reward: 30.789, revenue_rate: 0.7705, distance: 9.4839, memory: -0.0358, power: 0.2866, lr: 0.000100, took: 79.624s
[COOPERATIVE] Epoch 1, Batch 1280/1563, loss: 7.058, reward: 32.901, critic_reward: 34.221, revenue_rate: 0.8100, distance: 10.1841, memory: -0.0036, power: 0.3078, lr: 0.000100, took: 86.991s
[COOPERATIVE] Epoch 1, Batch 1290/1563, loss: 9.178, reward: 32.281, critic_reward: 33.806, revenue_rate: 0.7922, distance: 9.7675, memory: -0.0273, power: 0.2963, lr: 0.000100, took: 83.118s
[COOPERATIVE] Epoch 1, Batch 1300/1563, loss: 5.843, reward: 32.334, critic_reward: 31.558, revenue_rate: 0.7958, distance: 9.8069, memory: -0.0240, power: 0.2954, lr: 0.000100, took: 83.406s
[COOPERATIVE] Epoch 1, Batch 1310/1563, loss: 3.500, reward: 33.930, critic_reward: 34.281, revenue_rate: 0.8362, distance: 10.5311, memory: -0.0093, power: 0.3183, lr: 0.000100, took: 90.819s
[COOPERATIVE] Epoch 1, Batch 1320/1563, loss: 4.433, reward: 34.373, critic_reward: 34.218, revenue_rate: 0.8511, distance: 10.8647, memory: 0.0075, power: 0.3285, lr: 0.000100, took: 95.425s
[COOPERATIVE] Epoch 1, Batch 1330/1563, loss: 3.764, reward: 33.705, critic_reward: 33.407, revenue_rate: 0.8272, distance: 10.4813, memory: -0.0119, power: 0.3162, lr: 0.000100, took: 89.379s
[COOPERATIVE] Epoch 1, Batch 1340/1563, loss: 5.598, reward: 33.788, critic_reward: 34.131, revenue_rate: 0.8313, distance: 10.4574, memory: -0.0037, power: 0.3155, lr: 0.000100, took: 88.722s
[COOPERATIVE] Epoch 1, Batch 1350/1563, loss: 4.203, reward: 31.186, critic_reward: 30.476, revenue_rate: 0.7662, distance: 9.4195, memory: -0.0413, power: 0.2846, lr: 0.000100, took: 81.715s
[COOPERATIVE] Epoch 1, Batch 1360/1563, loss: 3.187, reward: 33.010, critic_reward: 32.851, revenue_rate: 0.8124, distance: 10.1149, memory: -0.0246, power: 0.3076, lr: 0.000100, took: 86.071s
[COOPERATIVE] Epoch 1, Batch 1370/1563, loss: 8.352, reward: 32.543, critic_reward: 34.471, revenue_rate: 0.7996, distance: 9.8794, memory: -0.0302, power: 0.2999, lr: 0.000100, took: 84.269s
[COOPERATIVE] Epoch 1, Batch 1380/1563, loss: 4.193, reward: 29.579, critic_reward: 28.514, revenue_rate: 0.7250, distance: 8.8156, memory: -0.0674, power: 0.2668, lr: 0.000100, took: 73.148s
[COOPERATIVE] Epoch 1, Batch 1390/1563, loss: 6.147, reward: 29.046, critic_reward: 30.479, revenue_rate: 0.7092, distance: 8.4538, memory: -0.0700, power: 0.2581, lr: 0.000100, took: 70.539s
[COOPERATIVE] Epoch 1, Batch 1400/1563, loss: 8.451, reward: 31.190, critic_reward: 29.508, revenue_rate: 0.7690, distance: 9.3484, memory: -0.0366, power: 0.2845, lr: 0.000100, took: 78.448s
[COOPERATIVE] Epoch 1, Batch 1410/1563, loss: 3.176, reward: 33.394, critic_reward: 33.529, revenue_rate: 0.8260, distance: 10.3430, memory: -0.0099, power: 0.3132, lr: 0.000100, took: 88.580s
[COOPERATIVE] Epoch 1, Batch 1420/1563, loss: 3.328, reward: 31.404, critic_reward: 31.839, revenue_rate: 0.7678, distance: 9.3164, memory: -0.0394, power: 0.2832, lr: 0.000100, took: 78.940s
[COOPERATIVE] Epoch 1, Batch 1430/1563, loss: 5.924, reward: 28.895, critic_reward: 27.878, revenue_rate: 0.7059, distance: 8.4529, memory: -0.0648, power: 0.2562, lr: 0.000100, took: 71.669s
[COOPERATIVE] Epoch 1, Batch 1440/1563, loss: 3.362, reward: 30.661, critic_reward: 30.747, revenue_rate: 0.7564, distance: 9.3302, memory: -0.0299, power: 0.2809, lr: 0.000100, took: 79.258s
[COOPERATIVE] Epoch 1, Batch 1450/1563, loss: 6.227, reward: 32.930, critic_reward: 32.260, revenue_rate: 0.8081, distance: 9.9750, memory: -0.0260, power: 0.3046, lr: 0.000100, took: 84.260s
[COOPERATIVE] Epoch 1, Batch 1460/1563, loss: 8.212, reward: 31.841, critic_reward: 32.112, revenue_rate: 0.7825, distance: 9.6208, memory: -0.0280, power: 0.2925, lr: 0.000100, took: 84.325s
[COOPERATIVE] Epoch 1, Batch 1470/1563, loss: 6.157, reward: 32.460, critic_reward: 31.915, revenue_rate: 0.7991, distance: 9.9048, memory: -0.0288, power: 0.3019, lr: 0.000100, took: 82.426s
[COOPERATIVE] Epoch 1, Batch 1480/1563, loss: 3.005, reward: 33.768, critic_reward: 34.159, revenue_rate: 0.8303, distance: 10.4445, memory: -0.0121, power: 0.3159, lr: 0.000100, took: 89.406s
[COOPERATIVE] Epoch 1, Batch 1490/1563, loss: 3.258, reward: 31.644, critic_reward: 31.146, revenue_rate: 0.7779, distance: 9.5130, memory: -0.0381, power: 0.2856, lr: 0.000100, took: 78.304s
[COOPERATIVE] Epoch 1, Batch 1500/1563, loss: 3.015, reward: 31.101, critic_reward: 31.215, revenue_rate: 0.7623, distance: 9.2429, memory: -0.0494, power: 0.2789, lr: 0.000100, took: 77.113s
[COOPERATIVE] Epoch 1, Batch 1510/1563, loss: 4.088, reward: 29.955, critic_reward: 29.137, revenue_rate: 0.7339, distance: 8.8834, memory: -0.0525, power: 0.2683, lr: 0.000100, took: 71.573s
[COOPERATIVE] Epoch 1, Batch 1520/1563, loss: 3.271, reward: 31.058, critic_reward: 31.125, revenue_rate: 0.7619, distance: 9.3817, memory: -0.0298, power: 0.2831, lr: 0.000100, took: 74.165s
[COOPERATIVE] Epoch 1, Batch 1530/1563, loss: 2.551, reward: 29.930, critic_reward: 30.429, revenue_rate: 0.7338, distance: 8.8311, memory: -0.0474, power: 0.2688, lr: 0.000100, took: 70.429s
[COOPERATIVE] Epoch 1, Batch 1540/1563, loss: 3.868, reward: 28.633, critic_reward: 28.489, revenue_rate: 0.6985, distance: 8.3161, memory: -0.0788, power: 0.2522, lr: 0.000100, took: 69.899s
[COOPERATIVE] Epoch 1, Batch 1550/1563, loss: 6.012, reward: 32.198, critic_reward: 31.623, revenue_rate: 0.7910, distance: 9.6998, memory: -0.0309, power: 0.2926, lr: 0.000100, took: 82.118s
[COOPERATIVE] Epoch 1, Batch 1560/1563, loss: 5.139, reward: 32.905, critic_reward: 33.150, revenue_rate: 0.8078, distance: 9.9537, memory: -0.0319, power: 0.3007, lr: 0.000100, took: 86.916s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 32.346, revenue_rate: 0.7933, distance: 9.6290, memory: -0.0335, power: 0.2919
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48 (验证集奖励: 32.3461)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/1563, loss: 2.949, reward: 29.810, critic_reward: 29.794, revenue_rate: 0.7312, distance: 8.8277, memory: -0.0606, power: 0.2639, lr: 0.000100, took: 87.877s
[COOPERATIVE] Epoch 2, Batch 20/1563, loss: 4.349, reward: 31.882, critic_reward: 32.346, revenue_rate: 0.7818, distance: 9.5674, memory: -0.0360, power: 0.2918, lr: 0.000100, took: 90.906s
[COOPERATIVE] Epoch 2, Batch 30/1563, loss: 2.573, reward: 33.985, critic_reward: 34.043, revenue_rate: 0.8415, distance: 10.7090, memory: 0.0089, power: 0.3261, lr: 0.000100, took: 102.575s
[COOPERATIVE] Epoch 2, Batch 40/1563, loss: 2.643, reward: 33.971, critic_reward: 33.866, revenue_rate: 0.8373, distance: 10.6338, memory: 0.0001, power: 0.3222, lr: 0.000100, took: 101.518s
[COOPERATIVE] Epoch 2, Batch 50/1563, loss: 3.862, reward: 33.685, critic_reward: 33.163, revenue_rate: 0.8293, distance: 10.4187, memory: -0.0142, power: 0.3149, lr: 0.000100, took: 96.593s
[COOPERATIVE] Epoch 2, Batch 60/1563, loss: 3.827, reward: 32.755, critic_reward: 32.658, revenue_rate: 0.8096, distance: 10.0050, memory: -0.0225, power: 0.3004, lr: 0.000100, took: 91.509s
[COOPERATIVE] Epoch 2, Batch 70/1563, loss: 2.540, reward: 33.065, critic_reward: 33.224, revenue_rate: 0.8120, distance: 10.0109, memory: -0.0168, power: 0.3029, lr: 0.000100, took: 89.696s
[COOPERATIVE] Epoch 2, Batch 80/1563, loss: 3.764, reward: 32.411, critic_reward: 32.584, revenue_rate: 0.7924, distance: 9.5741, memory: -0.0439, power: 0.2932, lr: 0.000100, took: 87.309s
[COOPERATIVE] Epoch 2, Batch 90/1563, loss: 3.448, reward: 29.276, critic_reward: 28.952, revenue_rate: 0.7131, distance: 8.4187, memory: -0.0664, power: 0.2560, lr: 0.000100, took: 77.108s
[COOPERATIVE] Epoch 2, Batch 100/1563, loss: 4.417, reward: 29.458, critic_reward: 30.238, revenue_rate: 0.7177, distance: 8.5288, memory: -0.0698, power: 0.2566, lr: 0.000100, took: 78.563s
[COOPERATIVE] Epoch 2, Batch 110/1563, loss: 5.616, reward: 32.538, critic_reward: 32.871, revenue_rate: 0.7974, distance: 9.9073, memory: -0.0252, power: 0.3010, lr: 0.000100, took: 92.119s
[COOPERATIVE] Epoch 2, Batch 120/1563, loss: 11.394, reward: 33.120, critic_reward: 33.812, revenue_rate: 0.8168, distance: 10.0506, memory: -0.0184, power: 0.3072, lr: 0.000100, took: 91.422s
[COOPERATIVE] Epoch 2, Batch 130/1563, loss: 8.133, reward: 30.251, critic_reward: 30.635, revenue_rate: 0.7407, distance: 8.8684, memory: -0.0572, power: 0.2704, lr: 0.000100, took: 79.035s
[COOPERATIVE] Epoch 2, Batch 140/1563, loss: 6.395, reward: 30.327, critic_reward: 31.868, revenue_rate: 0.7378, distance: 8.8792, memory: -0.0522, power: 0.2670, lr: 0.000100, took: 76.906s
[COOPERATIVE] Epoch 2, Batch 150/1563, loss: 4.987, reward: 31.049, critic_reward: 30.241, revenue_rate: 0.7623, distance: 9.1960, memory: -0.0498, power: 0.2775, lr: 0.000100, took: 80.014s
[COOPERATIVE] Epoch 2, Batch 160/1563, loss: 3.318, reward: 32.223, critic_reward: 32.797, revenue_rate: 0.7903, distance: 9.6308, memory: -0.0320, power: 0.2900, lr: 0.000100, took: 82.150s
[COOPERATIVE] Epoch 2, Batch 170/1563, loss: 3.213, reward: 32.879, critic_reward: 32.503, revenue_rate: 0.8089, distance: 9.9402, memory: -0.0185, power: 0.3006, lr: 0.000100, took: 83.088s
[COOPERATIVE] Epoch 2, Batch 180/1563, loss: 3.430, reward: 32.574, critic_reward: 32.285, revenue_rate: 0.7993, distance: 9.7959, memory: -0.0219, power: 0.2965, lr: 0.000100, took: 86.970s
[COOPERATIVE] Epoch 2, Batch 190/1563, loss: 2.219, reward: 31.446, critic_reward: 31.305, revenue_rate: 0.7681, distance: 9.2965, memory: -0.0429, power: 0.2818, lr: 0.000100, took: 83.254s
[COOPERATIVE] Epoch 2, Batch 200/1563, loss: 2.741, reward: 30.744, critic_reward: 30.400, revenue_rate: 0.7552, distance: 9.0979, memory: -0.0424, power: 0.2763, lr: 0.000100, took: 78.246s
[COOPERATIVE] Epoch 2, Batch 210/1563, loss: 4.598, reward: 33.493, critic_reward: 32.985, revenue_rate: 0.8262, distance: 10.3017, memory: -0.0167, power: 0.3130, lr: 0.000100, took: 91.209s
[COOPERATIVE] Epoch 2, Batch 220/1563, loss: 14.782, reward: 33.286, critic_reward: 35.958, revenue_rate: 0.8186, distance: 10.0686, memory: -0.0156, power: 0.3069, lr: 0.000100, took: 97.097s
[COOPERATIVE] Epoch 2, Batch 230/1563, loss: 5.897, reward: 31.643, critic_reward: 30.778, revenue_rate: 0.7791, distance: 9.5294, memory: -0.0401, power: 0.2891, lr: 0.000100, took: 81.520s
[COOPERATIVE] Epoch 2, Batch 240/1563, loss: 10.406, reward: 28.641, critic_reward: 29.124, revenue_rate: 0.6985, distance: 8.2429, memory: -0.0787, power: 0.2495, lr: 0.000100, took: 70.556s
[COOPERATIVE] Epoch 2, Batch 250/1563, loss: 6.384, reward: 29.109, critic_reward: 29.734, revenue_rate: 0.7088, distance: 8.3685, memory: -0.0756, power: 0.2548, lr: 0.000100, took: 72.856s
[COOPERATIVE] Epoch 2, Batch 260/1563, loss: 2.962, reward: 31.113, critic_reward: 31.268, revenue_rate: 0.7622, distance: 9.2082, memory: -0.0461, power: 0.2804, lr: 0.000100, took: 82.757s
[COOPERATIVE] Epoch 2, Batch 270/1563, loss: 2.994, reward: 32.109, critic_reward: 31.937, revenue_rate: 0.7880, distance: 9.6230, memory: -0.0310, power: 0.2939, lr: 0.000100, took: 86.847s
[COOPERATIVE] Epoch 2, Batch 280/1563, loss: 2.258, reward: 31.406, critic_reward: 31.425, revenue_rate: 0.7686, distance: 9.2755, memory: -0.0463, power: 0.2799, lr: 0.000100, took: 81.660s
[COOPERATIVE] Epoch 2, Batch 290/1563, loss: 3.594, reward: 32.145, critic_reward: 31.766, revenue_rate: 0.7867, distance: 9.6018, memory: -0.0337, power: 0.2911, lr: 0.000100, took: 84.704s
[COOPERATIVE] Epoch 2, Batch 300/1563, loss: 3.984, reward: 33.778, critic_reward: 33.448, revenue_rate: 0.8284, distance: 10.2994, memory: -0.0110, power: 0.3105, lr: 0.000100, took: 89.925s
[COOPERATIVE] Epoch 2, Batch 310/1563, loss: 2.285, reward: 33.849, critic_reward: 33.881, revenue_rate: 0.8317, distance: 10.4081, memory: -0.0079, power: 0.3151, lr: 0.000100, took: 91.103s
[COOPERATIVE] Epoch 2, Batch 320/1563, loss: 2.350, reward: 33.272, critic_reward: 33.111, revenue_rate: 0.8187, distance: 10.2591, memory: -0.0090, power: 0.3102, lr: 0.000100, took: 90.688s
[COOPERATIVE] Epoch 2, Batch 330/1563, loss: 3.101, reward: 32.999, critic_reward: 33.073, revenue_rate: 0.8107, distance: 10.0147, memory: -0.0260, power: 0.3051, lr: 0.000100, took: 93.648s
[COOPERATIVE] Epoch 2, Batch 340/1563, loss: 2.604, reward: 31.089, critic_reward: 31.039, revenue_rate: 0.7616, distance: 9.1762, memory: -0.0435, power: 0.2772, lr: 0.000100, took: 85.904s
[COOPERATIVE] Epoch 2, Batch 350/1563, loss: 2.532, reward: 29.932, critic_reward: 30.013, revenue_rate: 0.7327, distance: 8.7836, memory: -0.0567, power: 0.2665, lr: 0.000100, took: 80.993s
[COOPERATIVE] Epoch 2, Batch 360/1563, loss: 4.515, reward: 30.531, critic_reward: 30.522, revenue_rate: 0.7504, distance: 8.9998, memory: -0.0519, power: 0.2721, lr: 0.000100, took: 81.146s
[COOPERATIVE] Epoch 2, Batch 370/1563, loss: 3.343, reward: 33.123, critic_reward: 33.687, revenue_rate: 0.8147, distance: 10.0954, memory: -0.0262, power: 0.3070, lr: 0.000100, took: 87.820s
[COOPERATIVE] Epoch 2, Batch 380/1563, loss: 4.456, reward: 33.869, critic_reward: 34.233, revenue_rate: 0.8346, distance: 10.4315, memory: -0.0172, power: 0.3153, lr: 0.000100, took: 96.343s
[COOPERATIVE] Epoch 2, Batch 390/1563, loss: 8.052, reward: 31.247, critic_reward: 32.658, revenue_rate: 0.7642, distance: 9.2149, memory: -0.0427, power: 0.2801, lr: 0.000100, took: 83.504s
[COOPERATIVE] Epoch 2, Batch 400/1563, loss: 3.837, reward: 31.417, critic_reward: 31.113, revenue_rate: 0.7684, distance: 9.2108, memory: -0.0422, power: 0.2819, lr: 0.000100, took: 84.101s
[COOPERATIVE] Epoch 2, Batch 410/1563, loss: 2.841, reward: 34.166, critic_reward: 34.081, revenue_rate: 0.8403, distance: 10.4826, memory: -0.0077, power: 0.3183, lr: 0.000100, took: 94.305s
[COOPERATIVE] Epoch 2, Batch 420/1563, loss: 3.868, reward: 33.623, critic_reward: 34.508, revenue_rate: 0.8300, distance: 10.3052, memory: -0.0139, power: 0.3115, lr: 0.000100, took: 90.500s
[COOPERATIVE] Epoch 2, Batch 430/1563, loss: 3.020, reward: 32.294, critic_reward: 31.791, revenue_rate: 0.7900, distance: 9.6685, memory: -0.0333, power: 0.2932, lr: 0.000100, took: 89.981s
[COOPERATIVE] Epoch 2, Batch 440/1563, loss: 4.184, reward: 30.882, critic_reward: 30.739, revenue_rate: 0.7571, distance: 9.1501, memory: -0.0452, power: 0.2770, lr: 0.000100, took: 85.093s
[COOPERATIVE] Epoch 2, Batch 450/1563, loss: 3.627, reward: 31.890, critic_reward: 31.452, revenue_rate: 0.7784, distance: 9.6136, memory: -0.0320, power: 0.2906, lr: 0.000100, took: 90.730s
[COOPERATIVE] Epoch 2, Batch 460/1563, loss: 3.861, reward: 31.021, critic_reward: 31.181, revenue_rate: 0.7610, distance: 9.2584, memory: -0.0369, power: 0.2813, lr: 0.000100, took: 88.241s
[COOPERATIVE] Epoch 2, Batch 470/1563, loss: 3.574, reward: 30.876, critic_reward: 30.455, revenue_rate: 0.7546, distance: 9.1215, memory: -0.0509, power: 0.2766, lr: 0.000100, took: 81.589s
[COOPERATIVE] Epoch 2, Batch 480/1563, loss: 5.045, reward: 33.457, critic_reward: 34.038, revenue_rate: 0.8209, distance: 10.1123, memory: -0.0182, power: 0.3070, lr: 0.000100, took: 95.231s
[COOPERATIVE] Epoch 2, Batch 490/1563, loss: 4.998, reward: 33.279, critic_reward: 33.637, revenue_rate: 0.8157, distance: 10.0613, memory: -0.0237, power: 0.3033, lr: 0.000100, took: 91.448s
[COOPERATIVE] Epoch 2, Batch 500/1563, loss: 7.359, reward: 32.583, critic_reward: 30.783, revenue_rate: 0.8028, distance: 9.8138, memory: -0.0256, power: 0.2965, lr: 0.000100, took: 88.914s
[COOPERATIVE] Epoch 2, Batch 510/1563, loss: 5.082, reward: 31.614, critic_reward: 32.863, revenue_rate: 0.7731, distance: 9.3152, memory: -0.0476, power: 0.2837, lr: 0.000100, took: 79.294s
[COOPERATIVE] Epoch 2, Batch 520/1563, loss: 3.723, reward: 30.432, critic_reward: 29.453, revenue_rate: 0.7444, distance: 8.9312, memory: -0.0552, power: 0.2682, lr: 0.000100, took: 75.237s
[COOPERATIVE] Epoch 2, Batch 530/1563, loss: 2.889, reward: 31.182, critic_reward: 31.459, revenue_rate: 0.7588, distance: 9.1678, memory: -0.0482, power: 0.2789, lr: 0.000100, took: 80.708s
[COOPERATIVE] Epoch 2, Batch 540/1563, loss: 5.959, reward: 30.245, critic_reward: 31.333, revenue_rate: 0.7401, distance: 8.8648, memory: -0.0556, power: 0.2683, lr: 0.000100, took: 82.988s
[COOPERATIVE] Epoch 2, Batch 550/1563, loss: 5.129, reward: 31.683, critic_reward: 31.872, revenue_rate: 0.7776, distance: 9.4098, memory: -0.0406, power: 0.2864, lr: 0.000100, took: 89.541s
[COOPERATIVE] Epoch 2, Batch 560/1563, loss: 8.304, reward: 32.172, critic_reward: 30.269, revenue_rate: 0.7908, distance: 9.6304, memory: -0.0322, power: 0.2912, lr: 0.000100, took: 95.640s
[COOPERATIVE] Epoch 2, Batch 570/1563, loss: 12.985, reward: 30.006, critic_reward: 32.840, revenue_rate: 0.7351, distance: 8.7280, memory: -0.0631, power: 0.2655, lr: 0.000100, took: 80.299s
[COOPERATIVE] Epoch 2, Batch 580/1563, loss: 3.791, reward: 28.983, critic_reward: 28.128, revenue_rate: 0.7073, distance: 8.4106, memory: -0.0729, power: 0.2545, lr: 0.000100, took: 74.573s
[COOPERATIVE] Epoch 2, Batch 590/1563, loss: 2.467, reward: 27.873, critic_reward: 27.737, revenue_rate: 0.6804, distance: 8.1153, memory: -0.0796, power: 0.2432, lr: 0.000100, took: 67.496s
[COOPERATIVE] Epoch 2, Batch 600/1563, loss: 2.332, reward: 29.639, critic_reward: 29.371, revenue_rate: 0.7254, distance: 8.7273, memory: -0.0534, power: 0.2663, lr: 0.000100, took: 71.432s
[COOPERATIVE] Epoch 2, Batch 610/1563, loss: 3.517, reward: 31.627, critic_reward: 31.606, revenue_rate: 0.7734, distance: 9.5014, memory: -0.0382, power: 0.2881, lr: 0.000100, took: 78.796s
[COOPERATIVE] Epoch 2, Batch 620/1563, loss: 3.140, reward: 31.974, critic_reward: 31.438, revenue_rate: 0.7838, distance: 9.6299, memory: -0.0326, power: 0.2916, lr: 0.000100, took: 80.282s
[COOPERATIVE] Epoch 2, Batch 630/1563, loss: 2.644, reward: 33.621, critic_reward: 33.992, revenue_rate: 0.8278, distance: 10.3975, memory: -0.0050, power: 0.3142, lr: 0.000100, took: 87.397s
[COOPERATIVE] Epoch 2, Batch 640/1563, loss: 2.925, reward: 34.218, critic_reward: 33.771, revenue_rate: 0.8417, distance: 10.5604, memory: -0.0120, power: 0.3179, lr: 0.000100, took: 88.545s
[COOPERATIVE] Epoch 2, Batch 650/1563, loss: 3.285, reward: 33.298, critic_reward: 32.900, revenue_rate: 0.8144, distance: 10.0482, memory: -0.0221, power: 0.3031, lr: 0.000100, took: 86.109s
[COOPERATIVE] Epoch 2, Batch 660/1563, loss: 2.996, reward: 32.459, critic_reward: 32.881, revenue_rate: 0.7961, distance: 9.6336, memory: -0.0322, power: 0.2939, lr: 0.000100, took: 80.754s
[COOPERATIVE] Epoch 2, Batch 670/1563, loss: 3.487, reward: 31.451, critic_reward: 31.624, revenue_rate: 0.7707, distance: 9.3474, memory: -0.0456, power: 0.2847, lr: 0.000100, took: 79.564s
[COOPERATIVE] Epoch 2, Batch 680/1563, loss: 2.915, reward: 28.837, critic_reward: 28.793, revenue_rate: 0.7035, distance: 8.2526, memory: -0.0807, power: 0.2510, lr: 0.000100, took: 67.620s
[COOPERATIVE] Epoch 2, Batch 690/1563, loss: 3.036, reward: 30.013, critic_reward: 29.918, revenue_rate: 0.7339, distance: 8.7923, memory: -0.0538, power: 0.2653, lr: 0.000100, took: 70.165s
[COOPERATIVE] Epoch 2, Batch 700/1563, loss: 2.506, reward: 30.598, critic_reward: 30.714, revenue_rate: 0.7482, distance: 8.9658, memory: -0.0600, power: 0.2698, lr: 0.000100, took: 75.352s
[COOPERATIVE] Epoch 2, Batch 710/1563, loss: 4.119, reward: 30.086, critic_reward: 30.192, revenue_rate: 0.7359, distance: 8.9864, memory: -0.0568, power: 0.2713, lr: 0.000100, took: 74.034s
[COOPERATIVE] Epoch 2, Batch 720/1563, loss: 2.577, reward: 29.852, critic_reward: 30.017, revenue_rate: 0.7278, distance: 8.7090, memory: -0.0626, power: 0.2652, lr: 0.000100, took: 71.516s
[COOPERATIVE] Epoch 2, Batch 730/1563, loss: 2.638, reward: 30.160, critic_reward: 30.305, revenue_rate: 0.7384, distance: 8.7889, memory: -0.0524, power: 0.2684, lr: 0.000100, took: 72.778s
[COOPERATIVE] Epoch 2, Batch 740/1563, loss: 2.547, reward: 33.079, critic_reward: 32.631, revenue_rate: 0.8129, distance: 9.9688, memory: -0.0209, power: 0.3034, lr: 0.000100, took: 83.710s
[COOPERATIVE] Epoch 2, Batch 750/1563, loss: 3.285, reward: 35.886, critic_reward: 35.932, revenue_rate: 0.8884, distance: 11.5028, memory: 0.0242, power: 0.3469, lr: 0.000100, took: 98.224s
[COOPERATIVE] Epoch 2, Batch 760/1563, loss: 3.703, reward: 35.647, critic_reward: 36.120, revenue_rate: 0.8857, distance: 11.4147, memory: 0.0284, power: 0.3452, lr: 0.000100, took: 99.664s
[COOPERATIVE] Epoch 2, Batch 770/1563, loss: 4.063, reward: 33.619, critic_reward: 33.803, revenue_rate: 0.8235, distance: 10.1695, memory: -0.0204, power: 0.3093, lr: 0.000100, took: 85.602s
[COOPERATIVE] Epoch 2, Batch 780/1563, loss: 4.161, reward: 31.652, critic_reward: 31.321, revenue_rate: 0.7751, distance: 9.3450, memory: -0.0411, power: 0.2830, lr: 0.000100, took: 77.447s
[COOPERATIVE] Epoch 2, Batch 790/1563, loss: 2.150, reward: 31.058, critic_reward: 31.430, revenue_rate: 0.7608, distance: 9.1546, memory: -0.0442, power: 0.2784, lr: 0.000100, took: 78.172s
[COOPERATIVE] Epoch 2, Batch 800/1563, loss: 6.465, reward: 31.156, critic_reward: 29.823, revenue_rate: 0.7640, distance: 9.2575, memory: -0.0421, power: 0.2795, lr: 0.000100, took: 76.403s
[COOPERATIVE] Epoch 2, Batch 810/1563, loss: 10.325, reward: 31.422, critic_reward: 31.169, revenue_rate: 0.7680, distance: 9.1927, memory: -0.0479, power: 0.2814, lr: 0.000100, took: 78.222s
[COOPERATIVE] Epoch 2, Batch 820/1563, loss: 7.666, reward: 34.005, critic_reward: 33.299, revenue_rate: 0.8376, distance: 10.5249, memory: -0.0047, power: 0.3167, lr: 0.000100, took: 87.598s
[COOPERATIVE] Epoch 2, Batch 830/1563, loss: 3.896, reward: 34.225, critic_reward: 35.039, revenue_rate: 0.8453, distance: 10.6264, memory: -0.0030, power: 0.3189, lr: 0.000100, took: 89.527s
[COOPERATIVE] Epoch 2, Batch 840/1563, loss: 2.683, reward: 32.353, critic_reward: 32.805, revenue_rate: 0.7931, distance: 9.6122, memory: -0.0365, power: 0.2926, lr: 0.000100, took: 79.917s
[COOPERATIVE] Epoch 2, Batch 850/1563, loss: 2.882, reward: 28.563, critic_reward: 27.902, revenue_rate: 0.6961, distance: 8.2000, memory: -0.0753, power: 0.2478, lr: 0.000100, took: 67.151s
[COOPERATIVE] Epoch 2, Batch 860/1563, loss: 3.419, reward: 27.276, critic_reward: 27.033, revenue_rate: 0.6627, distance: 7.6606, memory: -0.0857, power: 0.2348, lr: 0.000100, took: 63.001s
[COOPERATIVE] Epoch 2, Batch 870/1563, loss: 4.228, reward: 29.680, critic_reward: 30.173, revenue_rate: 0.7246, distance: 8.6043, memory: -0.0631, power: 0.2599, lr: 0.000100, took: 70.795s
[COOPERATIVE] Epoch 2, Batch 880/1563, loss: 3.242, reward: 34.173, critic_reward: 33.630, revenue_rate: 0.8395, distance: 10.4071, memory: -0.0218, power: 0.3156, lr: 0.000100, took: 90.296s
[COOPERATIVE] Epoch 2, Batch 890/1563, loss: 3.508, reward: 34.995, critic_reward: 35.764, revenue_rate: 0.8664, distance: 11.0859, memory: 0.0064, power: 0.3354, lr: 0.000100, took: 93.603s
[COOPERATIVE] Epoch 2, Batch 900/1563, loss: 2.641, reward: 33.937, critic_reward: 34.010, revenue_rate: 0.8384, distance: 10.5234, memory: -0.0028, power: 0.3214, lr: 0.000100, took: 92.306s
[COOPERATIVE] Epoch 2, Batch 910/1563, loss: 4.763, reward: 32.548, critic_reward: 33.556, revenue_rate: 0.7978, distance: 9.7342, memory: -0.0301, power: 0.2977, lr: 0.000100, took: 82.278s
[COOPERATIVE] Epoch 2, Batch 920/1563, loss: 4.819, reward: 28.566, critic_reward: 27.908, revenue_rate: 0.6953, distance: 8.2926, memory: -0.0769, power: 0.2488, lr: 0.000100, took: 67.977s
[COOPERATIVE] Epoch 2, Batch 930/1563, loss: 5.308, reward: 31.566, critic_reward: 31.686, revenue_rate: 0.7740, distance: 9.4476, memory: -0.0413, power: 0.2833, lr: 0.000100, took: 77.239s
[COOPERATIVE] Epoch 2, Batch 940/1563, loss: 2.712, reward: 32.919, critic_reward: 32.775, revenue_rate: 0.8083, distance: 9.8662, memory: -0.0208, power: 0.2992, lr: 0.000100, took: 79.478s
[COOPERATIVE] Epoch 2, Batch 950/1563, loss: 3.594, reward: 32.880, critic_reward: 33.331, revenue_rate: 0.8043, distance: 9.7575, memory: -0.0290, power: 0.2980, lr: 0.000100, took: 81.829s
[COOPERATIVE] Epoch 2, Batch 960/1563, loss: 5.547, reward: 30.362, critic_reward: 29.823, revenue_rate: 0.7432, distance: 8.8734, memory: -0.0540, power: 0.2679, lr: 0.000100, took: 72.968s
[COOPERATIVE] Epoch 2, Batch 970/1563, loss: 4.965, reward: 31.133, critic_reward: 30.664, revenue_rate: 0.7610, distance: 9.1914, memory: -0.0491, power: 0.2760, lr: 0.000100, took: 75.665s
[COOPERATIVE] Epoch 2, Batch 980/1563, loss: 2.764, reward: 31.056, critic_reward: 31.679, revenue_rate: 0.7582, distance: 9.0702, memory: -0.0510, power: 0.2761, lr: 0.000100, took: 75.269s
[COOPERATIVE] Epoch 2, Batch 990/1563, loss: 2.720, reward: 29.512, critic_reward: 29.332, revenue_rate: 0.7219, distance: 8.5577, memory: -0.0647, power: 0.2582, lr: 0.000100, took: 72.615s
[COOPERATIVE] Epoch 2, Batch 1000/1563, loss: 4.375, reward: 30.600, critic_reward: 30.144, revenue_rate: 0.7490, distance: 8.9272, memory: -0.0607, power: 0.2714, lr: 0.000100, took: 74.607s
[COOPERATIVE] Epoch 2, Batch 1010/1563, loss: 4.329, reward: 31.346, critic_reward: 32.202, revenue_rate: 0.7663, distance: 9.1869, memory: -0.0451, power: 0.2809, lr: 0.000100, took: 78.868s
[COOPERATIVE] Epoch 2, Batch 1020/1563, loss: 3.420, reward: 32.030, critic_reward: 31.082, revenue_rate: 0.7856, distance: 9.5205, memory: -0.0364, power: 0.2892, lr: 0.000100, took: 79.326s
[COOPERATIVE] Epoch 2, Batch 1030/1563, loss: 3.695, reward: 32.858, critic_reward: 32.950, revenue_rate: 0.8095, distance: 10.0359, memory: -0.0190, power: 0.3042, lr: 0.000100, took: 83.951s
[COOPERATIVE] Epoch 2, Batch 1040/1563, loss: 5.128, reward: 32.471, critic_reward: 32.216, revenue_rate: 0.7976, distance: 9.7074, memory: -0.0311, power: 0.2944, lr: 0.000100, took: 83.190s
[COOPERATIVE] Epoch 2, Batch 1050/1563, loss: 2.242, reward: 32.753, critic_reward: 32.509, revenue_rate: 0.8036, distance: 9.9188, memory: -0.0323, power: 0.2990, lr: 0.000100, took: 81.969s
[COOPERATIVE] Epoch 2, Batch 1060/1563, loss: 3.548, reward: 33.574, critic_reward: 34.283, revenue_rate: 0.8297, distance: 10.3561, memory: -0.0070, power: 0.3127, lr: 0.000100, took: 86.538s
[COOPERATIVE] Epoch 2, Batch 1070/1563, loss: 3.730, reward: 33.970, critic_reward: 34.419, revenue_rate: 0.8369, distance: 10.4713, memory: -0.0151, power: 0.3159, lr: 0.000100, took: 87.870s
[COOPERATIVE] Epoch 2, Batch 1080/1563, loss: 4.704, reward: 34.067, critic_reward: 33.464, revenue_rate: 0.8381, distance: 10.4322, memory: -0.0028, power: 0.3169, lr: 0.000100, took: 87.706s
[COOPERATIVE] Epoch 2, Batch 1090/1563, loss: 3.484, reward: 33.721, critic_reward: 34.439, revenue_rate: 0.8275, distance: 10.2217, memory: -0.0181, power: 0.3094, lr: 0.000100, took: 85.420s
[COOPERATIVE] Epoch 2, Batch 1100/1563, loss: 3.806, reward: 34.156, critic_reward: 33.364, revenue_rate: 0.8429, distance: 10.3951, memory: -0.0116, power: 0.3157, lr: 0.000100, took: 90.392s
[COOPERATIVE] Epoch 2, Batch 1110/1563, loss: 4.030, reward: 33.874, critic_reward: 33.543, revenue_rate: 0.8322, distance: 10.2116, memory: -0.0122, power: 0.3117, lr: 0.000100, took: 85.895s
[COOPERATIVE] Epoch 2, Batch 1120/1563, loss: 3.713, reward: 32.043, critic_reward: 32.032, revenue_rate: 0.7847, distance: 9.5844, memory: -0.0296, power: 0.2896, lr: 0.000100, took: 81.370s
[COOPERATIVE] Epoch 2, Batch 1130/1563, loss: 5.979, reward: 33.311, critic_reward: 32.565, revenue_rate: 0.8128, distance: 10.0688, memory: -0.0176, power: 0.3049, lr: 0.000100, took: 84.019s
[COOPERATIVE] Epoch 2, Batch 1140/1563, loss: 6.295, reward: 31.396, critic_reward: 32.184, revenue_rate: 0.7676, distance: 9.2493, memory: -0.0402, power: 0.2803, lr: 0.000100, took: 78.687s
[COOPERATIVE] Epoch 2, Batch 1150/1563, loss: 6.362, reward: 32.006, critic_reward: 30.271, revenue_rate: 0.7810, distance: 9.4934, memory: -0.0299, power: 0.2887, lr: 0.000100, took: 78.828s
[COOPERATIVE] Epoch 2, Batch 1160/1563, loss: 9.416, reward: 34.511, critic_reward: 36.660, revenue_rate: 0.8518, distance: 10.7352, memory: -0.0087, power: 0.3236, lr: 0.000100, took: 90.443s
[COOPERATIVE] Epoch 2, Batch 1170/1563, loss: 5.786, reward: 33.611, critic_reward: 32.712, revenue_rate: 0.8299, distance: 10.3486, memory: -0.0056, power: 0.3118, lr: 0.000100, took: 86.882s
[COOPERATIVE] Epoch 2, Batch 1180/1563, loss: 4.353, reward: 33.070, critic_reward: 33.523, revenue_rate: 0.8109, distance: 9.9767, memory: -0.0325, power: 0.3014, lr: 0.000100, took: 82.765s
[COOPERATIVE] Epoch 2, Batch 1190/1563, loss: 4.572, reward: 33.040, critic_reward: 32.450, revenue_rate: 0.8066, distance: 9.7916, memory: -0.0329, power: 0.2983, lr: 0.000100, took: 81.779s
[COOPERATIVE] Epoch 2, Batch 1200/1563, loss: 2.880, reward: 34.319, critic_reward: 34.467, revenue_rate: 0.8432, distance: 10.4777, memory: -0.0049, power: 0.3173, lr: 0.000100, took: 89.846s
[COOPERATIVE] Epoch 2, Batch 1210/1563, loss: 3.173, reward: 34.526, critic_reward: 33.756, revenue_rate: 0.8505, distance: 10.6587, memory: 0.0014, power: 0.3227, lr: 0.000100, took: 85.974s
[COOPERATIVE] Epoch 2, Batch 1220/1563, loss: 5.038, reward: 32.971, critic_reward: 33.783, revenue_rate: 0.8120, distance: 10.0695, memory: -0.0279, power: 0.3007, lr: 0.000100, took: 82.521s
[COOPERATIVE] Epoch 2, Batch 1230/1563, loss: 2.411, reward: 30.221, critic_reward: 30.688, revenue_rate: 0.7383, distance: 8.7748, memory: -0.0598, power: 0.2677, lr: 0.000100, took: 74.722s
[COOPERATIVE] Epoch 2, Batch 1240/1563, loss: 3.154, reward: 27.780, critic_reward: 27.363, revenue_rate: 0.6775, distance: 7.9415, memory: -0.0758, power: 0.2420, lr: 0.000100, took: 64.946s
[COOPERATIVE] Epoch 2, Batch 1250/1563, loss: 6.205, reward: 30.718, critic_reward: 31.709, revenue_rate: 0.7521, distance: 8.9666, memory: -0.0599, power: 0.2733, lr: 0.000100, took: 76.071s
[COOPERATIVE] Epoch 2, Batch 1260/1563, loss: 3.655, reward: 31.491, critic_reward: 30.846, revenue_rate: 0.7700, distance: 9.2640, memory: -0.0456, power: 0.2824, lr: 0.000100, took: 76.326s
[COOPERATIVE] Epoch 2, Batch 1270/1563, loss: 2.275, reward: 33.013, critic_reward: 32.783, revenue_rate: 0.8112, distance: 9.9560, memory: -0.0163, power: 0.3014, lr: 0.000100, took: 83.028s
[COOPERATIVE] Epoch 2, Batch 1280/1563, loss: 2.362, reward: 34.919, critic_reward: 34.967, revenue_rate: 0.8585, distance: 10.7732, memory: 0.0015, power: 0.3272, lr: 0.000100, took: 90.422s
[COOPERATIVE] Epoch 2, Batch 1290/1563, loss: 2.167, reward: 34.668, critic_reward: 34.310, revenue_rate: 0.8556, distance: 10.7286, memory: 0.0015, power: 0.3259, lr: 0.000100, took: 87.319s
[COOPERATIVE] Epoch 2, Batch 1300/1563, loss: 2.852, reward: 34.334, critic_reward: 34.413, revenue_rate: 0.8479, distance: 10.5910, memory: -0.0024, power: 0.3206, lr: 0.000100, took: 88.619s
[COOPERATIVE] Epoch 2, Batch 1310/1563, loss: 3.443, reward: 32.551, critic_reward: 31.990, revenue_rate: 0.7990, distance: 9.7266, memory: -0.0369, power: 0.2960, lr: 0.000100, took: 82.542s
[COOPERATIVE] Epoch 2, Batch 1320/1563, loss: 3.453, reward: 31.081, critic_reward: 31.588, revenue_rate: 0.7572, distance: 9.1440, memory: -0.0511, power: 0.2774, lr: 0.000100, took: 74.895s
[COOPERATIVE] Epoch 2, Batch 1330/1563, loss: 2.627, reward: 30.888, critic_reward: 30.547, revenue_rate: 0.7574, distance: 9.1214, memory: -0.0433, power: 0.2754, lr: 0.000100, took: 74.962s
[COOPERATIVE] Epoch 2, Batch 1340/1563, loss: 3.596, reward: 31.337, critic_reward: 31.006, revenue_rate: 0.7664, distance: 9.3288, memory: -0.0533, power: 0.2806, lr: 0.000100, took: 78.613s
[COOPERATIVE] Epoch 2, Batch 1350/1563, loss: 2.496, reward: 31.266, critic_reward: 31.278, revenue_rate: 0.7635, distance: 9.1333, memory: -0.0493, power: 0.2787, lr: 0.000100, took: 75.398s
[COOPERATIVE] Epoch 2, Batch 1360/1563, loss: 2.918, reward: 31.011, critic_reward: 30.673, revenue_rate: 0.7591, distance: 9.1651, memory: -0.0417, power: 0.2791, lr: 0.000100, took: 74.165s
[COOPERATIVE] Epoch 2, Batch 1370/1563, loss: 2.448, reward: 33.113, critic_reward: 32.889, revenue_rate: 0.8160, distance: 10.1482, memory: -0.0155, power: 0.3085, lr: 0.000100, took: 86.074s
[COOPERATIVE] Epoch 2, Batch 1380/1563, loss: 2.302, reward: 32.842, critic_reward: 32.596, revenue_rate: 0.8067, distance: 9.9009, memory: -0.0332, power: 0.2985, lr: 0.000100, took: 81.688s
[COOPERATIVE] Epoch 2, Batch 1390/1563, loss: 2.431, reward: 32.227, critic_reward: 32.578, revenue_rate: 0.7924, distance: 9.6459, memory: -0.0279, power: 0.2906, lr: 0.000100, took: 79.571s
[COOPERATIVE] Epoch 2, Batch 1400/1563, loss: 2.970, reward: 32.764, critic_reward: 32.704, revenue_rate: 0.8019, distance: 9.7136, memory: -0.0401, power: 0.2956, lr: 0.000100, took: 80.842s
[COOPERATIVE] Epoch 2, Batch 1410/1563, loss: 3.849, reward: 33.940, critic_reward: 33.636, revenue_rate: 0.8347, distance: 10.3190, memory: -0.0192, power: 0.3139, lr: 0.000100, took: 86.588s
[COOPERATIVE] Epoch 2, Batch 1420/1563, loss: 3.186, reward: 33.659, critic_reward: 34.337, revenue_rate: 0.8274, distance: 10.1470, memory: -0.0112, power: 0.3094, lr: 0.000100, took: 85.686s
[COOPERATIVE] Epoch 2, Batch 1430/1563, loss: 2.410, reward: 33.006, critic_reward: 32.491, revenue_rate: 0.8102, distance: 10.0112, memory: -0.0234, power: 0.3003, lr: 0.000100, took: 85.222s
[COOPERATIVE] Epoch 2, Batch 1440/1563, loss: 2.826, reward: 33.764, critic_reward: 33.447, revenue_rate: 0.8308, distance: 10.1863, memory: -0.0163, power: 0.3106, lr: 0.000100, took: 85.168s
[COOPERATIVE] Epoch 2, Batch 1450/1563, loss: 2.866, reward: 33.616, critic_reward: 33.697, revenue_rate: 0.8256, distance: 10.1269, memory: -0.0245, power: 0.3076, lr: 0.000100, took: 85.956s
[COOPERATIVE] Epoch 2, Batch 1460/1563, loss: 3.068, reward: 34.039, critic_reward: 33.590, revenue_rate: 0.8379, distance: 10.3740, memory: -0.0134, power: 0.3139, lr: 0.000100, took: 86.457s
[COOPERATIVE] Epoch 2, Batch 1470/1563, loss: 2.128, reward: 33.444, critic_reward: 33.497, revenue_rate: 0.8215, distance: 10.0622, memory: -0.0220, power: 0.3052, lr: 0.000100, took: 85.629s
[COOPERATIVE] Epoch 2, Batch 1480/1563, loss: 5.122, reward: 33.844, critic_reward: 32.856, revenue_rate: 0.8323, distance: 10.2069, memory: -0.0215, power: 0.3085, lr: 0.000100, took: 84.691s
[COOPERATIVE] Epoch 2, Batch 1490/1563, loss: 2.523, reward: 34.390, critic_reward: 34.570, revenue_rate: 0.8439, distance: 10.4521, memory: -0.0089, power: 0.3179, lr: 0.000100, took: 87.152s
[COOPERATIVE] Epoch 2, Batch 1500/1563, loss: 2.480, reward: 34.186, critic_reward: 34.311, revenue_rate: 0.8417, distance: 10.3828, memory: -0.0078, power: 0.3151, lr: 0.000100, took: 87.250s
[COOPERATIVE] Epoch 2, Batch 1510/1563, loss: 4.815, reward: 32.710, critic_reward: 33.266, revenue_rate: 0.8028, distance: 9.7850, memory: -0.0228, power: 0.2969, lr: 0.000100, took: 81.679s
[COOPERATIVE] Epoch 2, Batch 1520/1563, loss: 7.251, reward: 32.817, critic_reward: 31.159, revenue_rate: 0.8013, distance: 9.6819, memory: -0.0312, power: 0.2958, lr: 0.000100, took: 81.828s
[COOPERATIVE] Epoch 2, Batch 1530/1563, loss: 2.809, reward: 33.733, critic_reward: 33.744, revenue_rate: 0.8253, distance: 10.0824, memory: -0.0244, power: 0.3069, lr: 0.000100, took: 86.542s
[COOPERATIVE] Epoch 2, Batch 1540/1563, loss: 5.389, reward: 31.399, critic_reward: 32.509, revenue_rate: 0.7678, distance: 9.1664, memory: -0.0518, power: 0.2798, lr: 0.000100, took: 75.673s
[COOPERATIVE] Epoch 2, Batch 1550/1563, loss: 3.261, reward: 31.085, critic_reward: 30.510, revenue_rate: 0.7592, distance: 9.1004, memory: -0.0547, power: 0.2758, lr: 0.000100, took: 75.001s
[COOPERATIVE] Epoch 2, Batch 1560/1563, loss: 3.236, reward: 32.582, critic_reward: 32.924, revenue_rate: 0.7995, distance: 9.7430, memory: -0.0329, power: 0.2958, lr: 0.000100, took: 82.857s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 33.461, revenue_rate: 0.8221, distance: 10.0747, memory: -0.0212, power: 0.3053
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48 (验证集奖励: 33.4610)
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/1563, loss: 4.753, reward: 32.789, critic_reward: 33.617, revenue_rate: 0.8075, distance: 9.9017, memory: -0.0239, power: 0.2990, lr: 0.000100, took: 84.311s
[COOPERATIVE] Epoch 3, Batch 20/1563, loss: 3.185, reward: 33.676, critic_reward: 32.919, revenue_rate: 0.8295, distance: 10.2383, memory: -0.0218, power: 0.3081, lr: 0.000100, took: 86.588s
[COOPERATIVE] Epoch 3, Batch 30/1563, loss: 2.761, reward: 34.138, critic_reward: 34.361, revenue_rate: 0.8398, distance: 10.3556, memory: 0.0005, power: 0.3136, lr: 0.000100, took: 89.165s
[COOPERATIVE] Epoch 3, Batch 40/1563, loss: 3.553, reward: 32.814, critic_reward: 33.522, revenue_rate: 0.8099, distance: 9.8367, memory: -0.0261, power: 0.2991, lr: 0.000100, took: 82.126s
[COOPERATIVE] Epoch 3, Batch 50/1563, loss: 5.063, reward: 31.870, critic_reward: 30.493, revenue_rate: 0.7801, distance: 9.3483, memory: -0.0399, power: 0.2830, lr: 0.000100, took: 77.348s
[COOPERATIVE] Epoch 3, Batch 60/1563, loss: 11.101, reward: 31.794, critic_reward: 33.776, revenue_rate: 0.7779, distance: 9.3361, memory: -0.0440, power: 0.2846, lr: 0.000100, took: 78.729s
[COOPERATIVE] Epoch 3, Batch 70/1563, loss: 8.100, reward: 29.871, critic_reward: 30.294, revenue_rate: 0.7283, distance: 8.5797, memory: -0.0735, power: 0.2616, lr: 0.000100, took: 70.255s
[COOPERATIVE] Epoch 3, Batch 80/1563, loss: 5.118, reward: 31.307, critic_reward: 30.602, revenue_rate: 0.7629, distance: 9.0701, memory: -0.0522, power: 0.2761, lr: 0.000100, took: 76.946s
[COOPERATIVE] Epoch 3, Batch 90/1563, loss: 3.435, reward: 32.334, critic_reward: 32.761, revenue_rate: 0.7928, distance: 9.6025, memory: -0.0335, power: 0.2889, lr: 0.000100, took: 78.993s
[COOPERATIVE] Epoch 3, Batch 100/1563, loss: 4.980, reward: 33.159, critic_reward: 32.451, revenue_rate: 0.8109, distance: 9.9664, memory: -0.0289, power: 0.2989, lr: 0.000100, took: 82.177s
[COOPERATIVE] Epoch 3, Batch 110/1563, loss: 2.875, reward: 33.540, critic_reward: 34.029, revenue_rate: 0.8203, distance: 10.0372, memory: -0.0192, power: 0.3045, lr: 0.000100, took: 83.206s
[COOPERATIVE] Epoch 3, Batch 120/1563, loss: 3.984, reward: 32.956, critic_reward: 31.973, revenue_rate: 0.8094, distance: 9.8522, memory: -0.0213, power: 0.3005, lr: 0.000100, took: 79.589s
[COOPERATIVE] Epoch 3, Batch 130/1563, loss: 4.383, reward: 31.981, critic_reward: 33.179, revenue_rate: 0.7792, distance: 9.3436, memory: -0.0512, power: 0.2842, lr: 0.000100, took: 77.713s
[COOPERATIVE] Epoch 3, Batch 140/1563, loss: 4.326, reward: 31.596, critic_reward: 30.153, revenue_rate: 0.7735, distance: 9.2254, memory: -0.0413, power: 0.2809, lr: 0.000100, took: 76.038s
[COOPERATIVE] Epoch 3, Batch 150/1563, loss: 3.404, reward: 33.847, critic_reward: 34.862, revenue_rate: 0.8290, distance: 10.2068, memory: -0.0131, power: 0.3092, lr: 0.000100, took: 84.172s
[COOPERATIVE] Epoch 3, Batch 160/1563, loss: 2.148, reward: 34.216, critic_reward: 34.127, revenue_rate: 0.8411, distance: 10.4678, memory: -0.0102, power: 0.3158, lr: 0.000100, took: 88.598s
[COOPERATIVE] Epoch 3, Batch 170/1563, loss: 2.636, reward: 33.384, critic_reward: 33.680, revenue_rate: 0.8200, distance: 10.0845, memory: -0.0209, power: 0.3051, lr: 0.000100, took: 86.291s
[COOPERATIVE] Epoch 3, Batch 180/1563, loss: 2.202, reward: 31.119, critic_reward: 31.263, revenue_rate: 0.7616, distance: 9.1770, memory: -0.0439, power: 0.2770, lr: 0.000100, took: 75.344s
[COOPERATIVE] Epoch 3, Batch 190/1563, loss: 2.675, reward: 30.265, critic_reward: 29.867, revenue_rate: 0.7384, distance: 8.7206, memory: -0.0511, power: 0.2664, lr: 0.000100, took: 71.599s
[COOPERATIVE] Epoch 3, Batch 200/1563, loss: 3.843, reward: 30.506, critic_reward: 30.566, revenue_rate: 0.7496, distance: 9.0682, memory: -0.0381, power: 0.2767, lr: 0.000100, took: 77.221s
[COOPERATIVE] Epoch 3, Batch 210/1563, loss: 4.973, reward: 32.691, critic_reward: 32.805, revenue_rate: 0.8006, distance: 9.7887, memory: -0.0306, power: 0.2963, lr: 0.000100, took: 80.936s
[COOPERATIVE] Epoch 3, Batch 220/1563, loss: 6.601, reward: 31.715, critic_reward: 31.599, revenue_rate: 0.7772, distance: 9.3505, memory: -0.0449, power: 0.2837, lr: 0.000100, took: 76.851s
[COOPERATIVE] Epoch 3, Batch 230/1563, loss: 3.097, reward: 32.164, critic_reward: 31.975, revenue_rate: 0.7842, distance: 9.5225, memory: -0.0345, power: 0.2884, lr: 0.000100, took: 78.337s
[COOPERATIVE] Epoch 3, Batch 240/1563, loss: 3.734, reward: 32.067, critic_reward: 31.727, revenue_rate: 0.7844, distance: 9.4891, memory: -0.0400, power: 0.2866, lr: 0.000100, took: 75.791s
[COOPERATIVE] Epoch 3, Batch 250/1563, loss: 3.111, reward: 32.332, critic_reward: 32.838, revenue_rate: 0.7910, distance: 9.5432, memory: -0.0408, power: 0.2908, lr: 0.000100, took: 76.915s
[COOPERATIVE] Epoch 3, Batch 260/1563, loss: 3.576, reward: 31.449, critic_reward: 30.417, revenue_rate: 0.7667, distance: 9.1462, memory: -0.0444, power: 0.2816, lr: 0.000100, took: 78.224s
[COOPERATIVE] Epoch 3, Batch 270/1563, loss: 3.639, reward: 31.760, critic_reward: 32.265, revenue_rate: 0.7792, distance: 9.3649, memory: -0.0351, power: 0.2855, lr: 0.000100, took: 77.734s
[COOPERATIVE] Epoch 3, Batch 280/1563, loss: 3.614, reward: 32.933, critic_reward: 32.361, revenue_rate: 0.8039, distance: 9.7703, memory: -0.0248, power: 0.2973, lr: 0.000100, took: 81.621s
[COOPERATIVE] Epoch 3, Batch 290/1563, loss: 3.223, reward: 33.226, critic_reward: 33.526, revenue_rate: 0.8158, distance: 9.9899, memory: -0.0174, power: 0.3009, lr: 0.000100, took: 86.028s
[COOPERATIVE] Epoch 3, Batch 300/1563, loss: 2.455, reward: 33.228, critic_reward: 33.166, revenue_rate: 0.8140, distance: 9.9214, memory: -0.0229, power: 0.3005, lr: 0.000100, took: 82.693s
[COOPERATIVE] Epoch 3, Batch 310/1563, loss: 2.859, reward: 35.235, critic_reward: 35.059, revenue_rate: 0.8695, distance: 10.9737, memory: 0.0090, power: 0.3311, lr: 0.000100, took: 94.895s
[COOPERATIVE] Epoch 3, Batch 320/1563, loss: 2.620, reward: 34.711, critic_reward: 35.037, revenue_rate: 0.8525, distance: 10.5488, memory: 0.0020, power: 0.3194, lr: 0.000100, took: 85.536s
[COOPERATIVE] Epoch 3, Batch 330/1563, loss: 4.875, reward: 33.797, critic_reward: 34.348, revenue_rate: 0.8285, distance: 10.2081, memory: -0.0220, power: 0.3080, lr: 0.000100, took: 86.088s
[COOPERATIVE] Epoch 3, Batch 340/1563, loss: 3.729, reward: 31.905, critic_reward: 32.136, revenue_rate: 0.7771, distance: 9.2783, memory: -0.0413, power: 0.2839, lr: 0.000100, took: 77.315s
[COOPERATIVE] Epoch 3, Batch 350/1563, loss: 4.534, reward: 32.972, critic_reward: 32.013, revenue_rate: 0.8084, distance: 9.7740, memory: -0.0347, power: 0.2988, lr: 0.000100, took: 81.633s
[COOPERATIVE] Epoch 3, Batch 360/1563, loss: 5.503, reward: 34.460, critic_reward: 35.921, revenue_rate: 0.8489, distance: 10.4982, memory: -0.0059, power: 0.3187, lr: 0.000100, took: 87.822s
[COOPERATIVE] Epoch 3, Batch 370/1563, loss: 2.690, reward: 33.552, critic_reward: 33.125, revenue_rate: 0.8243, distance: 10.0222, memory: -0.0187, power: 0.3070, lr: 0.000100, took: 86.569s
[COOPERATIVE] Epoch 3, Batch 380/1563, loss: 4.124, reward: 32.113, critic_reward: 33.025, revenue_rate: 0.7846, distance: 9.4695, memory: -0.0336, power: 0.2882, lr: 0.000100, took: 78.744s
[COOPERATIVE] Epoch 3, Batch 390/1563, loss: 4.393, reward: 31.430, critic_reward: 30.650, revenue_rate: 0.7694, distance: 9.2624, memory: -0.0460, power: 0.2804, lr: 0.000100, took: 74.759s
[COOPERATIVE] Epoch 3, Batch 400/1563, loss: 4.415, reward: 31.680, critic_reward: 32.770, revenue_rate: 0.7736, distance: 9.2804, memory: -0.0387, power: 0.2829, lr: 0.000100, took: 76.792s
[COOPERATIVE] Epoch 3, Batch 410/1563, loss: 2.665, reward: 31.470, critic_reward: 31.184, revenue_rate: 0.7711, distance: 9.3129, memory: -0.0442, power: 0.2819, lr: 0.000100, took: 76.532s
[COOPERATIVE] Epoch 3, Batch 420/1563, loss: 3.203, reward: 32.577, critic_reward: 32.404, revenue_rate: 0.7995, distance: 9.6748, memory: -0.0294, power: 0.2923, lr: 0.000100, took: 81.817s
[COOPERATIVE] Epoch 3, Batch 430/1563, loss: 2.518, reward: 33.199, critic_reward: 32.908, revenue_rate: 0.8139, distance: 9.9635, memory: -0.0270, power: 0.3000, lr: 0.000100, took: 82.443s
[COOPERATIVE] Epoch 3, Batch 440/1563, loss: 2.695, reward: 34.839, critic_reward: 34.670, revenue_rate: 0.8570, distance: 10.7552, memory: -0.0010, power: 0.3231, lr: 0.000100, took: 90.172s
[COOPERATIVE] Epoch 3, Batch 450/1563, loss: 2.417, reward: 34.874, critic_reward: 34.780, revenue_rate: 0.8636, distance: 10.9876, memory: 0.0078, power: 0.3319, lr: 0.000100, took: 92.604s
[COOPERATIVE] Epoch 3, Batch 460/1563, loss: 1.958, reward: 34.813, critic_reward: 34.941, revenue_rate: 0.8614, distance: 10.8565, memory: 0.0105, power: 0.3335, lr: 0.000100, took: 88.907s
[COOPERATIVE] Epoch 3, Batch 470/1563, loss: 3.705, reward: 34.314, critic_reward: 34.570, revenue_rate: 0.8438, distance: 10.5917, memory: -0.0082, power: 0.3204, lr: 0.000100, took: 88.834s
[COOPERATIVE] Epoch 3, Batch 480/1563, loss: 3.050, reward: 34.309, critic_reward: 33.813, revenue_rate: 0.8451, distance: 10.5007, memory: -0.0080, power: 0.3195, lr: 0.000100, took: 89.954s
[COOPERATIVE] Epoch 3, Batch 490/1563, loss: 3.719, reward: 33.689, critic_reward: 34.003, revenue_rate: 0.8250, distance: 10.0859, memory: -0.0176, power: 0.3077, lr: 0.000100, took: 84.875s
[COOPERATIVE] Epoch 3, Batch 500/1563, loss: 3.511, reward: 31.198, critic_reward: 31.904, revenue_rate: 0.7628, distance: 8.9888, memory: -0.0512, power: 0.2752, lr: 0.000100, took: 76.348s
[COOPERATIVE] Epoch 3, Batch 510/1563, loss: 2.889, reward: 31.799, critic_reward: 31.658, revenue_rate: 0.7775, distance: 9.3491, memory: -0.0417, power: 0.2840, lr: 0.000100, took: 77.394s
[COOPERATIVE] Epoch 3, Batch 520/1563, loss: 3.170, reward: 32.069, critic_reward: 31.618, revenue_rate: 0.7879, distance: 9.5629, memory: -0.0226, power: 0.2890, lr: 0.000100, took: 75.502s
[COOPERATIVE] Epoch 3, Batch 530/1563, loss: 3.097, reward: 31.969, critic_reward: 32.320, revenue_rate: 0.7826, distance: 9.4061, memory: -0.0368, power: 0.2862, lr: 0.000100, took: 80.064s
[COOPERATIVE] Epoch 3, Batch 540/1563, loss: 4.187, reward: 33.099, critic_reward: 32.775, revenue_rate: 0.8154, distance: 10.0005, memory: -0.0201, power: 0.3024, lr: 0.000100, took: 83.295s
[COOPERATIVE] Epoch 3, Batch 550/1563, loss: 4.494, reward: 33.794, critic_reward: 33.751, revenue_rate: 0.8300, distance: 10.2911, memory: -0.0164, power: 0.3108, lr: 0.000100, took: 85.778s
[COOPERATIVE] Epoch 3, Batch 560/1563, loss: 5.161, reward: 33.087, critic_reward: 34.095, revenue_rate: 0.8116, distance: 9.8725, memory: -0.0266, power: 0.2993, lr: 0.000100, took: 82.316s
[COOPERATIVE] Epoch 3, Batch 570/1563, loss: 7.393, reward: 31.157, critic_reward: 30.412, revenue_rate: 0.7622, distance: 9.0015, memory: -0.0531, power: 0.2765, lr: 0.000100, took: 72.897s
[COOPERATIVE] Epoch 3, Batch 580/1563, loss: 3.790, reward: 31.277, critic_reward: 30.650, revenue_rate: 0.7639, distance: 9.0262, memory: -0.0515, power: 0.2768, lr: 0.000100, took: 72.157s
[COOPERATIVE] Epoch 3, Batch 590/1563, loss: 3.645, reward: 32.093, critic_reward: 31.991, revenue_rate: 0.7855, distance: 9.5677, memory: -0.0356, power: 0.2869, lr: 0.000100, took: 79.847s
[COOPERATIVE] Epoch 3, Batch 600/1563, loss: 2.499, reward: 33.197, critic_reward: 33.236, revenue_rate: 0.8179, distance: 9.9829, memory: -0.0226, power: 0.3038, lr: 0.000100, took: 83.404s
[COOPERATIVE] Epoch 3, Batch 610/1563, loss: 4.282, reward: 32.694, critic_reward: 32.893, revenue_rate: 0.8009, distance: 9.7423, memory: -0.0401, power: 0.2948, lr: 0.000100, took: 78.624s
[COOPERATIVE] Epoch 3, Batch 620/1563, loss: 3.093, reward: 29.646, critic_reward: 29.614, revenue_rate: 0.7217, distance: 8.5366, memory: -0.0718, power: 0.2574, lr: 0.000100, took: 69.998s
[COOPERATIVE] Epoch 3, Batch 630/1563, loss: 4.661, reward: 30.139, critic_reward: 29.643, revenue_rate: 0.7342, distance: 8.6824, memory: -0.0659, power: 0.2634, lr: 0.000100, took: 68.834s
[COOPERATIVE] Epoch 3, Batch 640/1563, loss: 3.112, reward: 29.580, critic_reward: 29.688, revenue_rate: 0.7232, distance: 8.5208, memory: -0.0698, power: 0.2577, lr: 0.000100, took: 71.539s
[COOPERATIVE] Epoch 3, Batch 650/1563, loss: 2.883, reward: 30.121, critic_reward: 29.925, revenue_rate: 0.7370, distance: 8.8418, memory: -0.0616, power: 0.2656, lr: 0.000100, took: 71.771s
[COOPERATIVE] Epoch 3, Batch 660/1563, loss: 3.450, reward: 30.452, critic_reward: 29.754, revenue_rate: 0.7448, distance: 8.9540, memory: -0.0535, power: 0.2706, lr: 0.000100, took: 73.221s
[COOPERATIVE] Epoch 3, Batch 670/1563, loss: 2.836, reward: 31.384, critic_reward: 31.695, revenue_rate: 0.7692, distance: 9.2287, memory: -0.0441, power: 0.2781, lr: 0.000100, took: 72.701s
[COOPERATIVE] Epoch 3, Batch 680/1563, loss: 2.282, reward: 32.228, critic_reward: 31.882, revenue_rate: 0.7873, distance: 9.5381, memory: -0.0451, power: 0.2872, lr: 0.000100, took: 78.668s
[COOPERATIVE] Epoch 3, Batch 690/1563, loss: 2.788, reward: 30.924, critic_reward: 31.342, revenue_rate: 0.7544, distance: 8.9631, memory: -0.0539, power: 0.2730, lr: 0.000100, took: 73.946s
[COOPERATIVE] Epoch 3, Batch 700/1563, loss: 4.558, reward: 30.477, critic_reward: 29.708, revenue_rate: 0.7433, distance: 8.8444, memory: -0.0494, power: 0.2699, lr: 0.000100, took: 70.030s
[COOPERATIVE] Epoch 3, Batch 710/1563, loss: 4.267, reward: 31.584, critic_reward: 31.683, revenue_rate: 0.7727, distance: 9.3277, memory: -0.0422, power: 0.2834, lr: 0.000100, took: 78.981s
[COOPERATIVE] Epoch 3, Batch 720/1563, loss: 3.517, reward: 33.123, critic_reward: 33.531, revenue_rate: 0.8140, distance: 10.0681, memory: -0.0222, power: 0.3029, lr: 0.000100, took: 83.618s
[COOPERATIVE] Epoch 3, Batch 730/1563, loss: 2.422, reward: 33.887, critic_reward: 33.498, revenue_rate: 0.8335, distance: 10.2192, memory: -0.0184, power: 0.3119, lr: 0.000100, took: 86.418s
[COOPERATIVE] Epoch 3, Batch 740/1563, loss: 2.713, reward: 34.558, critic_reward: 35.081, revenue_rate: 0.8516, distance: 10.6246, memory: -0.0029, power: 0.3199, lr: 0.000100, took: 89.374s
[COOPERATIVE] Epoch 3, Batch 750/1563, loss: 2.712, reward: 33.929, critic_reward: 34.143, revenue_rate: 0.8345, distance: 10.3354, memory: -0.0182, power: 0.3113, lr: 0.000100, took: 84.255s
[COOPERATIVE] Epoch 3, Batch 760/1563, loss: 2.164, reward: 32.373, critic_reward: 32.667, revenue_rate: 0.7914, distance: 9.5868, memory: -0.0326, power: 0.2907, lr: 0.000100, took: 81.757s
[COOPERATIVE] Epoch 3, Batch 770/1563, loss: 2.938, reward: 31.355, critic_reward: 31.323, revenue_rate: 0.7697, distance: 9.2037, memory: -0.0483, power: 0.2810, lr: 0.000100, took: 73.822s
[COOPERATIVE] Epoch 3, Batch 780/1563, loss: 8.667, reward: 32.521, critic_reward: 32.770, revenue_rate: 0.7936, distance: 9.5565, memory: -0.0324, power: 0.2925, lr: 0.000100, took: 79.576s
[COOPERATIVE] Epoch 3, Batch 790/1563, loss: 11.353, reward: 33.773, critic_reward: 32.395, revenue_rate: 0.8267, distance: 10.2584, memory: -0.0095, power: 0.3102, lr: 0.000100, took: 85.423s
[COOPERATIVE] Epoch 3, Batch 800/1563, loss: 17.477, reward: 33.059, critic_reward: 35.928, revenue_rate: 0.8128, distance: 10.0304, memory: -0.0131, power: 0.3027, lr: 0.000100, took: 83.982s
[COOPERATIVE] Epoch 3, Batch 810/1563, loss: 11.340, reward: 34.333, critic_reward: 31.450, revenue_rate: 0.8488, distance: 10.7259, memory: -0.0002, power: 0.3266, lr: 0.000100, took: 90.603s
[COOPERATIVE] Epoch 3, Batch 820/1563, loss: 4.127, reward: 35.208, critic_reward: 36.134, revenue_rate: 0.8739, distance: 11.2809, memory: 0.0205, power: 0.3427, lr: 0.000100, took: 97.181s
[COOPERATIVE] Epoch 3, Batch 830/1563, loss: 3.138, reward: 34.840, critic_reward: 34.795, revenue_rate: 0.8634, distance: 11.0665, memory: 0.0237, power: 0.3387, lr: 0.000100, took: 89.527s
[COOPERATIVE] Epoch 3, Batch 840/1563, loss: 2.577, reward: 33.794, critic_reward: 33.837, revenue_rate: 0.8343, distance: 10.4581, memory: -0.0077, power: 0.3181, lr: 0.000100, took: 90.387s
[COOPERATIVE] Epoch 3, Batch 850/1563, loss: 4.547, reward: 33.268, critic_reward: 33.776, revenue_rate: 0.8183, distance: 10.1463, memory: -0.0139, power: 0.3083, lr: 0.000100, took: 84.955s
[COOPERATIVE] Epoch 3, Batch 860/1563, loss: 2.287, reward: 32.503, critic_reward: 32.499, revenue_rate: 0.7971, distance: 9.6968, memory: -0.0264, power: 0.2950, lr: 0.000100, took: 82.486s
[COOPERATIVE] Epoch 3, Batch 870/1563, loss: 3.345, reward: 32.662, critic_reward: 31.988, revenue_rate: 0.8033, distance: 9.8208, memory: -0.0264, power: 0.2968, lr: 0.000100, took: 81.455s
[COOPERATIVE] Epoch 3, Batch 880/1563, loss: 3.508, reward: 33.244, critic_reward: 32.876, revenue_rate: 0.8123, distance: 9.9841, memory: -0.0210, power: 0.3029, lr: 0.000100, took: 83.524s
[COOPERATIVE] Epoch 3, Batch 890/1563, loss: 3.031, reward: 32.194, critic_reward: 31.818, revenue_rate: 0.7844, distance: 9.3980, memory: -0.0372, power: 0.2885, lr: 0.000100, took: 78.532s
[COOPERATIVE] Epoch 3, Batch 900/1563, loss: 2.355, reward: 32.385, critic_reward: 32.355, revenue_rate: 0.7944, distance: 9.6641, memory: -0.0286, power: 0.2922, lr: 0.000100, took: 79.209s
[COOPERATIVE] Epoch 3, Batch 910/1563, loss: 3.298, reward: 32.136, critic_reward: 32.658, revenue_rate: 0.7875, distance: 9.4883, memory: -0.0333, power: 0.2907, lr: 0.000100, took: 78.783s
[COOPERATIVE] Epoch 3, Batch 920/1563, loss: 4.094, reward: 32.191, critic_reward: 31.628, revenue_rate: 0.7866, distance: 9.5321, memory: -0.0441, power: 0.2891, lr: 0.000100, took: 78.182s
[COOPERATIVE] Epoch 3, Batch 930/1563, loss: 3.145, reward: 31.724, critic_reward: 32.295, revenue_rate: 0.7797, distance: 9.3829, memory: -0.0369, power: 0.2848, lr: 0.000100, took: 79.754s
[COOPERATIVE] Epoch 3, Batch 940/1563, loss: 3.367, reward: 32.464, critic_reward: 32.271, revenue_rate: 0.7944, distance: 9.6602, memory: -0.0377, power: 0.2913, lr: 0.000100, took: 79.989s
[COOPERATIVE] Epoch 3, Batch 950/1563, loss: 2.378, reward: 33.192, critic_reward: 32.976, revenue_rate: 0.8115, distance: 9.9511, memory: -0.0244, power: 0.3007, lr: 0.000100, took: 83.075s
[COOPERATIVE] Epoch 3, Batch 960/1563, loss: 6.422, reward: 33.575, critic_reward: 35.357, revenue_rate: 0.8261, distance: 10.2301, memory: -0.0130, power: 0.3089, lr: 0.000100, took: 85.138s
[COOPERATIVE] Epoch 3, Batch 970/1563, loss: 3.453, reward: 32.957, critic_reward: 32.250, revenue_rate: 0.8069, distance: 9.7890, memory: -0.0314, power: 0.2980, lr: 0.000100, took: 83.791s
[COOPERATIVE] Epoch 3, Batch 980/1563, loss: 4.419, reward: 32.430, critic_reward: 32.883, revenue_rate: 0.7953, distance: 9.6785, memory: -0.0316, power: 0.2932, lr: 0.000100, took: 79.452s
[COOPERATIVE] Epoch 3, Batch 990/1563, loss: 5.115, reward: 31.813, critic_reward: 30.605, revenue_rate: 0.7772, distance: 9.3207, memory: -0.0366, power: 0.2820, lr: 0.000100, took: 77.998s
[COOPERATIVE] Epoch 3, Batch 1000/1563, loss: 5.716, reward: 32.586, critic_reward: 33.513, revenue_rate: 0.8000, distance: 9.6504, memory: -0.0307, power: 0.2918, lr: 0.000100, took: 81.898s
[COOPERATIVE] Epoch 3, Batch 1010/1563, loss: 3.551, reward: 32.218, critic_reward: 31.837, revenue_rate: 0.7908, distance: 9.5608, memory: -0.0374, power: 0.2916, lr: 0.000100, took: 79.647s
[COOPERATIVE] Epoch 3, Batch 1020/1563, loss: 5.098, reward: 31.017, critic_reward: 32.379, revenue_rate: 0.7583, distance: 9.0789, memory: -0.0539, power: 0.2737, lr: 0.000100, took: 74.370s
[COOPERATIVE] Epoch 3, Batch 1030/1563, loss: 3.094, reward: 30.013, critic_reward: 29.406, revenue_rate: 0.7311, distance: 8.6737, memory: -0.0642, power: 0.2646, lr: 0.000100, took: 71.372s
[COOPERATIVE] Epoch 3, Batch 1040/1563, loss: 2.410, reward: 30.822, critic_reward: 31.103, revenue_rate: 0.7537, distance: 9.0176, memory: -0.0531, power: 0.2716, lr: 0.000100, took: 75.374s
[COOPERATIVE] Epoch 3, Batch 1050/1563, loss: 2.652, reward: 32.924, critic_reward: 32.966, revenue_rate: 0.8088, distance: 9.8231, memory: -0.0325, power: 0.2984, lr: 0.000100, took: 81.793s
[COOPERATIVE] Epoch 3, Batch 1060/1563, loss: 3.493, reward: 34.133, critic_reward: 33.495, revenue_rate: 0.8405, distance: 10.5076, memory: -0.0118, power: 0.3188, lr: 0.000100, took: 88.885s
[COOPERATIVE] Epoch 3, Batch 1070/1563, loss: 4.554, reward: 34.251, critic_reward: 35.452, revenue_rate: 0.8419, distance: 10.4381, memory: -0.0116, power: 0.3181, lr: 0.000100, took: 87.966s
[COOPERATIVE] Epoch 3, Batch 1080/1563, loss: 3.701, reward: 32.890, critic_reward: 32.009, revenue_rate: 0.8097, distance: 10.0323, memory: -0.0241, power: 0.3048, lr: 0.000100, took: 85.763s
[COOPERATIVE] Epoch 3, Batch 1090/1563, loss: 3.733, reward: 31.952, critic_reward: 32.972, revenue_rate: 0.7833, distance: 9.5538, memory: -0.0402, power: 0.2889, lr: 0.000100, took: 76.083s
[COOPERATIVE] Epoch 3, Batch 1100/1563, loss: 3.955, reward: 30.975, critic_reward: 30.081, revenue_rate: 0.7593, distance: 9.1358, memory: -0.0531, power: 0.2780, lr: 0.000100, took: 73.132s
[COOPERATIVE] Epoch 3, Batch 1110/1563, loss: 3.566, reward: 31.941, critic_reward: 32.552, revenue_rate: 0.7813, distance: 9.4380, memory: -0.0417, power: 0.2857, lr: 0.000100, took: 74.690s
[COOPERATIVE] Epoch 3, Batch 1120/1563, loss: 3.358, reward: 32.887, critic_reward: 31.949, revenue_rate: 0.8039, distance: 9.8150, memory: -0.0259, power: 0.2990, lr: 0.000100, took: 81.724s
[COOPERATIVE] Epoch 3, Batch 1130/1563, loss: 3.469, reward: 33.610, critic_reward: 34.647, revenue_rate: 0.8273, distance: 10.2042, memory: -0.0172, power: 0.3090, lr: 0.000100, took: 82.151s
[COOPERATIVE] Epoch 3, Batch 1140/1563, loss: 3.267, reward: 33.869, critic_reward: 33.498, revenue_rate: 0.8332, distance: 10.2060, memory: -0.0164, power: 0.3126, lr: 0.000100, took: 86.893s
[COOPERATIVE] Epoch 3, Batch 1150/1563, loss: 4.393, reward: 34.672, critic_reward: 34.417, revenue_rate: 0.8503, distance: 10.5571, memory: -0.0052, power: 0.3206, lr: 0.000100, took: 89.386s
[COOPERATIVE] Epoch 3, Batch 1160/1563, loss: 2.412, reward: 33.258, critic_reward: 33.714, revenue_rate: 0.8195, distance: 10.0136, memory: -0.0202, power: 0.3062, lr: 0.000100, took: 84.451s
[COOPERATIVE] Epoch 3, Batch 1170/1563, loss: 2.589, reward: 31.317, critic_reward: 31.413, revenue_rate: 0.7645, distance: 9.2038, memory: -0.0410, power: 0.2802, lr: 0.000100, took: 77.148s
[COOPERATIVE] Epoch 3, Batch 1180/1563, loss: 2.892, reward: 31.354, critic_reward: 31.222, revenue_rate: 0.7664, distance: 9.2229, memory: -0.0468, power: 0.2794, lr: 0.000100, took: 75.555s
[COOPERATIVE] Epoch 3, Batch 1190/1563, loss: 2.949, reward: 32.278, critic_reward: 31.939, revenue_rate: 0.7880, distance: 9.5423, memory: -0.0350, power: 0.2898, lr: 0.000100, took: 78.462s
[COOPERATIVE] Epoch 3, Batch 1200/1563, loss: 4.099, reward: 32.916, critic_reward: 32.406, revenue_rate: 0.8103, distance: 9.9320, memory: -0.0259, power: 0.2996, lr: 0.000100, took: 80.149s
[COOPERATIVE] Epoch 3, Batch 1210/1563, loss: 7.485, reward: 33.876, critic_reward: 34.744, revenue_rate: 0.8329, distance: 10.2420, memory: -0.0253, power: 0.3106, lr: 0.000100, took: 84.997s
[COOPERATIVE] Epoch 3, Batch 1220/1563, loss: 3.294, reward: 34.110, critic_reward: 33.541, revenue_rate: 0.8336, distance: 10.1960, memory: -0.0262, power: 0.3119, lr: 0.000100, took: 85.603s
[COOPERATIVE] Epoch 3, Batch 1230/1563, loss: 2.977, reward: 33.157, critic_reward: 33.129, revenue_rate: 0.8103, distance: 9.8087, memory: -0.0289, power: 0.2984, lr: 0.000100, took: 81.712s
[COOPERATIVE] Epoch 3, Batch 1240/1563, loss: 2.804, reward: 32.244, critic_reward: 32.278, revenue_rate: 0.7881, distance: 9.4714, memory: -0.0366, power: 0.2906, lr: 0.000100, took: 79.162s
[COOPERATIVE] Epoch 3, Batch 1250/1563, loss: 2.128, reward: 32.362, critic_reward: 32.257, revenue_rate: 0.7935, distance: 9.6208, memory: -0.0430, power: 0.2929, lr: 0.000100, took: 79.580s
[COOPERATIVE] Epoch 3, Batch 1260/1563, loss: 3.033, reward: 31.704, critic_reward: 30.962, revenue_rate: 0.7786, distance: 9.4210, memory: -0.0333, power: 0.2850, lr: 0.000100, took: 79.360s
[COOPERATIVE] Epoch 3, Batch 1270/1563, loss: 5.005, reward: 31.110, critic_reward: 31.331, revenue_rate: 0.7593, distance: 9.0893, memory: -0.0512, power: 0.2761, lr: 0.000100, took: 75.471s
[COOPERATIVE] Epoch 3, Batch 1280/1563, loss: 3.493, reward: 32.346, critic_reward: 31.622, revenue_rate: 0.7916, distance: 9.5038, memory: -0.0379, power: 0.2896, lr: 0.000100, took: 78.339s
[COOPERATIVE] Epoch 3, Batch 1290/1563, loss: 2.922, reward: 33.841, critic_reward: 33.844, revenue_rate: 0.8333, distance: 10.2946, memory: -0.0182, power: 0.3100, lr: 0.000100, took: 87.228s
[COOPERATIVE] Epoch 3, Batch 1300/1563, loss: 3.381, reward: 34.439, critic_reward: 34.920, revenue_rate: 0.8470, distance: 10.5827, memory: -0.0004, power: 0.3199, lr: 0.000100, took: 88.112s
[COOPERATIVE] Epoch 3, Batch 1310/1563, loss: 4.052, reward: 35.354, critic_reward: 35.249, revenue_rate: 0.8695, distance: 11.0111, memory: 0.0056, power: 0.3332, lr: 0.000100, took: 94.259s
[COOPERATIVE] Epoch 3, Batch 1320/1563, loss: 3.083, reward: 35.452, critic_reward: 34.849, revenue_rate: 0.8753, distance: 11.1070, memory: 0.0176, power: 0.3359, lr: 0.000100, took: 92.963s
[COOPERATIVE] Epoch 3, Batch 1330/1563, loss: 2.949, reward: 35.230, critic_reward: 35.524, revenue_rate: 0.8688, distance: 10.9059, memory: 0.0058, power: 0.3307, lr: 0.000100, took: 91.977s
[COOPERATIVE] Epoch 3, Batch 1340/1563, loss: 3.538, reward: 34.394, critic_reward: 35.247, revenue_rate: 0.8452, distance: 10.4935, memory: -0.0152, power: 0.3170, lr: 0.000100, took: 87.337s
[COOPERATIVE] Epoch 3, Batch 1350/1563, loss: 2.565, reward: 32.163, critic_reward: 32.315, revenue_rate: 0.7864, distance: 9.4714, memory: -0.0378, power: 0.2886, lr: 0.000100, took: 78.059s
[COOPERATIVE] Epoch 3, Batch 1360/1563, loss: 3.035, reward: 30.425, critic_reward: 30.039, revenue_rate: 0.7420, distance: 8.8532, memory: -0.0637, power: 0.2698, lr: 0.000100, took: 72.568s
[COOPERATIVE] Epoch 3, Batch 1370/1563, loss: 2.620, reward: 30.342, critic_reward: 29.759, revenue_rate: 0.7414, distance: 8.8440, memory: -0.0567, power: 0.2687, lr: 0.000100, took: 74.216s
[COOPERATIVE] Epoch 3, Batch 1380/1563, loss: 2.018, reward: 32.553, critic_reward: 32.369, revenue_rate: 0.7937, distance: 9.5590, memory: -0.0350, power: 0.2895, lr: 0.000100, took: 79.485s
[COOPERATIVE] Epoch 3, Batch 1390/1563, loss: 2.290, reward: 33.901, critic_reward: 34.087, revenue_rate: 0.8343, distance: 10.2670, memory: -0.0177, power: 0.3086, lr: 0.000100, took: 87.473s
[COOPERATIVE] Epoch 3, Batch 1400/1563, loss: 3.400, reward: 34.189, critic_reward: 33.991, revenue_rate: 0.8380, distance: 10.3247, memory: -0.0144, power: 0.3136, lr: 0.000100, took: 84.383s
[COOPERATIVE] Epoch 3, Batch 1410/1563, loss: 3.749, reward: 32.979, critic_reward: 32.482, revenue_rate: 0.8076, distance: 9.9223, memory: -0.0238, power: 0.3000, lr: 0.000100, took: 83.442s
[COOPERATIVE] Epoch 3, Batch 1420/1563, loss: 2.471, reward: 32.852, critic_reward: 32.968, revenue_rate: 0.8028, distance: 9.7402, memory: -0.0394, power: 0.2944, lr: 0.000100, took: 80.143s
[COOPERATIVE] Epoch 3, Batch 1430/1563, loss: 2.696, reward: 33.598, critic_reward: 33.124, revenue_rate: 0.8257, distance: 10.0307, memory: -0.0197, power: 0.3085, lr: 0.000100, took: 82.573s
[COOPERATIVE] Epoch 3, Batch 1440/1563, loss: 3.068, reward: 34.739, critic_reward: 34.328, revenue_rate: 0.8573, distance: 10.7618, memory: 0.0041, power: 0.3268, lr: 0.000100, took: 89.826s
[COOPERATIVE] Epoch 3, Batch 1450/1563, loss: 3.555, reward: 35.726, critic_reward: 36.552, revenue_rate: 0.8854, distance: 11.2085, memory: 0.0132, power: 0.3396, lr: 0.000100, took: 94.334s
[COOPERATIVE] Epoch 3, Batch 1460/1563, loss: 3.030, reward: 35.126, critic_reward: 34.772, revenue_rate: 0.8710, distance: 10.9916, memory: 0.0117, power: 0.3322, lr: 0.000100, took: 92.558s
[COOPERATIVE] Epoch 3, Batch 1470/1563, loss: 2.969, reward: 34.540, critic_reward: 34.063, revenue_rate: 0.8540, distance: 10.7266, memory: -0.0018, power: 0.3200, lr: 0.000100, took: 90.216s
[COOPERATIVE] Epoch 3, Batch 1480/1563, loss: 2.680, reward: 34.126, critic_reward: 33.845, revenue_rate: 0.8401, distance: 10.4439, memory: -0.0096, power: 0.3141, lr: 0.000100, took: 87.107s
[COOPERATIVE] Epoch 3, Batch 1490/1563, loss: 2.851, reward: 33.229, critic_reward: 33.098, revenue_rate: 0.8163, distance: 9.9107, memory: -0.0293, power: 0.3020, lr: 0.000100, took: 82.382s
[COOPERATIVE] Epoch 3, Batch 1500/1563, loss: 4.773, reward: 31.489, critic_reward: 32.700, revenue_rate: 0.7707, distance: 9.2681, memory: -0.0416, power: 0.2799, lr: 0.000100, took: 78.336s
[COOPERATIVE] Epoch 3, Batch 1510/1563, loss: 3.210, reward: 31.833, critic_reward: 31.185, revenue_rate: 0.7787, distance: 9.4758, memory: -0.0352, power: 0.2848, lr: 0.000100, took: 77.143s
[COOPERATIVE] Epoch 3, Batch 1520/1563, loss: 4.209, reward: 32.557, critic_reward: 33.178, revenue_rate: 0.8001, distance: 9.7237, memory: -0.0316, power: 0.2936, lr: 0.000100, took: 82.042s
[COOPERATIVE] Epoch 3, Batch 1530/1563, loss: 3.020, reward: 34.607, critic_reward: 34.191, revenue_rate: 0.8547, distance: 10.6908, memory: -0.0035, power: 0.3209, lr: 0.000100, took: 88.479s
[COOPERATIVE] Epoch 3, Batch 1540/1563, loss: 4.388, reward: 35.076, critic_reward: 35.737, revenue_rate: 0.8661, distance: 10.8389, memory: 0.0016, power: 0.3284, lr: 0.000100, took: 90.966s
[COOPERATIVE] Epoch 3, Batch 1550/1563, loss: 2.942, reward: 35.477, critic_reward: 35.184, revenue_rate: 0.8718, distance: 11.0260, memory: 0.0061, power: 0.3337, lr: 0.000100, took: 91.950s
[COOPERATIVE] Epoch 3, Batch 1560/1563, loss: 3.424, reward: 34.618, critic_reward: 34.381, revenue_rate: 0.8520, distance: 10.5756, memory: -0.0097, power: 0.3235, lr: 0.000100, took: 88.966s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 33.463, revenue_rate: 0.8216, distance: 10.0278, memory: -0.0228, power: 0.3038
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48 (验证集奖励: 33.4630)
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-14 02:51:08
训练总耗时: 11:39:48.421002
训练过程统计:
  最终训练奖励: 32.0679
  最佳验证奖励: 33.4630
  训练轮数完成: 4689
  奖励提升: 22.5976
  平均每轮提升: 0.0048
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 10000
  测试批次数: 157
  可视化样本数: 5
测试开始时间: 2025-08-14 02:51:48
测试结束时间: 2025-08-14 03:14:39
测试耗时: 0:22:50.970126

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 33.4630
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48
测试结果:
  平均收益率: 0.8238
  平均距离: 10.0802
  平均内存使用: -0.0234
  平均功耗: 0.3052
模型信息:
  Actor参数: 3,730,953
  Critic参数: 494,285
  总参数: 4,225,238
综合性能评分: 3.0336
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-14 03:21:08
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/1563, loss: 865.264, reward: 11.682, critic_reward: 0.831, revenue_rate: 0.2956, distance: 4.4160, memory: -0.0605, power: 0.1325, lr: 0.000100, took: 40.274s
[COMPETITIVE] Epoch 1, Batch 20/1563, loss: 13.616, reward: 15.405, critic_reward: 14.655, revenue_rate: 0.3821, distance: 5.1896, memory: -0.1688, power: 0.1573, lr: 0.000100, took: 41.569s
[COMPETITIVE] Epoch 1, Batch 30/1563, loss: 11.358, reward: 19.321, critic_reward: 20.479, revenue_rate: 0.4791, distance: 6.4584, memory: -0.1370, power: 0.1932, lr: 0.000100, took: 51.808s
[COMPETITIVE] Epoch 1, Batch 40/1563, loss: 22.650, reward: 24.391, critic_reward: 24.735, revenue_rate: 0.6074, distance: 8.0210, memory: -0.0996, power: 0.2403, lr: 0.000100, took: 64.646s
[COMPETITIVE] Epoch 1, Batch 50/1563, loss: 24.160, reward: 25.863, critic_reward: 25.007, revenue_rate: 0.6408, distance: 8.2751, memory: -0.0808, power: 0.2498, lr: 0.000100, took: 67.133s
[COMPETITIVE] Epoch 1, Batch 60/1563, loss: 10.800, reward: 24.594, critic_reward: 23.681, revenue_rate: 0.6057, distance: 7.6638, memory: -0.0943, power: 0.2321, lr: 0.000100, took: 62.339s
[COMPETITIVE] Epoch 1, Batch 70/1563, loss: 6.781, reward: 26.779, critic_reward: 27.051, revenue_rate: 0.6608, distance: 8.4577, memory: -0.0847, power: 0.2544, lr: 0.000100, took: 68.696s
[COMPETITIVE] Epoch 1, Batch 80/1563, loss: 23.860, reward: 26.653, critic_reward: 24.245, revenue_rate: 0.6564, distance: 8.3958, memory: -0.0797, power: 0.2519, lr: 0.000100, took: 68.112s
[COMPETITIVE] Epoch 1, Batch 90/1563, loss: 15.523, reward: 26.370, critic_reward: 27.284, revenue_rate: 0.6480, distance: 8.0565, memory: -0.0919, power: 0.2434, lr: 0.000100, took: 67.565s
[COMPETITIVE] Epoch 1, Batch 100/1563, loss: 8.276, reward: 25.812, critic_reward: 25.370, revenue_rate: 0.6334, distance: 7.7687, memory: -0.0966, power: 0.2330, lr: 0.000100, took: 62.178s
[COMPETITIVE] Epoch 1, Batch 110/1563, loss: 8.602, reward: 26.969, critic_reward: 26.320, revenue_rate: 0.6620, distance: 8.1381, memory: -0.0852, power: 0.2466, lr: 0.000100, took: 66.419s
[COMPETITIVE] Epoch 1, Batch 120/1563, loss: 15.121, reward: 28.538, critic_reward: 28.871, revenue_rate: 0.7014, distance: 8.7528, memory: -0.0624, power: 0.2646, lr: 0.000100, took: 73.244s
[COMPETITIVE] Epoch 1, Batch 130/1563, loss: 4.765, reward: 29.315, critic_reward: 29.352, revenue_rate: 0.7230, distance: 9.0949, memory: -0.0551, power: 0.2753, lr: 0.000100, took: 74.135s
[COMPETITIVE] Epoch 1, Batch 140/1563, loss: 6.393, reward: 28.530, critic_reward: 29.246, revenue_rate: 0.7051, distance: 9.0126, memory: -0.0565, power: 0.2714, lr: 0.000100, took: 73.551s
[COMPETITIVE] Epoch 1, Batch 150/1563, loss: 12.624, reward: 28.290, critic_reward: 27.498, revenue_rate: 0.6996, distance: 8.7082, memory: -0.0736, power: 0.2641, lr: 0.000100, took: 72.444s
[COMPETITIVE] Epoch 1, Batch 160/1563, loss: 9.691, reward: 26.772, critic_reward: 26.663, revenue_rate: 0.6581, distance: 8.2132, memory: -0.0818, power: 0.2489, lr: 0.000100, took: 66.956s
[COMPETITIVE] Epoch 1, Batch 170/1563, loss: 15.626, reward: 27.541, critic_reward: 28.451, revenue_rate: 0.6766, distance: 8.3080, memory: -0.0844, power: 0.2501, lr: 0.000100, took: 67.097s
[COMPETITIVE] Epoch 1, Batch 180/1563, loss: 25.301, reward: 28.358, critic_reward: 27.486, revenue_rate: 0.6976, distance: 8.6244, memory: -0.0728, power: 0.2598, lr: 0.000100, took: 70.232s
[COMPETITIVE] Epoch 1, Batch 190/1563, loss: 9.727, reward: 27.916, critic_reward: 28.496, revenue_rate: 0.6839, distance: 8.4447, memory: -0.0705, power: 0.2551, lr: 0.000100, took: 68.622s
[COMPETITIVE] Epoch 1, Batch 200/1563, loss: 13.873, reward: 27.926, critic_reward: 26.382, revenue_rate: 0.6858, distance: 8.4614, memory: -0.0777, power: 0.2557, lr: 0.000100, took: 68.788s
[COMPETITIVE] Epoch 1, Batch 210/1563, loss: 7.183, reward: 30.019, critic_reward: 30.743, revenue_rate: 0.7424, distance: 9.5484, memory: -0.0356, power: 0.2891, lr: 0.000100, took: 78.745s
[COMPETITIVE] Epoch 1, Batch 220/1563, loss: 4.197, reward: 29.808, critic_reward: 29.937, revenue_rate: 0.7364, distance: 9.4280, memory: -0.0344, power: 0.2848, lr: 0.000100, took: 79.357s
[COMPETITIVE] Epoch 1, Batch 230/1563, loss: 5.194, reward: 30.093, critic_reward: 29.859, revenue_rate: 0.7437, distance: 9.3225, memory: -0.0438, power: 0.2826, lr: 0.000100, took: 76.368s
[COMPETITIVE] Epoch 1, Batch 240/1563, loss: 4.271, reward: 29.797, critic_reward: 29.960, revenue_rate: 0.7319, distance: 9.0785, memory: -0.0530, power: 0.2731, lr: 0.000100, took: 76.434s
[COMPETITIVE] Epoch 1, Batch 250/1563, loss: 5.480, reward: 29.190, critic_reward: 28.816, revenue_rate: 0.7174, distance: 8.8324, memory: -0.0499, power: 0.2668, lr: 0.000100, took: 72.349s
[COMPETITIVE] Epoch 1, Batch 260/1563, loss: 7.057, reward: 30.472, critic_reward: 30.621, revenue_rate: 0.7488, distance: 9.3291, memory: -0.0352, power: 0.2824, lr: 0.000100, took: 76.602s
[COMPETITIVE] Epoch 1, Batch 270/1563, loss: 10.197, reward: 28.888, critic_reward: 29.719, revenue_rate: 0.7094, distance: 8.8872, memory: -0.0639, power: 0.2680, lr: 0.000100, took: 74.519s
[COMPETITIVE] Epoch 1, Batch 280/1563, loss: 5.680, reward: 27.361, critic_reward: 27.400, revenue_rate: 0.6680, distance: 8.0972, memory: -0.0804, power: 0.2471, lr: 0.000100, took: 66.358s
[COMPETITIVE] Epoch 1, Batch 290/1563, loss: 3.234, reward: 27.017, critic_reward: 26.721, revenue_rate: 0.6608, distance: 7.9397, memory: -0.0854, power: 0.2417, lr: 0.000100, took: 64.167s
[COMPETITIVE] Epoch 1, Batch 300/1563, loss: 12.589, reward: 27.459, critic_reward: 27.117, revenue_rate: 0.6699, distance: 8.2034, memory: -0.0753, power: 0.2480, lr: 0.000100, took: 66.423s
[COMPETITIVE] Epoch 1, Batch 310/1563, loss: 4.942, reward: 30.230, critic_reward: 29.792, revenue_rate: 0.7423, distance: 9.1881, memory: -0.0470, power: 0.2782, lr: 0.000100, took: 75.574s
[COMPETITIVE] Epoch 1, Batch 320/1563, loss: 5.611, reward: 28.993, critic_reward: 28.653, revenue_rate: 0.7107, distance: 8.6905, memory: -0.0688, power: 0.2624, lr: 0.000100, took: 71.181s
[COMPETITIVE] Epoch 1, Batch 330/1563, loss: 4.880, reward: 28.630, critic_reward: 28.555, revenue_rate: 0.7048, distance: 8.5407, memory: -0.0664, power: 0.2590, lr: 0.000100, took: 69.385s
[COMPETITIVE] Epoch 1, Batch 340/1563, loss: 11.611, reward: 29.614, critic_reward: 29.884, revenue_rate: 0.7285, distance: 8.9730, memory: -0.0611, power: 0.2707, lr: 0.000100, took: 74.821s
[COMPETITIVE] Epoch 1, Batch 350/1563, loss: 3.767, reward: 28.278, critic_reward: 28.733, revenue_rate: 0.6936, distance: 8.5332, memory: -0.0634, power: 0.2576, lr: 0.000100, took: 69.508s
[COMPETITIVE] Epoch 1, Batch 360/1563, loss: 5.431, reward: 30.771, critic_reward: 31.088, revenue_rate: 0.7575, distance: 9.4733, memory: -0.0343, power: 0.2871, lr: 0.000100, took: 77.667s
[COMPETITIVE] Epoch 1, Batch 370/1563, loss: 4.307, reward: 30.117, critic_reward: 30.024, revenue_rate: 0.7426, distance: 9.2356, memory: -0.0436, power: 0.2807, lr: 0.000100, took: 78.116s
[COMPETITIVE] Epoch 1, Batch 380/1563, loss: 4.171, reward: 28.964, critic_reward: 28.939, revenue_rate: 0.7080, distance: 8.5372, memory: -0.0650, power: 0.2588, lr: 0.000100, took: 69.953s
[COMPETITIVE] Epoch 1, Batch 390/1563, loss: 13.540, reward: 31.360, critic_reward: 31.067, revenue_rate: 0.7711, distance: 9.5487, memory: -0.0328, power: 0.2901, lr: 0.000100, took: 81.289s
[COMPETITIVE] Epoch 1, Batch 400/1563, loss: 10.976, reward: 31.382, critic_reward: 31.185, revenue_rate: 0.7725, distance: 9.7322, memory: -0.0315, power: 0.2943, lr: 0.000100, took: 80.076s
[COMPETITIVE] Epoch 1, Batch 410/1563, loss: 4.400, reward: 29.432, critic_reward: 29.336, revenue_rate: 0.7212, distance: 8.9367, memory: -0.0560, power: 0.2706, lr: 0.000100, took: 73.793s
[COMPETITIVE] Epoch 1, Batch 420/1563, loss: 4.314, reward: 29.529, critic_reward: 29.937, revenue_rate: 0.7245, distance: 8.9524, memory: -0.0519, power: 0.2706, lr: 0.000100, took: 73.364s
[COMPETITIVE] Epoch 1, Batch 430/1563, loss: 7.750, reward: 30.549, critic_reward: 29.599, revenue_rate: 0.7478, distance: 9.2378, memory: -0.0461, power: 0.2797, lr: 0.000100, took: 75.821s
[COMPETITIVE] Epoch 1, Batch 440/1563, loss: 5.936, reward: 31.722, critic_reward: 32.026, revenue_rate: 0.7815, distance: 9.7010, memory: -0.0398, power: 0.2934, lr: 0.000100, took: 80.369s
[COMPETITIVE] Epoch 1, Batch 450/1563, loss: 3.688, reward: 30.774, critic_reward: 30.912, revenue_rate: 0.7564, distance: 9.2388, memory: -0.0514, power: 0.2805, lr: 0.000100, took: 75.967s
[COMPETITIVE] Epoch 1, Batch 460/1563, loss: 4.245, reward: 32.131, critic_reward: 32.539, revenue_rate: 0.7899, distance: 9.8397, memory: -0.0281, power: 0.2984, lr: 0.000100, took: 83.458s
[COMPETITIVE] Epoch 1, Batch 470/1563, loss: 4.999, reward: 30.815, critic_reward: 30.551, revenue_rate: 0.7542, distance: 9.2134, memory: -0.0485, power: 0.2797, lr: 0.000100, took: 75.489s
[COMPETITIVE] Epoch 1, Batch 480/1563, loss: 4.181, reward: 29.913, critic_reward: 29.840, revenue_rate: 0.7335, distance: 8.9635, memory: -0.0584, power: 0.2695, lr: 0.000100, took: 73.141s
[COMPETITIVE] Epoch 1, Batch 490/1563, loss: 6.341, reward: 30.068, critic_reward: 30.160, revenue_rate: 0.7377, distance: 9.0741, memory: -0.0497, power: 0.2742, lr: 0.000100, took: 75.803s
[COMPETITIVE] Epoch 1, Batch 500/1563, loss: 6.627, reward: 30.452, critic_reward: 29.985, revenue_rate: 0.7479, distance: 9.2336, memory: -0.0458, power: 0.2786, lr: 0.000100, took: 75.781s
[COMPETITIVE] Epoch 1, Batch 510/1563, loss: 9.823, reward: 30.946, critic_reward: 29.955, revenue_rate: 0.7620, distance: 9.4731, memory: -0.0459, power: 0.2843, lr: 0.000100, took: 79.991s
[COMPETITIVE] Epoch 1, Batch 520/1563, loss: 8.193, reward: 32.074, critic_reward: 32.949, revenue_rate: 0.7919, distance: 10.0942, memory: -0.0167, power: 0.3064, lr: 0.000100, took: 83.582s
[COMPETITIVE] Epoch 1, Batch 530/1563, loss: 12.475, reward: 30.852, critic_reward: 31.683, revenue_rate: 0.7574, distance: 9.3595, memory: -0.0393, power: 0.2855, lr: 0.000100, took: 77.316s
[COMPETITIVE] Epoch 1, Batch 540/1563, loss: 4.366, reward: 30.781, critic_reward: 30.492, revenue_rate: 0.7558, distance: 9.3322, memory: -0.0436, power: 0.2817, lr: 0.000100, took: 76.252s
[COMPETITIVE] Epoch 1, Batch 550/1563, loss: 19.672, reward: 31.670, critic_reward: 32.716, revenue_rate: 0.7806, distance: 9.9144, memory: -0.0258, power: 0.2997, lr: 0.000100, took: 82.196s
[COMPETITIVE] Epoch 1, Batch 560/1563, loss: 13.579, reward: 32.412, critic_reward: 34.424, revenue_rate: 0.8019, distance: 10.2458, memory: -0.0094, power: 0.3127, lr: 0.000100, took: 85.572s
[COMPETITIVE] Epoch 1, Batch 570/1563, loss: 4.087, reward: 31.188, critic_reward: 30.812, revenue_rate: 0.7643, distance: 9.3809, memory: -0.0338, power: 0.2862, lr: 0.000100, took: 78.960s
[COMPETITIVE] Epoch 1, Batch 580/1563, loss: 7.405, reward: 31.325, critic_reward: 32.154, revenue_rate: 0.7700, distance: 9.5563, memory: -0.0382, power: 0.2888, lr: 0.000100, took: 80.746s
[COMPETITIVE] Epoch 1, Batch 590/1563, loss: 4.690, reward: 30.537, critic_reward: 30.192, revenue_rate: 0.7492, distance: 9.1374, memory: -0.0531, power: 0.2782, lr: 0.000100, took: 76.539s
[COMPETITIVE] Epoch 1, Batch 600/1563, loss: 5.479, reward: 31.539, critic_reward: 32.505, revenue_rate: 0.7714, distance: 9.5480, memory: -0.0383, power: 0.2883, lr: 0.000100, took: 80.163s
[COMPETITIVE] Epoch 1, Batch 610/1563, loss: 21.193, reward: 30.568, critic_reward: 31.119, revenue_rate: 0.7481, distance: 9.0772, memory: -0.0504, power: 0.2772, lr: 0.000100, took: 74.402s
[COMPETITIVE] Epoch 1, Batch 620/1563, loss: 11.108, reward: 28.988, critic_reward: 28.512, revenue_rate: 0.7098, distance: 8.5956, memory: -0.0665, power: 0.2604, lr: 0.000100, took: 69.750s
[COMPETITIVE] Epoch 1, Batch 630/1563, loss: 5.152, reward: 28.016, critic_reward: 28.676, revenue_rate: 0.6842, distance: 8.2023, memory: -0.0752, power: 0.2478, lr: 0.000100, took: 68.184s
[COMPETITIVE] Epoch 1, Batch 640/1563, loss: 5.434, reward: 29.161, critic_reward: 29.062, revenue_rate: 0.7137, distance: 8.5956, memory: -0.0664, power: 0.2609, lr: 0.000100, took: 70.020s
[COMPETITIVE] Epoch 1, Batch 650/1563, loss: 5.445, reward: 30.542, critic_reward: 29.998, revenue_rate: 0.7528, distance: 9.2466, memory: -0.0496, power: 0.2788, lr: 0.000100, took: 75.541s
[COMPETITIVE] Epoch 1, Batch 660/1563, loss: 3.752, reward: 28.801, critic_reward: 29.259, revenue_rate: 0.7050, distance: 8.6145, memory: -0.0646, power: 0.2612, lr: 0.000100, took: 70.139s
[COMPETITIVE] Epoch 1, Batch 670/1563, loss: 8.322, reward: 30.159, critic_reward: 30.599, revenue_rate: 0.7407, distance: 9.1476, memory: -0.0482, power: 0.2786, lr: 0.000100, took: 74.871s
[COMPETITIVE] Epoch 1, Batch 680/1563, loss: 9.748, reward: 32.732, critic_reward: 33.765, revenue_rate: 0.8088, distance: 10.2007, memory: -0.0206, power: 0.3073, lr: 0.000100, took: 84.780s
[COMPETITIVE] Epoch 1, Batch 690/1563, loss: 5.310, reward: 32.225, critic_reward: 32.409, revenue_rate: 0.7955, distance: 10.0302, memory: -0.0282, power: 0.3045, lr: 0.000100, took: 85.149s
[COMPETITIVE] Epoch 1, Batch 700/1563, loss: 5.493, reward: 29.584, critic_reward: 29.667, revenue_rate: 0.7253, distance: 8.8567, memory: -0.0553, power: 0.2689, lr: 0.000100, took: 72.353s
[COMPETITIVE] Epoch 1, Batch 710/1563, loss: 10.398, reward: 29.507, critic_reward: 28.475, revenue_rate: 0.7261, distance: 8.9773, memory: -0.0479, power: 0.2709, lr: 0.000100, took: 73.576s
[COMPETITIVE] Epoch 1, Batch 720/1563, loss: 18.221, reward: 29.928, critic_reward: 29.166, revenue_rate: 0.7366, distance: 9.0385, memory: -0.0349, power: 0.2746, lr: 0.000100, took: 75.990s
[COMPETITIVE] Epoch 1, Batch 730/1563, loss: 7.261, reward: 31.381, critic_reward: 30.869, revenue_rate: 0.7680, distance: 9.3958, memory: -0.0488, power: 0.2860, lr: 0.000100, took: 77.384s
[COMPETITIVE] Epoch 1, Batch 740/1563, loss: 3.544, reward: 30.909, critic_reward: 31.019, revenue_rate: 0.7584, distance: 9.3017, memory: -0.0461, power: 0.2776, lr: 0.000100, took: 78.015s
[COMPETITIVE] Epoch 1, Batch 750/1563, loss: 3.727, reward: 31.068, critic_reward: 30.546, revenue_rate: 0.7601, distance: 9.2801, memory: -0.0528, power: 0.2838, lr: 0.000100, took: 83.804s
[COMPETITIVE] Epoch 1, Batch 760/1563, loss: 4.247, reward: 32.037, critic_reward: 31.705, revenue_rate: 0.7878, distance: 9.7056, memory: -0.0265, power: 0.2937, lr: 0.000100, took: 80.544s
[COMPETITIVE] Epoch 1, Batch 770/1563, loss: 3.261, reward: 31.133, critic_reward: 31.276, revenue_rate: 0.7648, distance: 9.3711, memory: -0.0307, power: 0.2863, lr: 0.000100, took: 77.759s
[COMPETITIVE] Epoch 1, Batch 780/1563, loss: 3.278, reward: 30.571, critic_reward: 30.401, revenue_rate: 0.7486, distance: 9.1754, memory: -0.0465, power: 0.2794, lr: 0.000100, took: 75.606s
[COMPETITIVE] Epoch 1, Batch 790/1563, loss: 7.204, reward: 31.619, critic_reward: 30.747, revenue_rate: 0.7738, distance: 9.4908, memory: -0.0381, power: 0.2877, lr: 0.000100, took: 78.149s
[COMPETITIVE] Epoch 1, Batch 800/1563, loss: 6.179, reward: 32.035, critic_reward: 32.188, revenue_rate: 0.7885, distance: 9.8285, memory: -0.0224, power: 0.2957, lr: 0.000100, took: 81.363s
[COMPETITIVE] Epoch 1, Batch 810/1563, loss: 4.441, reward: 32.244, critic_reward: 32.403, revenue_rate: 0.7896, distance: 9.7560, memory: -0.0380, power: 0.2968, lr: 0.000100, took: 83.035s
[COMPETITIVE] Epoch 1, Batch 820/1563, loss: 3.551, reward: 31.719, critic_reward: 32.226, revenue_rate: 0.7759, distance: 9.4938, memory: -0.0379, power: 0.2883, lr: 0.000100, took: 78.888s
[COMPETITIVE] Epoch 1, Batch 830/1563, loss: 6.774, reward: 29.570, critic_reward: 29.984, revenue_rate: 0.7227, distance: 8.7070, memory: -0.0652, power: 0.2651, lr: 0.000100, took: 72.965s
[COMPETITIVE] Epoch 1, Batch 840/1563, loss: 6.888, reward: 27.291, critic_reward: 26.838, revenue_rate: 0.6685, distance: 8.0904, memory: -0.0839, power: 0.2460, lr: 0.000100, took: 65.687s
[COMPETITIVE] Epoch 1, Batch 850/1563, loss: 3.796, reward: 26.442, critic_reward: 25.870, revenue_rate: 0.6474, distance: 7.6708, memory: -0.0867, power: 0.2320, lr: 0.000100, took: 61.834s
[COMPETITIVE] Epoch 1, Batch 860/1563, loss: 4.182, reward: 28.386, critic_reward: 28.215, revenue_rate: 0.6958, distance: 8.3703, memory: -0.0718, power: 0.2558, lr: 0.000100, took: 70.395s
[COMPETITIVE] Epoch 1, Batch 870/1563, loss: 3.401, reward: 30.527, critic_reward: 30.382, revenue_rate: 0.7462, distance: 9.0396, memory: -0.0495, power: 0.2731, lr: 0.000100, took: 74.407s
[COMPETITIVE] Epoch 1, Batch 880/1563, loss: 5.695, reward: 31.990, critic_reward: 32.596, revenue_rate: 0.7863, distance: 9.7018, memory: -0.0369, power: 0.2920, lr: 0.000100, took: 80.253s
[COMPETITIVE] Epoch 1, Batch 890/1563, loss: 6.347, reward: 30.271, critic_reward: 29.443, revenue_rate: 0.7415, distance: 9.1012, memory: -0.0596, power: 0.2734, lr: 0.000100, took: 73.892s
[COMPETITIVE] Epoch 1, Batch 900/1563, loss: 4.824, reward: 30.596, critic_reward: 30.967, revenue_rate: 0.7498, distance: 9.0905, memory: -0.0569, power: 0.2765, lr: 0.000100, took: 74.591s
[COMPETITIVE] Epoch 1, Batch 910/1563, loss: 5.237, reward: 30.526, critic_reward: 30.370, revenue_rate: 0.7482, distance: 9.1150, memory: -0.0535, power: 0.2739, lr: 0.000100, took: 74.392s
[COMPETITIVE] Epoch 1, Batch 920/1563, loss: 2.829, reward: 31.747, critic_reward: 31.775, revenue_rate: 0.7778, distance: 9.5330, memory: -0.0355, power: 0.2899, lr: 0.000100, took: 78.991s
[COMPETITIVE] Epoch 1, Batch 930/1563, loss: 2.807, reward: 31.685, critic_reward: 31.395, revenue_rate: 0.7776, distance: 9.5824, memory: -0.0323, power: 0.2891, lr: 0.000100, took: 80.367s
[COMPETITIVE] Epoch 1, Batch 940/1563, loss: 5.695, reward: 33.074, critic_reward: 33.474, revenue_rate: 0.8185, distance: 10.4347, memory: -0.0129, power: 0.3162, lr: 0.000100, took: 87.263s
[COMPETITIVE] Epoch 1, Batch 950/1563, loss: 6.217, reward: 33.656, critic_reward: 33.647, revenue_rate: 0.8321, distance: 10.6313, memory: -0.0049, power: 0.3243, lr: 0.000100, took: 90.899s
[COMPETITIVE] Epoch 1, Batch 960/1563, loss: 3.523, reward: 32.174, critic_reward: 31.937, revenue_rate: 0.7882, distance: 9.7858, memory: -0.0227, power: 0.2961, lr: 0.000100, took: 80.963s
[COMPETITIVE] Epoch 1, Batch 970/1563, loss: 17.676, reward: 28.515, critic_reward: 26.942, revenue_rate: 0.6962, distance: 8.3979, memory: -0.0691, power: 0.2561, lr: 0.000100, took: 68.926s
[COMPETITIVE] Epoch 1, Batch 980/1563, loss: 8.444, reward: 28.961, critic_reward: 29.421, revenue_rate: 0.7099, distance: 8.5284, memory: -0.0557, power: 0.2604, lr: 0.000100, took: 72.373s
[COMPETITIVE] Epoch 1, Batch 990/1563, loss: 4.137, reward: 29.584, critic_reward: 29.302, revenue_rate: 0.7267, distance: 8.7921, memory: -0.0547, power: 0.2659, lr: 0.000100, took: 71.621s
[COMPETITIVE] Epoch 1, Batch 1000/1563, loss: 11.580, reward: 32.157, critic_reward: 32.596, revenue_rate: 0.7911, distance: 9.8745, memory: -0.0305, power: 0.2971, lr: 0.000100, took: 81.414s
[COMPETITIVE] Epoch 1, Batch 1010/1563, loss: 5.456, reward: 31.596, critic_reward: 30.358, revenue_rate: 0.7757, distance: 9.4984, memory: -0.0399, power: 0.2910, lr: 0.000100, took: 79.168s
[COMPETITIVE] Epoch 1, Batch 1020/1563, loss: 4.495, reward: 31.811, critic_reward: 31.737, revenue_rate: 0.7833, distance: 9.7505, memory: -0.0341, power: 0.2963, lr: 0.000100, took: 81.397s
[COMPETITIVE] Epoch 1, Batch 1030/1563, loss: 5.153, reward: 31.200, critic_reward: 31.851, revenue_rate: 0.7680, distance: 9.6328, memory: -0.0290, power: 0.2894, lr: 0.000100, took: 78.914s
[COMPETITIVE] Epoch 1, Batch 1040/1563, loss: 3.598, reward: 29.802, critic_reward: 29.950, revenue_rate: 0.7314, distance: 9.0007, memory: -0.0459, power: 0.2738, lr: 0.000100, took: 76.046s
[COMPETITIVE] Epoch 1, Batch 1050/1563, loss: 4.712, reward: 28.072, critic_reward: 27.405, revenue_rate: 0.6862, distance: 8.2003, memory: -0.0777, power: 0.2515, lr: 0.000100, took: 66.660s
[COMPETITIVE] Epoch 1, Batch 1060/1563, loss: 7.681, reward: 29.611, critic_reward: 29.115, revenue_rate: 0.7239, distance: 8.7940, memory: -0.0573, power: 0.2661, lr: 0.000100, took: 72.223s
[COMPETITIVE] Epoch 1, Batch 1070/1563, loss: 5.646, reward: 29.733, critic_reward: 28.998, revenue_rate: 0.7262, distance: 8.7407, memory: -0.0696, power: 0.2660, lr: 0.000100, took: 73.182s
[COMPETITIVE] Epoch 1, Batch 1080/1563, loss: 4.142, reward: 30.686, critic_reward: 30.626, revenue_rate: 0.7479, distance: 9.0629, memory: -0.0516, power: 0.2765, lr: 0.000100, took: 74.791s
[COMPETITIVE] Epoch 1, Batch 1090/1563, loss: 3.598, reward: 30.318, critic_reward: 30.354, revenue_rate: 0.7437, distance: 9.0546, memory: -0.0416, power: 0.2739, lr: 0.000100, took: 73.929s
[COMPETITIVE] Epoch 1, Batch 1100/1563, loss: 12.300, reward: 31.625, critic_reward: 32.081, revenue_rate: 0.7716, distance: 9.3423, memory: -0.0469, power: 0.2857, lr: 0.000100, took: 79.810s
[COMPETITIVE] Epoch 1, Batch 1110/1563, loss: 2.473, reward: 33.091, critic_reward: 32.903, revenue_rate: 0.8154, distance: 10.1983, memory: -0.0107, power: 0.3088, lr: 0.000100, took: 84.809s
[COMPETITIVE] Epoch 1, Batch 1120/1563, loss: 3.524, reward: 31.776, critic_reward: 32.133, revenue_rate: 0.7752, distance: 9.5131, memory: -0.0314, power: 0.2889, lr: 0.000100, took: 78.299s
[COMPETITIVE] Epoch 1, Batch 1130/1563, loss: 7.190, reward: 29.478, critic_reward: 29.695, revenue_rate: 0.7209, distance: 8.7605, memory: -0.0652, power: 0.2636, lr: 0.000100, took: 70.972s
[COMPETITIVE] Epoch 1, Batch 1140/1563, loss: 9.228, reward: 28.518, critic_reward: 26.749, revenue_rate: 0.6962, distance: 8.3860, memory: -0.0682, power: 0.2533, lr: 0.000100, took: 67.807s
[COMPETITIVE] Epoch 1, Batch 1150/1563, loss: 8.419, reward: 30.475, critic_reward: 30.559, revenue_rate: 0.7473, distance: 9.0524, memory: -0.0584, power: 0.2761, lr: 0.000100, took: 74.920s
[COMPETITIVE] Epoch 1, Batch 1160/1563, loss: 3.872, reward: 31.758, critic_reward: 31.445, revenue_rate: 0.7840, distance: 9.6652, memory: -0.0278, power: 0.2921, lr: 0.000100, took: 81.469s
[COMPETITIVE] Epoch 1, Batch 1170/1563, loss: 3.676, reward: 31.572, critic_reward: 32.289, revenue_rate: 0.7733, distance: 9.4852, memory: -0.0408, power: 0.2868, lr: 0.000100, took: 77.970s
[COMPETITIVE] Epoch 1, Batch 1180/1563, loss: 3.926, reward: 31.608, critic_reward: 31.828, revenue_rate: 0.7798, distance: 9.5142, memory: -0.0469, power: 0.2868, lr: 0.000100, took: 78.552s
[COMPETITIVE] Epoch 1, Batch 1190/1563, loss: 2.817, reward: 31.975, critic_reward: 32.397, revenue_rate: 0.7830, distance: 9.7154, memory: -0.0244, power: 0.2949, lr: 0.000100, took: 81.016s
[COMPETITIVE] Epoch 1, Batch 1200/1563, loss: 3.887, reward: 30.151, critic_reward: 29.952, revenue_rate: 0.7377, distance: 8.9132, memory: -0.0563, power: 0.2713, lr: 0.000100, took: 72.525s
[COMPETITIVE] Epoch 1, Batch 1210/1563, loss: 2.595, reward: 28.782, critic_reward: 28.657, revenue_rate: 0.7024, distance: 8.3130, memory: -0.0764, power: 0.2551, lr: 0.000100, took: 67.994s
[COMPETITIVE] Epoch 1, Batch 1220/1563, loss: 3.942, reward: 28.604, critic_reward: 29.049, revenue_rate: 0.7009, distance: 8.4702, memory: -0.0631, power: 0.2563, lr: 0.000100, took: 70.731s
[COMPETITIVE] Epoch 1, Batch 1230/1563, loss: 5.804, reward: 28.154, critic_reward: 29.017, revenue_rate: 0.6870, distance: 8.0965, memory: -0.0788, power: 0.2477, lr: 0.000100, took: 66.289s
[COMPETITIVE] Epoch 1, Batch 1240/1563, loss: 8.474, reward: 28.316, critic_reward: 26.590, revenue_rate: 0.6920, distance: 8.1382, memory: -0.0758, power: 0.2467, lr: 0.000100, took: 66.491s
[COMPETITIVE] Epoch 1, Batch 1250/1563, loss: 2.968, reward: 29.147, critic_reward: 28.863, revenue_rate: 0.7104, distance: 8.4068, memory: -0.0643, power: 0.2565, lr: 0.000100, took: 68.779s
[COMPETITIVE] Epoch 1, Batch 1260/1563, loss: 4.113, reward: 31.209, critic_reward: 31.708, revenue_rate: 0.7677, distance: 9.3181, memory: -0.0422, power: 0.2831, lr: 0.000100, took: 76.608s
[COMPETITIVE] Epoch 1, Batch 1270/1563, loss: 2.614, reward: 31.334, critic_reward: 31.379, revenue_rate: 0.7679, distance: 9.3172, memory: -0.0420, power: 0.2824, lr: 0.000100, took: 76.971s
[COMPETITIVE] Epoch 1, Batch 1280/1563, loss: 7.632, reward: 29.826, critic_reward: 29.392, revenue_rate: 0.7286, distance: 8.7727, memory: -0.0628, power: 0.2667, lr: 0.000100, took: 71.934s
[COMPETITIVE] Epoch 1, Batch 1290/1563, loss: 3.607, reward: 31.103, critic_reward: 31.096, revenue_rate: 0.7614, distance: 9.2592, memory: -0.0455, power: 0.2797, lr: 0.000100, took: 77.820s
[COMPETITIVE] Epoch 1, Batch 1300/1563, loss: 2.422, reward: 31.996, critic_reward: 32.059, revenue_rate: 0.7869, distance: 9.6567, memory: -0.0195, power: 0.2918, lr: 0.000100, took: 79.434s
[COMPETITIVE] Epoch 1, Batch 1310/1563, loss: 2.576, reward: 31.631, critic_reward: 31.932, revenue_rate: 0.7762, distance: 9.5513, memory: -0.0361, power: 0.2859, lr: 0.000100, took: 79.698s
[COMPETITIVE] Epoch 1, Batch 1320/1563, loss: 3.051, reward: 31.751, critic_reward: 31.653, revenue_rate: 0.7825, distance: 9.7339, memory: -0.0313, power: 0.2932, lr: 0.000100, took: 80.012s
[COMPETITIVE] Epoch 1, Batch 1330/1563, loss: 2.948, reward: 33.294, critic_reward: 33.087, revenue_rate: 0.8175, distance: 10.3901, memory: -0.0125, power: 0.3145, lr: 0.000100, took: 86.877s
[COMPETITIVE] Epoch 1, Batch 1340/1563, loss: 5.294, reward: 33.995, critic_reward: 33.989, revenue_rate: 0.8374, distance: 10.6364, memory: -0.0107, power: 0.3234, lr: 0.000100, took: 91.713s
[COMPETITIVE] Epoch 1, Batch 1350/1563, loss: 5.278, reward: 33.446, critic_reward: 34.085, revenue_rate: 0.8248, distance: 10.3422, memory: -0.0084, power: 0.3132, lr: 0.000100, took: 86.345s
[COMPETITIVE] Epoch 1, Batch 1360/1563, loss: 3.051, reward: 32.606, critic_reward: 32.605, revenue_rate: 0.8014, distance: 9.9156, memory: -0.0336, power: 0.3012, lr: 0.000100, took: 82.367s
[COMPETITIVE] Epoch 1, Batch 1370/1563, loss: 3.503, reward: 30.797, critic_reward: 30.234, revenue_rate: 0.7540, distance: 9.1330, memory: -0.0440, power: 0.2758, lr: 0.000100, took: 74.766s
[COMPETITIVE] Epoch 1, Batch 1380/1563, loss: 2.683, reward: 29.410, critic_reward: 28.965, revenue_rate: 0.7193, distance: 8.6065, memory: -0.0572, power: 0.2591, lr: 0.000100, took: 69.956s
[COMPETITIVE] Epoch 1, Batch 1390/1563, loss: 5.861, reward: 29.056, critic_reward: 29.526, revenue_rate: 0.7085, distance: 8.3952, memory: -0.0717, power: 0.2546, lr: 0.000100, took: 68.743s
[COMPETITIVE] Epoch 1, Batch 1400/1563, loss: 6.231, reward: 29.111, critic_reward: 27.609, revenue_rate: 0.7144, distance: 8.4559, memory: -0.0731, power: 0.2562, lr: 0.000100, took: 70.620s
[COMPETITIVE] Epoch 1, Batch 1410/1563, loss: 3.382, reward: 30.412, critic_reward: 30.656, revenue_rate: 0.7468, distance: 8.9635, memory: -0.0451, power: 0.2730, lr: 0.000100, took: 74.008s
[COMPETITIVE] Epoch 1, Batch 1420/1563, loss: 2.540, reward: 31.729, critic_reward: 31.732, revenue_rate: 0.7770, distance: 9.5158, memory: -0.0341, power: 0.2911, lr: 0.000100, took: 79.368s
[COMPETITIVE] Epoch 1, Batch 1430/1563, loss: 2.243, reward: 32.276, critic_reward: 32.179, revenue_rate: 0.7947, distance: 9.9283, memory: -0.0154, power: 0.2998, lr: 0.000100, took: 83.660s
[COMPETITIVE] Epoch 1, Batch 1440/1563, loss: 3.709, reward: 33.119, critic_reward: 33.248, revenue_rate: 0.8198, distance: 10.3169, memory: -0.0144, power: 0.3115, lr: 0.000100, took: 85.933s
[COMPETITIVE] Epoch 1, Batch 1450/1563, loss: 2.665, reward: 32.334, critic_reward: 32.324, revenue_rate: 0.7931, distance: 9.7603, memory: -0.0253, power: 0.2965, lr: 0.000100, took: 82.307s
[COMPETITIVE] Epoch 1, Batch 1460/1563, loss: 4.888, reward: 31.158, critic_reward: 32.309, revenue_rate: 0.7630, distance: 9.1886, memory: -0.0509, power: 0.2807, lr: 0.000100, took: 75.752s
[COMPETITIVE] Epoch 1, Batch 1470/1563, loss: 4.379, reward: 28.735, critic_reward: 28.097, revenue_rate: 0.7020, distance: 8.3137, memory: -0.0652, power: 0.2555, lr: 0.000100, took: 67.947s
[COMPETITIVE] Epoch 1, Batch 1480/1563, loss: 4.191, reward: 30.004, critic_reward: 29.913, revenue_rate: 0.7323, distance: 8.8301, memory: -0.0587, power: 0.2677, lr: 0.000100, took: 72.001s
[COMPETITIVE] Epoch 1, Batch 1490/1563, loss: 4.196, reward: 33.936, critic_reward: 34.326, revenue_rate: 0.8393, distance: 10.6386, memory: 0.0022, power: 0.3206, lr: 0.000100, took: 88.789s
[COMPETITIVE] Epoch 1, Batch 1500/1563, loss: 5.707, reward: 35.956, critic_reward: 36.601, revenue_rate: 0.8945, distance: 11.8128, memory: 0.0309, power: 0.3551, lr: 0.000100, took: 100.284s
[COMPETITIVE] Epoch 1, Batch 1510/1563, loss: 3.815, reward: 35.355, critic_reward: 35.443, revenue_rate: 0.8761, distance: 11.3155, memory: 0.0139, power: 0.3410, lr: 0.000100, took: 98.026s
[COMPETITIVE] Epoch 1, Batch 1520/1563, loss: 6.027, reward: 34.536, critic_reward: 33.662, revenue_rate: 0.8521, distance: 10.8487, memory: -0.0003, power: 0.3285, lr: 0.000100, took: 91.524s
[COMPETITIVE] Epoch 1, Batch 1530/1563, loss: 7.352, reward: 34.840, critic_reward: 34.043, revenue_rate: 0.8629, distance: 11.0078, memory: 0.0099, power: 0.3365, lr: 0.000100, took: 95.516s
[COMPETITIVE] Epoch 1, Batch 1540/1563, loss: 2.730, reward: 32.451, critic_reward: 32.596, revenue_rate: 0.7983, distance: 9.9621, memory: -0.0207, power: 0.3007, lr: 0.000100, took: 81.958s
[COMPETITIVE] Epoch 1, Batch 1550/1563, loss: 5.230, reward: 31.465, critic_reward: 32.207, revenue_rate: 0.7714, distance: 9.3675, memory: -0.0509, power: 0.2827, lr: 0.000100, took: 76.941s
[COMPETITIVE] Epoch 1, Batch 1560/1563, loss: 5.125, reward: 31.527, critic_reward: 32.030, revenue_rate: 0.7730, distance: 9.4575, memory: -0.0426, power: 0.2853, lr: 0.000100, took: 79.903s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 31.954, revenue_rate: 0.7843, distance: 9.5639, memory: -0.0336, power: 0.2898
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39 (验证集奖励: 31.9542)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/1563, loss: 3.834, reward: 31.736, critic_reward: 31.586, revenue_rate: 0.7812, distance: 9.6106, memory: -0.0243, power: 0.2913, lr: 0.000100, took: 81.186s
[COMPETITIVE] Epoch 2, Batch 20/1563, loss: 5.964, reward: 33.084, critic_reward: 31.917, revenue_rate: 0.8147, distance: 10.1944, memory: -0.0003, power: 0.3089, lr: 0.000100, took: 84.483s
[COMPETITIVE] Epoch 2, Batch 30/1563, loss: 5.157, reward: 33.654, critic_reward: 34.372, revenue_rate: 0.8306, distance: 10.3669, memory: -0.0044, power: 0.3145, lr: 0.000100, took: 88.549s
[COMPETITIVE] Epoch 2, Batch 40/1563, loss: 3.387, reward: 32.505, critic_reward: 32.782, revenue_rate: 0.7963, distance: 9.7657, memory: -0.0311, power: 0.2973, lr: 0.000100, took: 80.929s
[COMPETITIVE] Epoch 2, Batch 50/1563, loss: 6.021, reward: 33.519, critic_reward: 32.485, revenue_rate: 0.8247, distance: 10.3285, memory: -0.0258, power: 0.3124, lr: 0.000100, took: 88.412s
[COMPETITIVE] Epoch 2, Batch 60/1563, loss: 5.933, reward: 33.254, critic_reward: 33.646, revenue_rate: 0.8236, distance: 10.3006, memory: -0.0101, power: 0.3114, lr: 0.000100, took: 86.130s
[COMPETITIVE] Epoch 2, Batch 70/1563, loss: 3.936, reward: 31.999, critic_reward: 31.776, revenue_rate: 0.7841, distance: 9.5409, memory: -0.0291, power: 0.2904, lr: 0.000100, took: 78.776s
[COMPETITIVE] Epoch 2, Batch 80/1563, loss: 4.685, reward: 32.756, critic_reward: 33.514, revenue_rate: 0.8012, distance: 9.7055, memory: -0.0376, power: 0.2980, lr: 0.000100, took: 80.589s
[COMPETITIVE] Epoch 2, Batch 90/1563, loss: 2.765, reward: 30.937, critic_reward: 30.485, revenue_rate: 0.7555, distance: 9.0470, memory: -0.0522, power: 0.2753, lr: 0.000100, took: 74.764s
[COMPETITIVE] Epoch 2, Batch 100/1563, loss: 3.904, reward: 31.733, critic_reward: 31.611, revenue_rate: 0.7770, distance: 9.4830, memory: -0.0305, power: 0.2871, lr: 0.000100, took: 78.964s
[COMPETITIVE] Epoch 2, Batch 110/1563, loss: 6.601, reward: 33.816, critic_reward: 35.250, revenue_rate: 0.8294, distance: 10.3521, memory: -0.0146, power: 0.3124, lr: 0.000100, took: 87.810s
[COMPETITIVE] Epoch 2, Batch 120/1563, loss: 2.822, reward: 31.937, critic_reward: 31.726, revenue_rate: 0.7867, distance: 9.6337, memory: -0.0312, power: 0.2923, lr: 0.000100, took: 79.798s
[COMPETITIVE] Epoch 2, Batch 130/1563, loss: 2.958, reward: 31.502, critic_reward: 31.498, revenue_rate: 0.7735, distance: 9.4141, memory: -0.0435, power: 0.2842, lr: 0.000100, took: 77.318s
[COMPETITIVE] Epoch 2, Batch 140/1563, loss: 5.464, reward: 33.047, critic_reward: 33.972, revenue_rate: 0.8074, distance: 9.9574, memory: -0.0231, power: 0.3019, lr: 0.000100, took: 84.218s
[COMPETITIVE] Epoch 2, Batch 150/1563, loss: 7.020, reward: 33.559, critic_reward: 32.199, revenue_rate: 0.8320, distance: 10.6089, memory: 0.0085, power: 0.3191, lr: 0.000100, took: 89.195s
[COMPETITIVE] Epoch 2, Batch 160/1563, loss: 3.454, reward: 34.441, critic_reward: 34.822, revenue_rate: 0.8516, distance: 10.8952, memory: 0.0065, power: 0.3275, lr: 0.000100, took: 93.746s
[COMPETITIVE] Epoch 2, Batch 170/1563, loss: 5.253, reward: 32.700, critic_reward: 32.207, revenue_rate: 0.8049, distance: 9.9262, memory: -0.0350, power: 0.3000, lr: 0.000100, took: 82.108s
[COMPETITIVE] Epoch 2, Batch 180/1563, loss: 4.613, reward: 31.505, critic_reward: 31.985, revenue_rate: 0.7699, distance: 9.2151, memory: -0.0477, power: 0.2813, lr: 0.000100, took: 76.559s
[COMPETITIVE] Epoch 2, Batch 190/1563, loss: 4.977, reward: 29.606, critic_reward: 29.156, revenue_rate: 0.7212, distance: 8.5981, memory: -0.0667, power: 0.2630, lr: 0.000100, took: 70.616s
[COMPETITIVE] Epoch 2, Batch 200/1563, loss: 8.078, reward: 30.516, critic_reward: 30.658, revenue_rate: 0.7496, distance: 9.0419, memory: -0.0535, power: 0.2735, lr: 0.000100, took: 74.360s
[COMPETITIVE] Epoch 2, Batch 210/1563, loss: 5.699, reward: 32.227, critic_reward: 31.288, revenue_rate: 0.7925, distance: 9.7140, memory: -0.0304, power: 0.2958, lr: 0.000100, took: 80.610s
[COMPETITIVE] Epoch 2, Batch 220/1563, loss: 3.446, reward: 33.187, critic_reward: 33.315, revenue_rate: 0.8175, distance: 10.1335, memory: -0.0135, power: 0.3097, lr: 0.000100, took: 87.650s
[COMPETITIVE] Epoch 2, Batch 230/1563, loss: 2.935, reward: 32.261, critic_reward: 32.319, revenue_rate: 0.7946, distance: 9.7251, memory: -0.0253, power: 0.2942, lr: 0.000100, took: 80.101s
[COMPETITIVE] Epoch 2, Batch 240/1563, loss: 8.202, reward: 32.459, critic_reward: 32.893, revenue_rate: 0.7964, distance: 9.7205, memory: -0.0307, power: 0.2933, lr: 0.000100, took: 80.550s
[COMPETITIVE] Epoch 2, Batch 250/1563, loss: 3.656, reward: 31.263, critic_reward: 31.027, revenue_rate: 0.7633, distance: 9.1294, memory: -0.0404, power: 0.2781, lr: 0.000100, took: 77.732s
[COMPETITIVE] Epoch 2, Batch 260/1563, loss: 3.384, reward: 31.081, critic_reward: 30.351, revenue_rate: 0.7603, distance: 9.1054, memory: -0.0552, power: 0.2762, lr: 0.000100, took: 74.835s
[COMPETITIVE] Epoch 2, Batch 270/1563, loss: 8.013, reward: 32.073, critic_reward: 33.086, revenue_rate: 0.7862, distance: 9.5200, memory: -0.0365, power: 0.2914, lr: 0.000100, took: 80.749s
[COMPETITIVE] Epoch 2, Batch 280/1563, loss: 4.563, reward: 32.161, critic_reward: 31.608, revenue_rate: 0.7884, distance: 9.6046, memory: -0.0256, power: 0.2891, lr: 0.000100, took: 79.413s
[COMPETITIVE] Epoch 2, Batch 290/1563, loss: 2.827, reward: 29.578, critic_reward: 29.688, revenue_rate: 0.7221, distance: 8.7128, memory: -0.0633, power: 0.2640, lr: 0.000100, took: 70.477s
[COMPETITIVE] Epoch 2, Batch 300/1563, loss: 7.506, reward: 28.801, critic_reward: 27.151, revenue_rate: 0.7003, distance: 8.3180, memory: -0.0869, power: 0.2516, lr: 0.000100, took: 67.627s
[COMPETITIVE] Epoch 2, Batch 310/1563, loss: 2.888, reward: 29.874, critic_reward: 29.611, revenue_rate: 0.7285, distance: 8.7260, memory: -0.0609, power: 0.2655, lr: 0.000100, took: 71.573s
[COMPETITIVE] Epoch 2, Batch 320/1563, loss: 2.423, reward: 31.436, critic_reward: 31.515, revenue_rate: 0.7699, distance: 9.4031, memory: -0.0397, power: 0.2863, lr: 0.000100, took: 77.988s
[COMPETITIVE] Epoch 2, Batch 330/1563, loss: 4.774, reward: 31.344, critic_reward: 30.897, revenue_rate: 0.7671, distance: 9.2627, memory: -0.0418, power: 0.2816, lr: 0.000100, took: 76.598s
[COMPETITIVE] Epoch 2, Batch 340/1563, loss: 3.538, reward: 30.275, critic_reward: 30.787, revenue_rate: 0.7404, distance: 8.8584, memory: -0.0584, power: 0.2673, lr: 0.000100, took: 74.889s
[COMPETITIVE] Epoch 2, Batch 350/1563, loss: 2.847, reward: 28.103, critic_reward: 28.287, revenue_rate: 0.6856, distance: 8.0585, memory: -0.0810, power: 0.2441, lr: 0.000100, took: 66.016s
[COMPETITIVE] Epoch 2, Batch 360/1563, loss: 4.415, reward: 26.129, critic_reward: 26.585, revenue_rate: 0.6378, distance: 7.3399, memory: -0.1017, power: 0.2245, lr: 0.000100, took: 59.934s
[COMPETITIVE] Epoch 2, Batch 370/1563, loss: 2.934, reward: 26.557, critic_reward: 26.080, revenue_rate: 0.6456, distance: 7.4735, memory: -0.0975, power: 0.2291, lr: 0.000100, took: 62.643s
[COMPETITIVE] Epoch 2, Batch 380/1563, loss: 3.373, reward: 30.415, critic_reward: 30.607, revenue_rate: 0.7440, distance: 8.9021, memory: -0.0512, power: 0.2711, lr: 0.000100, took: 73.060s
[COMPETITIVE] Epoch 2, Batch 390/1563, loss: 3.314, reward: 31.282, critic_reward: 31.714, revenue_rate: 0.7654, distance: 9.2496, memory: -0.0286, power: 0.2826, lr: 0.000100, took: 76.501s
[COMPETITIVE] Epoch 2, Batch 400/1563, loss: 2.264, reward: 30.351, critic_reward: 30.174, revenue_rate: 0.7420, distance: 8.8778, memory: -0.0491, power: 0.2692, lr: 0.000100, took: 74.803s
[COMPETITIVE] Epoch 2, Batch 410/1563, loss: 4.236, reward: 30.183, critic_reward: 29.714, revenue_rate: 0.7368, distance: 8.8116, memory: -0.0504, power: 0.2694, lr: 0.000100, took: 72.758s
[COMPETITIVE] Epoch 2, Batch 420/1563, loss: 6.030, reward: 31.026, critic_reward: 30.799, revenue_rate: 0.7628, distance: 9.2690, memory: -0.0441, power: 0.2782, lr: 0.000100, took: 75.997s
[COMPETITIVE] Epoch 2, Batch 430/1563, loss: 4.494, reward: 32.012, critic_reward: 31.788, revenue_rate: 0.7826, distance: 9.5524, memory: -0.0359, power: 0.2882, lr: 0.000100, took: 78.763s
[COMPETITIVE] Epoch 2, Batch 440/1563, loss: 8.745, reward: 30.753, critic_reward: 32.530, revenue_rate: 0.7525, distance: 8.9984, memory: -0.0529, power: 0.2737, lr: 0.000100, took: 74.146s
[COMPETITIVE] Epoch 2, Batch 450/1563, loss: 7.207, reward: 31.787, critic_reward: 30.326, revenue_rate: 0.7736, distance: 9.3812, memory: -0.0416, power: 0.2838, lr: 0.000100, took: 77.092s
[COMPETITIVE] Epoch 2, Batch 460/1563, loss: 6.968, reward: 32.236, critic_reward: 33.073, revenue_rate: 0.7925, distance: 9.7752, memory: -0.0322, power: 0.2931, lr: 0.000100, took: 81.804s
[COMPETITIVE] Epoch 2, Batch 470/1563, loss: 2.934, reward: 31.096, critic_reward: 31.181, revenue_rate: 0.7595, distance: 9.1312, memory: -0.0476, power: 0.2785, lr: 0.000100, took: 74.867s
[COMPETITIVE] Epoch 2, Batch 480/1563, loss: 4.429, reward: 31.001, critic_reward: 31.199, revenue_rate: 0.7567, distance: 9.0369, memory: -0.0542, power: 0.2750, lr: 0.000100, took: 74.530s
[COMPETITIVE] Epoch 2, Batch 490/1563, loss: 5.774, reward: 30.547, critic_reward: 29.365, revenue_rate: 0.7438, distance: 8.8316, memory: -0.0558, power: 0.2674, lr: 0.000100, took: 74.050s
[COMPETITIVE] Epoch 2, Batch 500/1563, loss: 11.685, reward: 29.941, critic_reward: 32.633, revenue_rate: 0.7344, distance: 8.7555, memory: -0.0601, power: 0.2652, lr: 0.000100, took: 71.361s
[COMPETITIVE] Epoch 2, Batch 510/1563, loss: 4.123, reward: 29.537, critic_reward: 28.826, revenue_rate: 0.7215, distance: 8.6431, memory: -0.0569, power: 0.2643, lr: 0.000100, took: 70.724s
[COMPETITIVE] Epoch 2, Batch 520/1563, loss: 2.953, reward: 30.719, critic_reward: 30.373, revenue_rate: 0.7518, distance: 9.0317, memory: -0.0393, power: 0.2740, lr: 0.000100, took: 76.059s
[COMPETITIVE] Epoch 2, Batch 530/1563, loss: 4.330, reward: 34.071, critic_reward: 34.229, revenue_rate: 0.8340, distance: 10.4398, memory: -0.0114, power: 0.3153, lr: 0.000100, took: 86.850s
[COMPETITIVE] Epoch 2, Batch 540/1563, loss: 3.547, reward: 33.930, critic_reward: 33.492, revenue_rate: 0.8362, distance: 10.4375, memory: -0.0185, power: 0.3146, lr: 0.000100, took: 87.078s
[COMPETITIVE] Epoch 2, Batch 550/1563, loss: 5.895, reward: 33.816, critic_reward: 34.780, revenue_rate: 0.8316, distance: 10.1954, memory: -0.0284, power: 0.3123, lr: 0.000100, took: 85.646s
[COMPETITIVE] Epoch 2, Batch 560/1563, loss: 3.112, reward: 32.088, critic_reward: 31.514, revenue_rate: 0.7889, distance: 9.6179, memory: -0.0317, power: 0.2902, lr: 0.000100, took: 78.522s
[COMPETITIVE] Epoch 2, Batch 570/1563, loss: 7.815, reward: 32.339, critic_reward: 33.966, revenue_rate: 0.7962, distance: 9.7271, memory: -0.0350, power: 0.2952, lr: 0.000100, took: 80.347s
[COMPETITIVE] Epoch 2, Batch 580/1563, loss: 8.036, reward: 31.514, critic_reward: 29.659, revenue_rate: 0.7711, distance: 9.3083, memory: -0.0424, power: 0.2823, lr: 0.000100, took: 78.399s
[COMPETITIVE] Epoch 2, Batch 590/1563, loss: 5.104, reward: 30.514, critic_reward: 31.051, revenue_rate: 0.7446, distance: 8.8467, memory: -0.0603, power: 0.2683, lr: 0.000100, took: 72.521s
[COMPETITIVE] Epoch 2, Batch 600/1563, loss: 5.462, reward: 31.755, critic_reward: 31.423, revenue_rate: 0.7776, distance: 9.3789, memory: -0.0381, power: 0.2855, lr: 0.000100, took: 77.254s
[COMPETITIVE] Epoch 2, Batch 610/1563, loss: 3.738, reward: 32.863, critic_reward: 32.716, revenue_rate: 0.8042, distance: 9.9154, memory: -0.0263, power: 0.2987, lr: 0.000100, took: 83.330s
[COMPETITIVE] Epoch 2, Batch 620/1563, loss: 2.801, reward: 32.279, critic_reward: 32.580, revenue_rate: 0.7906, distance: 9.6656, memory: -0.0308, power: 0.2926, lr: 0.000100, took: 79.549s
[COMPETITIVE] Epoch 2, Batch 630/1563, loss: 2.634, reward: 32.798, critic_reward: 33.278, revenue_rate: 0.8048, distance: 9.8871, memory: -0.0256, power: 0.3002, lr: 0.000100, took: 83.858s
[COMPETITIVE] Epoch 2, Batch 640/1563, loss: 4.549, reward: 32.148, critic_reward: 32.672, revenue_rate: 0.7863, distance: 9.5145, memory: -0.0322, power: 0.2884, lr: 0.000100, took: 78.494s
[COMPETITIVE] Epoch 2, Batch 650/1563, loss: 6.444, reward: 28.952, critic_reward: 27.947, revenue_rate: 0.7020, distance: 8.2458, memory: -0.0823, power: 0.2497, lr: 0.000100, took: 67.722s
[COMPETITIVE] Epoch 2, Batch 660/1563, loss: 9.819, reward: 27.980, critic_reward: 29.565, revenue_rate: 0.6825, distance: 8.0297, memory: -0.0820, power: 0.2437, lr: 0.000100, took: 65.186s
[COMPETITIVE] Epoch 2, Batch 670/1563, loss: 8.643, reward: 28.844, critic_reward: 26.746, revenue_rate: 0.7034, distance: 8.3020, memory: -0.0771, power: 0.2518, lr: 0.000100, took: 67.215s
[COMPETITIVE] Epoch 2, Batch 680/1563, loss: 2.889, reward: 33.206, critic_reward: 32.997, revenue_rate: 0.8175, distance: 10.0843, memory: -0.0140, power: 0.3064, lr: 0.000100, took: 84.092s
[COMPETITIVE] Epoch 2, Batch 690/1563, loss: 4.191, reward: 35.064, critic_reward: 34.429, revenue_rate: 0.8657, distance: 10.9810, memory: 0.0023, power: 0.3316, lr: 0.000100, took: 93.299s
[COMPETITIVE] Epoch 2, Batch 700/1563, loss: 2.932, reward: 34.195, critic_reward: 34.629, revenue_rate: 0.8428, distance: 10.5454, memory: 0.0001, power: 0.3190, lr: 0.000100, took: 89.016s
[COMPETITIVE] Epoch 2, Batch 710/1563, loss: 4.008, reward: 34.215, critic_reward: 33.985, revenue_rate: 0.8411, distance: 10.5487, memory: -0.0146, power: 0.3171, lr: 0.000100, took: 87.558s
[COMPETITIVE] Epoch 2, Batch 720/1563, loss: 3.047, reward: 33.636, critic_reward: 33.995, revenue_rate: 0.8251, distance: 10.2080, memory: -0.0193, power: 0.3088, lr: 0.000100, took: 86.382s
[COMPETITIVE] Epoch 2, Batch 730/1563, loss: 3.013, reward: 33.166, critic_reward: 33.039, revenue_rate: 0.8174, distance: 10.1245, memory: -0.0207, power: 0.3051, lr: 0.000100, took: 83.943s
[COMPETITIVE] Epoch 2, Batch 740/1563, loss: 3.297, reward: 32.761, critic_reward: 32.900, revenue_rate: 0.8041, distance: 9.7883, memory: -0.0279, power: 0.2978, lr: 0.000100, took: 83.231s
[COMPETITIVE] Epoch 2, Batch 750/1563, loss: 5.172, reward: 33.022, critic_reward: 32.252, revenue_rate: 0.8095, distance: 9.9067, memory: -0.0269, power: 0.2997, lr: 0.000100, took: 82.094s
[COMPETITIVE] Epoch 2, Batch 760/1563, loss: 3.496, reward: 32.337, critic_reward: 32.113, revenue_rate: 0.7963, distance: 9.7463, memory: -0.0325, power: 0.2932, lr: 0.000100, took: 80.195s
[COMPETITIVE] Epoch 2, Batch 770/1563, loss: 2.631, reward: 33.363, critic_reward: 33.524, revenue_rate: 0.8162, distance: 10.0126, memory: -0.0231, power: 0.3042, lr: 0.000100, took: 83.165s
[COMPETITIVE] Epoch 2, Batch 780/1563, loss: 3.554, reward: 33.700, critic_reward: 33.798, revenue_rate: 0.8293, distance: 10.2966, memory: -0.0191, power: 0.3097, lr: 0.000100, took: 85.066s
[COMPETITIVE] Epoch 2, Batch 790/1563, loss: 4.563, reward: 33.009, critic_reward: 32.073, revenue_rate: 0.8105, distance: 9.8852, memory: -0.0292, power: 0.3006, lr: 0.000100, took: 81.972s
[COMPETITIVE] Epoch 2, Batch 800/1563, loss: 3.833, reward: 33.523, critic_reward: 34.187, revenue_rate: 0.8237, distance: 10.0714, memory: -0.0209, power: 0.3074, lr: 0.000100, took: 86.557s
[COMPETITIVE] Epoch 2, Batch 810/1563, loss: 5.438, reward: 32.159, critic_reward: 31.837, revenue_rate: 0.7872, distance: 9.5042, memory: -0.0388, power: 0.2913, lr: 0.000100, took: 79.313s
[COMPETITIVE] Epoch 2, Batch 820/1563, loss: 2.331, reward: 30.331, critic_reward: 30.088, revenue_rate: 0.7392, distance: 8.7262, memory: -0.0604, power: 0.2649, lr: 0.000100, took: 71.367s
[COMPETITIVE] Epoch 2, Batch 830/1563, loss: 2.924, reward: 31.255, critic_reward: 31.272, revenue_rate: 0.7667, distance: 9.2661, memory: -0.0450, power: 0.2780, lr: 0.000100, took: 77.332s
[COMPETITIVE] Epoch 2, Batch 840/1563, loss: 4.188, reward: 33.141, critic_reward: 32.896, revenue_rate: 0.8154, distance: 10.1082, memory: -0.0139, power: 0.3076, lr: 0.000100, took: 84.109s
[COMPETITIVE] Epoch 2, Batch 850/1563, loss: 7.243, reward: 33.439, critic_reward: 35.125, revenue_rate: 0.8228, distance: 10.2544, memory: -0.0199, power: 0.3089, lr: 0.000100, took: 87.857s
[COMPETITIVE] Epoch 2, Batch 860/1563, loss: 4.613, reward: 33.428, critic_reward: 32.106, revenue_rate: 0.8209, distance: 10.1017, memory: -0.0236, power: 0.3077, lr: 0.000100, took: 84.682s
[COMPETITIVE] Epoch 2, Batch 870/1563, loss: 16.960, reward: 32.243, critic_reward: 35.901, revenue_rate: 0.7895, distance: 9.5384, memory: -0.0346, power: 0.2897, lr: 0.000100, took: 78.890s
[COMPETITIVE] Epoch 2, Batch 880/1563, loss: 3.949, reward: 31.876, critic_reward: 30.782, revenue_rate: 0.7791, distance: 9.3768, memory: -0.0468, power: 0.2843, lr: 0.000100, took: 77.023s
[COMPETITIVE] Epoch 2, Batch 890/1563, loss: 5.066, reward: 30.658, critic_reward: 31.867, revenue_rate: 0.7506, distance: 9.0158, memory: -0.0599, power: 0.2729, lr: 0.000100, took: 73.912s
[COMPETITIVE] Epoch 2, Batch 900/1563, loss: 5.046, reward: 29.411, critic_reward: 29.004, revenue_rate: 0.7199, distance: 8.5667, memory: -0.0617, power: 0.2623, lr: 0.000100, took: 70.363s
[COMPETITIVE] Epoch 2, Batch 910/1563, loss: 3.863, reward: 30.572, critic_reward: 30.106, revenue_rate: 0.7487, distance: 9.1137, memory: -0.0489, power: 0.2751, lr: 0.000100, took: 74.595s
[COMPETITIVE] Epoch 2, Batch 920/1563, loss: 3.184, reward: 32.373, critic_reward: 32.686, revenue_rate: 0.7933, distance: 9.7956, memory: -0.0247, power: 0.2967, lr: 0.000100, took: 83.023s
[COMPETITIVE] Epoch 2, Batch 930/1563, loss: 5.558, reward: 33.313, critic_reward: 34.086, revenue_rate: 0.8195, distance: 10.1709, memory: -0.0198, power: 0.3064, lr: 0.000100, took: 84.439s
[COMPETITIVE] Epoch 2, Batch 940/1563, loss: 3.465, reward: 33.088, critic_reward: 32.348, revenue_rate: 0.8132, distance: 9.9923, memory: -0.0192, power: 0.3021, lr: 0.000100, took: 85.884s
[COMPETITIVE] Epoch 2, Batch 950/1563, loss: 4.530, reward: 32.656, critic_reward: 32.431, revenue_rate: 0.8000, distance: 9.8049, memory: -0.0294, power: 0.2976, lr: 0.000100, took: 81.450s
[COMPETITIVE] Epoch 2, Batch 960/1563, loss: 3.557, reward: 32.352, critic_reward: 32.160, revenue_rate: 0.7972, distance: 9.9035, memory: -0.0211, power: 0.3015, lr: 0.000100, took: 84.315s
[COMPETITIVE] Epoch 2, Batch 970/1563, loss: 6.712, reward: 33.592, critic_reward: 34.174, revenue_rate: 0.8262, distance: 10.3538, memory: -0.0133, power: 0.3139, lr: 0.000100, took: 86.996s
[COMPETITIVE] Epoch 2, Batch 980/1563, loss: 2.777, reward: 33.971, critic_reward: 33.840, revenue_rate: 0.8351, distance: 10.3912, memory: -0.0140, power: 0.3144, lr: 0.000100, took: 86.269s
[COMPETITIVE] Epoch 2, Batch 990/1563, loss: 5.970, reward: 31.627, critic_reward: 32.818, revenue_rate: 0.7763, distance: 9.3775, memory: -0.0443, power: 0.2826, lr: 0.000100, took: 80.952s
[COMPETITIVE] Epoch 2, Batch 1000/1563, loss: 4.268, reward: 29.916, critic_reward: 29.189, revenue_rate: 0.7317, distance: 8.6867, memory: -0.0641, power: 0.2648, lr: 0.000100, took: 75.811s
[COMPETITIVE] Epoch 2, Batch 1010/1563, loss: 3.207, reward: 30.650, critic_reward: 30.403, revenue_rate: 0.7481, distance: 8.8954, memory: -0.0581, power: 0.2711, lr: 0.000100, took: 75.581s
[COMPETITIVE] Epoch 2, Batch 1020/1563, loss: 4.511, reward: 32.989, critic_reward: 32.060, revenue_rate: 0.8115, distance: 10.0035, memory: -0.0256, power: 0.3034, lr: 0.000100, took: 87.875s
[COMPETITIVE] Epoch 2, Batch 1030/1563, loss: 2.924, reward: 34.038, critic_reward: 33.838, revenue_rate: 0.8408, distance: 10.5746, memory: -0.0014, power: 0.3208, lr: 0.000100, took: 95.843s
[COMPETITIVE] Epoch 2, Batch 1040/1563, loss: 4.006, reward: 33.274, critic_reward: 33.604, revenue_rate: 0.8218, distance: 10.3447, memory: -0.0167, power: 0.3106, lr: 0.000100, took: 88.323s
[COMPETITIVE] Epoch 2, Batch 1050/1563, loss: 4.902, reward: 34.350, critic_reward: 33.332, revenue_rate: 0.8476, distance: 10.8069, memory: -0.0005, power: 0.3256, lr: 0.000100, took: 93.394s
[COMPETITIVE] Epoch 2, Batch 1060/1563, loss: 2.678, reward: 34.013, critic_reward: 33.999, revenue_rate: 0.8408, distance: 10.5166, memory: -0.0043, power: 0.3189, lr: 0.000100, took: 92.729s
[COMPETITIVE] Epoch 2, Batch 1070/1563, loss: 3.309, reward: 32.166, critic_reward: 32.262, revenue_rate: 0.7891, distance: 9.6184, memory: -0.0284, power: 0.2911, lr: 0.000100, took: 89.108s
[COMPETITIVE] Epoch 2, Batch 1080/1563, loss: 3.489, reward: 31.555, critic_reward: 30.973, revenue_rate: 0.7717, distance: 9.2893, memory: -0.0431, power: 0.2827, lr: 0.000100, took: 83.399s
[COMPETITIVE] Epoch 2, Batch 1090/1563, loss: 4.493, reward: 33.615, critic_reward: 34.428, revenue_rate: 0.8250, distance: 10.1928, memory: -0.0104, power: 0.3093, lr: 0.000100, took: 84.564s
[COMPETITIVE] Epoch 2, Batch 1100/1563, loss: 5.541, reward: 33.843, critic_reward: 33.285, revenue_rate: 0.8374, distance: 10.4836, memory: -0.0009, power: 0.3197, lr: 0.000100, took: 92.419s
[COMPETITIVE] Epoch 2, Batch 1110/1563, loss: 3.658, reward: 32.733, critic_reward: 32.475, revenue_rate: 0.8060, distance: 10.0243, memory: -0.0193, power: 0.3072, lr: 0.000100, took: 86.717s
[COMPETITIVE] Epoch 2, Batch 1120/1563, loss: 3.247, reward: 32.644, critic_reward: 32.867, revenue_rate: 0.8026, distance: 10.0547, memory: -0.0240, power: 0.3039, lr: 0.000100, took: 84.992s
[COMPETITIVE] Epoch 2, Batch 1130/1563, loss: 3.308, reward: 32.582, critic_reward: 33.070, revenue_rate: 0.7946, distance: 9.8250, memory: -0.0334, power: 0.2980, lr: 0.000100, took: 83.562s
[COMPETITIVE] Epoch 2, Batch 1140/1563, loss: 3.388, reward: 32.106, critic_reward: 31.654, revenue_rate: 0.7868, distance: 9.6137, memory: -0.0321, power: 0.2920, lr: 0.000100, took: 78.355s
[COMPETITIVE] Epoch 2, Batch 1150/1563, loss: 3.359, reward: 32.226, critic_reward: 31.751, revenue_rate: 0.7863, distance: 9.5339, memory: -0.0277, power: 0.2916, lr: 0.000100, took: 81.922s
[COMPETITIVE] Epoch 2, Batch 1160/1563, loss: 2.389, reward: 32.143, critic_reward: 32.041, revenue_rate: 0.7883, distance: 9.5808, memory: -0.0471, power: 0.2897, lr: 0.000100, took: 78.066s
[COMPETITIVE] Epoch 2, Batch 1170/1563, loss: 3.446, reward: 31.869, critic_reward: 31.816, revenue_rate: 0.7827, distance: 9.4879, memory: -0.0378, power: 0.2863, lr: 0.000100, took: 82.806s
[COMPETITIVE] Epoch 2, Batch 1180/1563, loss: 2.799, reward: 32.405, critic_reward: 32.249, revenue_rate: 0.7933, distance: 9.6635, memory: -0.0352, power: 0.2932, lr: 0.000100, took: 84.094s
[COMPETITIVE] Epoch 2, Batch 1190/1563, loss: 3.496, reward: 32.707, critic_reward: 33.263, revenue_rate: 0.7980, distance: 9.6531, memory: -0.0348, power: 0.2945, lr: 0.000100, took: 84.601s
[COMPETITIVE] Epoch 2, Batch 1200/1563, loss: 6.191, reward: 31.951, critic_reward: 31.293, revenue_rate: 0.7812, distance: 9.4524, memory: -0.0452, power: 0.2853, lr: 0.000100, took: 81.752s
[COMPETITIVE] Epoch 2, Batch 1210/1563, loss: 3.784, reward: 32.043, critic_reward: 32.926, revenue_rate: 0.7839, distance: 9.4191, memory: -0.0378, power: 0.2863, lr: 0.000100, took: 81.207s
[COMPETITIVE] Epoch 2, Batch 1220/1563, loss: 2.102, reward: 30.925, critic_reward: 30.762, revenue_rate: 0.7569, distance: 9.0531, memory: -0.0460, power: 0.2752, lr: 0.000100, took: 76.014s
[COMPETITIVE] Epoch 2, Batch 1230/1563, loss: 3.404, reward: 31.481, critic_reward: 31.485, revenue_rate: 0.7704, distance: 9.2428, memory: -0.0546, power: 0.2814, lr: 0.000100, took: 79.355s
[COMPETITIVE] Epoch 2, Batch 1240/1563, loss: 2.457, reward: 33.520, critic_reward: 33.415, revenue_rate: 0.8231, distance: 10.0159, memory: -0.0257, power: 0.3050, lr: 0.000100, took: 88.005s
[COMPETITIVE] Epoch 2, Batch 1250/1563, loss: 3.049, reward: 33.430, critic_reward: 33.046, revenue_rate: 0.8238, distance: 10.1983, memory: -0.0200, power: 0.3083, lr: 0.000100, took: 88.748s
[COMPETITIVE] Epoch 2, Batch 1260/1563, loss: 2.950, reward: 31.220, critic_reward: 31.253, revenue_rate: 0.7628, distance: 9.1411, memory: -0.0449, power: 0.2788, lr: 0.000100, took: 79.678s
[COMPETITIVE] Epoch 2, Batch 1270/1563, loss: 3.261, reward: 28.569, critic_reward: 28.171, revenue_rate: 0.6979, distance: 8.3046, memory: -0.0648, power: 0.2511, lr: 0.000100, took: 70.592s
[COMPETITIVE] Epoch 2, Batch 1280/1563, loss: 2.562, reward: 30.264, critic_reward: 30.564, revenue_rate: 0.7375, distance: 8.7864, memory: -0.0489, power: 0.2701, lr: 0.000100, took: 77.405s
[COMPETITIVE] Epoch 2, Batch 1290/1563, loss: 4.904, reward: 34.473, critic_reward: 35.252, revenue_rate: 0.8512, distance: 10.7208, memory: 0.0021, power: 0.3237, lr: 0.000100, took: 93.622s
[COMPETITIVE] Epoch 2, Batch 1300/1563, loss: 4.818, reward: 35.667, critic_reward: 34.787, revenue_rate: 0.8829, distance: 11.1906, memory: 0.0132, power: 0.3414, lr: 0.000100, took: 95.813s
[COMPETITIVE] Epoch 2, Batch 1310/1563, loss: 3.131, reward: 34.280, critic_reward: 34.685, revenue_rate: 0.8456, distance: 10.6045, memory: -0.0077, power: 0.3217, lr: 0.000100, took: 90.698s
[COMPETITIVE] Epoch 2, Batch 1320/1563, loss: 2.261, reward: 32.370, critic_reward: 32.573, revenue_rate: 0.7894, distance: 9.5758, memory: -0.0303, power: 0.2923, lr: 0.000100, took: 76.908s
[COMPETITIVE] Epoch 2, Batch 1330/1563, loss: 2.577, reward: 29.512, critic_reward: 29.480, revenue_rate: 0.7209, distance: 8.5028, memory: -0.0736, power: 0.2581, lr: 0.000100, took: 70.182s
[COMPETITIVE] Epoch 2, Batch 1340/1563, loss: 2.961, reward: 27.666, critic_reward: 27.249, revenue_rate: 0.6721, distance: 7.8513, memory: -0.0808, power: 0.2405, lr: 0.000100, took: 65.777s
[COMPETITIVE] Epoch 2, Batch 1350/1563, loss: 6.616, reward: 29.561, critic_reward: 28.996, revenue_rate: 0.7199, distance: 8.4664, memory: -0.0695, power: 0.2578, lr: 0.000100, took: 76.859s
[COMPETITIVE] Epoch 2, Batch 1360/1563, loss: 4.831, reward: 32.090, critic_reward: 31.974, revenue_rate: 0.7859, distance: 9.5160, memory: -0.0448, power: 0.2899, lr: 0.000100, took: 83.241s
[COMPETITIVE] Epoch 2, Batch 1370/1563, loss: 3.465, reward: 32.308, critic_reward: 32.395, revenue_rate: 0.7936, distance: 9.6897, memory: -0.0367, power: 0.2949, lr: 0.000100, took: 83.278s
[COMPETITIVE] Epoch 2, Batch 1380/1563, loss: 2.421, reward: 32.389, critic_reward: 32.258, revenue_rate: 0.7943, distance: 9.6755, memory: -0.0335, power: 0.2936, lr: 0.000100, took: 81.815s
[COMPETITIVE] Epoch 2, Batch 1390/1563, loss: 2.376, reward: 32.851, critic_reward: 32.773, revenue_rate: 0.8081, distance: 9.8524, memory: -0.0245, power: 0.2989, lr: 0.000100, took: 85.968s
[COMPETITIVE] Epoch 2, Batch 1400/1563, loss: 4.870, reward: 32.751, critic_reward: 33.078, revenue_rate: 0.8019, distance: 9.7310, memory: -0.0374, power: 0.2953, lr: 0.000100, took: 82.353s
[COMPETITIVE] Epoch 2, Batch 1410/1563, loss: 8.347, reward: 32.286, critic_reward: 33.537, revenue_rate: 0.7918, distance: 9.6450, memory: -0.0326, power: 0.2927, lr: 0.000100, took: 80.118s
[COMPETITIVE] Epoch 2, Batch 1420/1563, loss: 10.001, reward: 29.733, critic_reward: 31.000, revenue_rate: 0.7248, distance: 8.4732, memory: -0.0675, power: 0.2588, lr: 0.000100, took: 70.161s
[COMPETITIVE] Epoch 2, Batch 1430/1563, loss: 7.524, reward: 29.606, critic_reward: 28.184, revenue_rate: 0.7217, distance: 8.5638, memory: -0.0624, power: 0.2596, lr: 0.000100, took: 70.018s
[COMPETITIVE] Epoch 2, Batch 1440/1563, loss: 4.526, reward: 30.258, critic_reward: 30.175, revenue_rate: 0.7410, distance: 8.8611, memory: -0.0671, power: 0.2683, lr: 0.000100, took: 73.603s
[COMPETITIVE] Epoch 2, Batch 1450/1563, loss: 3.803, reward: 31.501, critic_reward: 31.797, revenue_rate: 0.7707, distance: 9.2404, memory: -0.0426, power: 0.2823, lr: 0.000100, took: 78.924s
[COMPETITIVE] Epoch 2, Batch 1460/1563, loss: 3.284, reward: 32.157, critic_reward: 32.020, revenue_rate: 0.7895, distance: 9.6183, memory: -0.0303, power: 0.2903, lr: 0.000100, took: 83.462s
[COMPETITIVE] Epoch 2, Batch 1470/1563, loss: 2.644, reward: 30.306, critic_reward: 30.193, revenue_rate: 0.7410, distance: 8.8426, memory: -0.0527, power: 0.2688, lr: 0.000100, took: 74.384s
[COMPETITIVE] Epoch 2, Batch 1480/1563, loss: 6.746, reward: 28.224, critic_reward: 27.171, revenue_rate: 0.6900, distance: 8.2026, memory: -0.0722, power: 0.2502, lr: 0.000100, took: 68.675s
[COMPETITIVE] Epoch 2, Batch 1490/1563, loss: 6.162, reward: 28.934, critic_reward: 30.269, revenue_rate: 0.7038, distance: 8.2771, memory: -0.0713, power: 0.2552, lr: 0.000100, took: 72.148s
[COMPETITIVE] Epoch 2, Batch 1500/1563, loss: 8.227, reward: 29.177, critic_reward: 27.370, revenue_rate: 0.7128, distance: 8.4179, memory: -0.0703, power: 0.2559, lr: 0.000100, took: 70.801s
[COMPETITIVE] Epoch 2, Batch 1510/1563, loss: 2.840, reward: 29.236, critic_reward: 29.306, revenue_rate: 0.7152, distance: 8.5605, memory: -0.0587, power: 0.2594, lr: 0.000100, took: 76.670s
[COMPETITIVE] Epoch 2, Batch 1520/1563, loss: 4.089, reward: 31.839, critic_reward: 32.116, revenue_rate: 0.7787, distance: 9.4968, memory: -0.0324, power: 0.2885, lr: 0.000100, took: 81.623s
[COMPETITIVE] Epoch 2, Batch 1530/1563, loss: 2.981, reward: 33.854, critic_reward: 33.530, revenue_rate: 0.8287, distance: 10.1588, memory: -0.0155, power: 0.3110, lr: 0.000100, took: 88.138s
[COMPETITIVE] Epoch 2, Batch 1540/1563, loss: 2.558, reward: 33.947, critic_reward: 33.927, revenue_rate: 0.8338, distance: 10.2067, memory: -0.0237, power: 0.3120, lr: 0.000100, took: 89.618s
[COMPETITIVE] Epoch 2, Batch 1550/1563, loss: 2.986, reward: 32.261, critic_reward: 32.354, revenue_rate: 0.7902, distance: 9.6400, memory: -0.0262, power: 0.2928, lr: 0.000100, took: 81.292s
[COMPETITIVE] Epoch 2, Batch 1560/1563, loss: 2.806, reward: 30.050, critic_reward: 30.115, revenue_rate: 0.7343, distance: 8.7330, memory: -0.0563, power: 0.2672, lr: 0.000100, took: 73.516s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 29.115, revenue_rate: 0.7119, distance: 8.4873, memory: -0.0559, power: 0.2588
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/1563, loss: 6.644, reward: 29.515, critic_reward: 28.466, revenue_rate: 0.7244, distance: 8.7069, memory: -0.0566, power: 0.2639, lr: 0.000100, took: 78.979s
[COMPETITIVE] Epoch 3, Batch 20/1563, loss: 2.980, reward: 30.132, critic_reward: 30.337, revenue_rate: 0.7379, distance: 8.8033, memory: -0.0522, power: 0.2681, lr: 0.000100, took: 76.859s
[COMPETITIVE] Epoch 3, Batch 30/1563, loss: 3.492, reward: 31.860, critic_reward: 31.133, revenue_rate: 0.7802, distance: 9.3961, memory: -0.0391, power: 0.2849, lr: 0.000100, took: 77.662s
[COMPETITIVE] Epoch 3, Batch 40/1563, loss: 2.535, reward: 33.234, critic_reward: 33.270, revenue_rate: 0.8222, distance: 10.1274, memory: -0.0123, power: 0.3087, lr: 0.000100, took: 86.648s
[COMPETITIVE] Epoch 3, Batch 50/1563, loss: 3.574, reward: 34.531, critic_reward: 34.026, revenue_rate: 0.8510, distance: 10.6068, memory: -0.0005, power: 0.3199, lr: 0.000100, took: 91.369s
[COMPETITIVE] Epoch 3, Batch 60/1563, loss: 4.393, reward: 34.420, critic_reward: 35.593, revenue_rate: 0.8485, distance: 10.6295, memory: 0.0007, power: 0.3219, lr: 0.000100, took: 90.621s
[COMPETITIVE] Epoch 3, Batch 70/1563, loss: 5.871, reward: 33.854, critic_reward: 32.645, revenue_rate: 0.8331, distance: 10.3607, memory: -0.0140, power: 0.3165, lr: 0.000100, took: 87.912s
[COMPETITIVE] Epoch 3, Batch 80/1563, loss: 4.464, reward: 33.386, critic_reward: 33.760, revenue_rate: 0.8177, distance: 10.0339, memory: -0.0200, power: 0.3055, lr: 0.000100, took: 86.128s
[COMPETITIVE] Epoch 3, Batch 90/1563, loss: 2.421, reward: 31.649, critic_reward: 31.874, revenue_rate: 0.7759, distance: 9.4010, memory: -0.0524, power: 0.2828, lr: 0.000100, took: 77.192s
[COMPETITIVE] Epoch 3, Batch 100/1563, loss: 2.831, reward: 31.054, critic_reward: 30.848, revenue_rate: 0.7557, distance: 9.0221, memory: -0.0546, power: 0.2741, lr: 0.000100, took: 75.237s
[COMPETITIVE] Epoch 3, Batch 110/1563, loss: 3.174, reward: 31.644, critic_reward: 30.998, revenue_rate: 0.7707, distance: 9.2184, memory: -0.0458, power: 0.2815, lr: 0.000100, took: 81.456s
[COMPETITIVE] Epoch 3, Batch 120/1563, loss: 3.024, reward: 33.729, critic_reward: 33.525, revenue_rate: 0.8304, distance: 10.2540, memory: -0.0176, power: 0.3146, lr: 0.000100, took: 88.556s
[COMPETITIVE] Epoch 3, Batch 130/1563, loss: 8.895, reward: 34.825, critic_reward: 36.850, revenue_rate: 0.8555, distance: 10.7522, memory: -0.0019, power: 0.3272, lr: 0.000100, took: 100.868s
[COMPETITIVE] Epoch 3, Batch 140/1563, loss: 4.940, reward: 34.904, critic_reward: 34.364, revenue_rate: 0.8619, distance: 10.8209, memory: 0.0020, power: 0.3286, lr: 0.000100, took: 100.844s
[COMPETITIVE] Epoch 3, Batch 150/1563, loss: 4.549, reward: 34.309, critic_reward: 34.001, revenue_rate: 0.8413, distance: 10.4323, memory: -0.0109, power: 0.3166, lr: 0.000100, took: 95.714s
[COMPETITIVE] Epoch 3, Batch 160/1563, loss: 2.801, reward: 33.295, critic_reward: 33.642, revenue_rate: 0.8176, distance: 10.1286, memory: -0.0131, power: 0.3062, lr: 0.000100, took: 93.247s
[COMPETITIVE] Epoch 3, Batch 170/1563, loss: 3.326, reward: 32.735, critic_reward: 32.239, revenue_rate: 0.8024, distance: 9.7438, memory: -0.0387, power: 0.2965, lr: 0.000100, took: 90.215s
[COMPETITIVE] Epoch 3, Batch 180/1563, loss: 2.387, reward: 32.750, critic_reward: 33.144, revenue_rate: 0.8032, distance: 9.7803, memory: -0.0259, power: 0.2963, lr: 0.000100, took: 91.867s
[COMPETITIVE] Epoch 3, Batch 190/1563, loss: 2.236, reward: 32.608, critic_reward: 32.116, revenue_rate: 0.7991, distance: 9.6899, memory: -0.0252, power: 0.2952, lr: 0.000100, took: 89.873s
[COMPETITIVE] Epoch 3, Batch 200/1563, loss: 2.969, reward: 32.976, critic_reward: 33.391, revenue_rate: 0.8120, distance: 9.9486, memory: -0.0227, power: 0.3022, lr: 0.000100, took: 94.490s
[COMPETITIVE] Epoch 3, Batch 210/1563, loss: 2.524, reward: 32.306, critic_reward: 32.409, revenue_rate: 0.7891, distance: 9.4895, memory: -0.0378, power: 0.2879, lr: 0.000100, took: 88.098s
[COMPETITIVE] Epoch 3, Batch 220/1563, loss: 2.552, reward: 30.584, critic_reward: 30.159, revenue_rate: 0.7473, distance: 8.8296, memory: -0.0538, power: 0.2692, lr: 0.000100, took: 82.997s
[COMPETITIVE] Epoch 3, Batch 230/1563, loss: 4.109, reward: 31.055, critic_reward: 30.986, revenue_rate: 0.7553, distance: 9.0402, memory: -0.0459, power: 0.2748, lr: 0.000100, took: 85.349s
[COMPETITIVE] Epoch 3, Batch 240/1563, loss: 2.002, reward: 31.487, critic_reward: 31.539, revenue_rate: 0.7689, distance: 9.1897, memory: -0.0409, power: 0.2794, lr: 0.000100, took: 84.978s
[COMPETITIVE] Epoch 3, Batch 250/1563, loss: 2.967, reward: 29.717, critic_reward: 30.296, revenue_rate: 0.7242, distance: 8.5407, memory: -0.0624, power: 0.2602, lr: 0.000100, took: 79.215s
[COMPETITIVE] Epoch 3, Batch 260/1563, loss: 5.644, reward: 29.183, critic_reward: 28.682, revenue_rate: 0.7084, distance: 8.2532, memory: -0.0743, power: 0.2545, lr: 0.000100, took: 76.706s
[COMPETITIVE] Epoch 3, Batch 270/1563, loss: 4.271, reward: 29.772, critic_reward: 29.179, revenue_rate: 0.7287, distance: 8.6693, memory: -0.0599, power: 0.2634, lr: 0.000100, took: 80.255s
[COMPETITIVE] Epoch 3, Batch 280/1563, loss: 2.759, reward: 30.375, critic_reward: 30.805, revenue_rate: 0.7392, distance: 8.8373, memory: -0.0570, power: 0.2663, lr: 0.000100, took: 81.740s
[COMPETITIVE] Epoch 3, Batch 290/1563, loss: 3.931, reward: 31.462, critic_reward: 30.968, revenue_rate: 0.7700, distance: 9.2545, memory: -0.0307, power: 0.2832, lr: 0.000100, took: 81.952s
[COMPETITIVE] Epoch 3, Batch 300/1563, loss: 3.695, reward: 33.146, critic_reward: 33.502, revenue_rate: 0.8125, distance: 9.9340, memory: -0.0182, power: 0.3021, lr: 0.000100, took: 91.647s
[COMPETITIVE] Epoch 3, Batch 310/1563, loss: 2.797, reward: 34.236, critic_reward: 34.264, revenue_rate: 0.8422, distance: 10.4359, memory: -0.0044, power: 0.3161, lr: 0.000100, took: 92.248s
[COMPETITIVE] Epoch 3, Batch 320/1563, loss: 2.765, reward: 34.800, critic_reward: 35.136, revenue_rate: 0.8548, distance: 10.5959, memory: -0.0048, power: 0.3212, lr: 0.000100, took: 100.687s
[COMPETITIVE] Epoch 3, Batch 330/1563, loss: 4.094, reward: 33.731, critic_reward: 33.494, revenue_rate: 0.8281, distance: 10.2949, memory: -0.0087, power: 0.3107, lr: 0.000100, took: 91.735s
[COMPETITIVE] Epoch 3, Batch 340/1563, loss: 5.180, reward: 33.099, critic_reward: 33.539, revenue_rate: 0.8086, distance: 9.8190, memory: -0.0284, power: 0.2999, lr: 0.000100, took: 86.593s
[COMPETITIVE] Epoch 3, Batch 350/1563, loss: 4.068, reward: 31.715, critic_reward: 31.286, revenue_rate: 0.7771, distance: 9.3733, memory: -0.0426, power: 0.2847, lr: 0.000100, took: 84.316s
[COMPETITIVE] Epoch 3, Batch 360/1563, loss: 3.441, reward: 31.588, critic_reward: 31.506, revenue_rate: 0.7740, distance: 9.2904, memory: -0.0427, power: 0.2823, lr: 0.000100, took: 84.084s
[COMPETITIVE] Epoch 3, Batch 370/1563, loss: 2.826, reward: 32.618, critic_reward: 32.657, revenue_rate: 0.8001, distance: 9.6349, memory: -0.0278, power: 0.2956, lr: 0.000100, took: 87.287s
[COMPETITIVE] Epoch 3, Batch 380/1563, loss: 2.757, reward: 33.241, critic_reward: 33.171, revenue_rate: 0.8138, distance: 9.9337, memory: -0.0237, power: 0.3027, lr: 0.000100, took: 111.455s
[COMPETITIVE] Epoch 3, Batch 390/1563, loss: 2.503, reward: 34.055, critic_reward: 33.693, revenue_rate: 0.8387, distance: 10.4351, memory: -0.0044, power: 0.3152, lr: 0.000100, took: 118.600s
[COMPETITIVE] Epoch 3, Batch 400/1563, loss: 3.840, reward: 34.749, critic_reward: 34.824, revenue_rate: 0.8544, distance: 10.6683, memory: -0.0038, power: 0.3241, lr: 0.000100, took: 121.804s
[COMPETITIVE] Epoch 3, Batch 410/1563, loss: 3.953, reward: 35.722, critic_reward: 36.474, revenue_rate: 0.8826, distance: 11.1535, memory: 0.0069, power: 0.3395, lr: 0.000100, took: 126.014s
[COMPETITIVE] Epoch 3, Batch 420/1563, loss: 5.001, reward: 35.065, critic_reward: 34.372, revenue_rate: 0.8661, distance: 10.8840, memory: 0.0007, power: 0.3282, lr: 0.000100, took: 123.272s
[COMPETITIVE] Epoch 3, Batch 430/1563, loss: 2.914, reward: 33.307, critic_reward: 33.034, revenue_rate: 0.8178, distance: 10.1045, memory: -0.0196, power: 0.3053, lr: 0.000100, took: 114.635s
[COMPETITIVE] Epoch 3, Batch 440/1563, loss: 4.819, reward: 32.977, critic_reward: 32.597, revenue_rate: 0.8067, distance: 9.8092, memory: -0.0343, power: 0.2989, lr: 0.000100, took: 116.032s
[COMPETITIVE] Epoch 3, Batch 450/1563, loss: 5.900, reward: 32.893, critic_reward: 31.515, revenue_rate: 0.8085, distance: 9.8599, memory: -0.0263, power: 0.2991, lr: 0.000100, took: 115.379s
[COMPETITIVE] Epoch 3, Batch 460/1563, loss: 2.661, reward: 34.976, critic_reward: 34.646, revenue_rate: 0.8649, distance: 10.8522, memory: 0.0124, power: 0.3326, lr: 0.000100, took: 128.363s
[COMPETITIVE] Epoch 3, Batch 470/1563, loss: 3.232, reward: 35.979, critic_reward: 35.838, revenue_rate: 0.8875, distance: 11.3528, memory: 0.0106, power: 0.3420, lr: 0.000100, took: 124.764s
[COMPETITIVE] Epoch 3, Batch 480/1563, loss: 2.159, reward: 35.180, critic_reward: 35.231, revenue_rate: 0.8683, distance: 10.9064, memory: 0.0105, power: 0.3323, lr: 0.000100, took: 123.227s
[COMPETITIVE] Epoch 3, Batch 490/1563, loss: 2.876, reward: 34.974, critic_reward: 35.073, revenue_rate: 0.8602, distance: 10.7964, memory: 0.0019, power: 0.3269, lr: 0.000100, took: 122.828s
[COMPETITIVE] Epoch 3, Batch 500/1563, loss: 4.859, reward: 33.794, critic_reward: 33.195, revenue_rate: 0.8316, distance: 10.1697, memory: -0.0167, power: 0.3113, lr: 0.000100, took: 117.502s
[COMPETITIVE] Epoch 3, Batch 510/1563, loss: 6.103, reward: 33.434, critic_reward: 35.011, revenue_rate: 0.8199, distance: 10.0308, memory: -0.0217, power: 0.3051, lr: 0.000100, took: 113.640s
[COMPETITIVE] Epoch 3, Batch 520/1563, loss: 3.738, reward: 31.579, critic_reward: 30.809, revenue_rate: 0.7750, distance: 9.3540, memory: -0.0391, power: 0.2837, lr: 0.000100, took: 106.513s
[COMPETITIVE] Epoch 3, Batch 530/1563, loss: 2.867, reward: 31.811, critic_reward: 32.061, revenue_rate: 0.7789, distance: 9.3777, memory: -0.0353, power: 0.2862, lr: 0.000100, took: 106.854s
[COMPETITIVE] Epoch 3, Batch 540/1563, loss: 3.075, reward: 33.044, critic_reward: 33.365, revenue_rate: 0.8145, distance: 10.0223, memory: -0.0221, power: 0.3025, lr: 0.000100, took: 117.004s
[COMPETITIVE] Epoch 3, Batch 550/1563, loss: 2.497, reward: 32.466, critic_reward: 32.176, revenue_rate: 0.7938, distance: 9.5830, memory: -0.0335, power: 0.2942, lr: 0.000100, took: 113.360s
[COMPETITIVE] Epoch 3, Batch 560/1563, loss: 5.400, reward: 32.657, critic_reward: 33.160, revenue_rate: 0.8006, distance: 9.7141, memory: -0.0317, power: 0.2964, lr: 0.000100, took: 111.896s
[COMPETITIVE] Epoch 3, Batch 570/1563, loss: 4.905, reward: 31.763, critic_reward: 31.400, revenue_rate: 0.7791, distance: 9.3521, memory: -0.0488, power: 0.2868, lr: 0.000100, took: 109.524s
[COMPETITIVE] Epoch 3, Batch 580/1563, loss: 2.314, reward: 30.561, critic_reward: 30.264, revenue_rate: 0.7472, distance: 8.8861, memory: -0.0536, power: 0.2717, lr: 0.000100, took: 102.055s
[COMPETITIVE] Epoch 3, Batch 590/1563, loss: 2.478, reward: 31.908, critic_reward: 32.029, revenue_rate: 0.7794, distance: 9.3854, memory: -0.0449, power: 0.2841, lr: 0.000100, took: 107.426s
[COMPETITIVE] Epoch 3, Batch 600/1563, loss: 2.514, reward: 32.479, critic_reward: 32.532, revenue_rate: 0.7989, distance: 9.6660, memory: -0.0331, power: 0.2957, lr: 0.000100, took: 110.783s
[COMPETITIVE] Epoch 3, Batch 610/1563, loss: 5.251, reward: 33.427, critic_reward: 32.461, revenue_rate: 0.8208, distance: 10.1147, memory: -0.0257, power: 0.3072, lr: 0.000100, took: 115.408s
[COMPETITIVE] Epoch 3, Batch 620/1563, loss: 4.418, reward: 33.774, critic_reward: 34.533, revenue_rate: 0.8291, distance: 10.3080, memory: -0.0210, power: 0.3118, lr: 0.000100, took: 119.016s
[COMPETITIVE] Epoch 3, Batch 630/1563, loss: 2.915, reward: 32.695, critic_reward: 32.573, revenue_rate: 0.8019, distance: 9.8659, memory: -0.0272, power: 0.2994, lr: 0.000100, took: 112.604s
[COMPETITIVE] Epoch 3, Batch 640/1563, loss: 3.065, reward: 32.490, critic_reward: 32.157, revenue_rate: 0.7996, distance: 9.7778, memory: -0.0368, power: 0.2951, lr: 0.000100, took: 112.616s
[COMPETITIVE] Epoch 3, Batch 650/1563, loss: 4.214, reward: 33.453, critic_reward: 33.656, revenue_rate: 0.8226, distance: 10.1471, memory: -0.0167, power: 0.3074, lr: 0.000100, took: 117.027s
[COMPETITIVE] Epoch 3, Batch 660/1563, loss: 2.712, reward: 34.457, critic_reward: 34.675, revenue_rate: 0.8488, distance: 10.6093, memory: 0.0004, power: 0.3222, lr: 0.000100, took: 120.783s
[COMPETITIVE] Epoch 3, Batch 670/1563, loss: 4.091, reward: 34.346, critic_reward: 34.694, revenue_rate: 0.8479, distance: 10.5873, memory: -0.0057, power: 0.3188, lr: 0.000100, took: 119.763s
[COMPETITIVE] Epoch 3, Batch 680/1563, loss: 5.572, reward: 33.994, critic_reward: 33.469, revenue_rate: 0.8345, distance: 10.4056, memory: -0.0178, power: 0.3147, lr: 0.000100, took: 117.368s
[COMPETITIVE] Epoch 3, Batch 690/1563, loss: 4.214, reward: 33.504, critic_reward: 33.305, revenue_rate: 0.8227, distance: 10.1632, memory: -0.0201, power: 0.3085, lr: 0.000100, took: 115.800s
[COMPETITIVE] Epoch 3, Batch 700/1563, loss: 3.416, reward: 33.214, critic_reward: 33.195, revenue_rate: 0.8151, distance: 10.0618, memory: -0.0298, power: 0.3043, lr: 0.000100, took: 115.495s
[COMPETITIVE] Epoch 3, Batch 710/1563, loss: 5.774, reward: 31.821, critic_reward: 30.979, revenue_rate: 0.7809, distance: 9.6057, memory: -0.0320, power: 0.2923, lr: 0.000100, took: 110.760s
[COMPETITIVE] Epoch 3, Batch 720/1563, loss: 4.568, reward: 33.146, critic_reward: 33.426, revenue_rate: 0.8146, distance: 10.0695, memory: -0.0166, power: 0.3053, lr: 0.000100, took: 114.790s
[COMPETITIVE] Epoch 3, Batch 730/1563, loss: 2.861, reward: 32.565, critic_reward: 32.660, revenue_rate: 0.7991, distance: 9.6653, memory: -0.0215, power: 0.2958, lr: 0.000100, took: 112.474s
[COMPETITIVE] Epoch 3, Batch 740/1563, loss: 3.885, reward: 32.190, critic_reward: 31.463, revenue_rate: 0.7903, distance: 9.6551, memory: -0.0310, power: 0.2916, lr: 0.000100, took: 109.873s
[COMPETITIVE] Epoch 3, Batch 750/1563, loss: 3.093, reward: 32.445, critic_reward: 32.291, revenue_rate: 0.7959, distance: 9.6987, memory: -0.0367, power: 0.2920, lr: 0.000100, took: 110.155s
[COMPETITIVE] Epoch 3, Batch 760/1563, loss: 3.593, reward: 32.757, critic_reward: 33.224, revenue_rate: 0.8003, distance: 9.6594, memory: -0.0245, power: 0.2961, lr: 0.000100, took: 111.717s
[COMPETITIVE] Epoch 3, Batch 770/1563, loss: 2.808, reward: 30.719, critic_reward: 30.492, revenue_rate: 0.7531, distance: 8.9089, memory: -0.0525, power: 0.2741, lr: 0.000100, took: 101.842s
[COMPETITIVE] Epoch 3, Batch 780/1563, loss: 2.582, reward: 28.780, critic_reward: 29.148, revenue_rate: 0.6992, distance: 8.2296, memory: -0.0806, power: 0.2512, lr: 0.000100, took: 96.010s
[COMPETITIVE] Epoch 3, Batch 790/1563, loss: 3.202, reward: 28.037, critic_reward: 27.911, revenue_rate: 0.6784, distance: 7.8730, memory: -0.0821, power: 0.2419, lr: 0.000100, took: 90.410s
[COMPETITIVE] Epoch 3, Batch 800/1563, loss: 3.771, reward: 32.383, critic_reward: 31.703, revenue_rate: 0.7938, distance: 9.6261, memory: -0.0280, power: 0.2921, lr: 0.000100, took: 111.206s
[COMPETITIVE] Epoch 3, Batch 810/1563, loss: 2.856, reward: 33.785, critic_reward: 33.564, revenue_rate: 0.8313, distance: 10.2072, memory: -0.0074, power: 0.3133, lr: 0.000100, took: 117.608s
[COMPETITIVE] Epoch 3, Batch 820/1563, loss: 3.227, reward: 32.654, critic_reward: 32.321, revenue_rate: 0.8021, distance: 9.7364, memory: -0.0223, power: 0.2972, lr: 0.000100, took: 112.750s
[COMPETITIVE] Epoch 3, Batch 830/1563, loss: 2.968, reward: 33.842, critic_reward: 34.298, revenue_rate: 0.8320, distance: 10.1765, memory: -0.0086, power: 0.3108, lr: 0.000100, took: 117.426s
[COMPETITIVE] Epoch 3, Batch 840/1563, loss: 2.445, reward: 33.267, critic_reward: 32.906, revenue_rate: 0.8178, distance: 9.9982, memory: -0.0213, power: 0.3039, lr: 0.000100, took: 114.698s
[COMPETITIVE] Epoch 3, Batch 850/1563, loss: 2.384, reward: 33.128, critic_reward: 33.068, revenue_rate: 0.8139, distance: 10.0233, memory: -0.0176, power: 0.3049, lr: 0.000100, took: 109.731s
[COMPETITIVE] Epoch 3, Batch 860/1563, loss: 4.655, reward: 33.372, critic_reward: 33.994, revenue_rate: 0.8191, distance: 10.0190, memory: -0.0148, power: 0.3049, lr: 0.000100, took: 112.721s
[COMPETITIVE] Epoch 3, Batch 870/1563, loss: 3.028, reward: 33.604, critic_reward: 33.194, revenue_rate: 0.8266, distance: 10.1028, memory: -0.0227, power: 0.3084, lr: 0.000100, took: 113.692s
[COMPETITIVE] Epoch 3, Batch 880/1563, loss: 2.912, reward: 33.585, critic_reward: 33.930, revenue_rate: 0.8214, distance: 10.1350, memory: -0.0163, power: 0.3069, lr: 0.000100, took: 117.359s
[COMPETITIVE] Epoch 3, Batch 890/1563, loss: 3.309, reward: 33.182, critic_reward: 33.550, revenue_rate: 0.8131, distance: 10.0842, memory: -0.0138, power: 0.3071, lr: 0.000100, took: 116.235s
[COMPETITIVE] Epoch 3, Batch 900/1563, loss: 4.769, reward: 33.004, critic_reward: 32.575, revenue_rate: 0.8111, distance: 9.9916, memory: -0.0267, power: 0.3036, lr: 0.000100, took: 114.344s
[COMPETITIVE] Epoch 3, Batch 910/1563, loss: 2.823, reward: 32.987, critic_reward: 32.633, revenue_rate: 0.8093, distance: 9.8016, memory: -0.0255, power: 0.3010, lr: 0.000100, took: 112.910s
[COMPETITIVE] Epoch 3, Batch 920/1563, loss: 3.031, reward: 34.060, critic_reward: 34.168, revenue_rate: 0.8360, distance: 10.3869, memory: -0.0149, power: 0.3140, lr: 0.000100, took: 117.706s
[COMPETITIVE] Epoch 3, Batch 930/1563, loss: 3.767, reward: 34.077, critic_reward: 33.470, revenue_rate: 0.8411, distance: 10.3767, memory: -0.0119, power: 0.3155, lr: 0.000100, took: 118.240s
[COMPETITIVE] Epoch 3, Batch 940/1563, loss: 2.586, reward: 32.627, critic_reward: 32.939, revenue_rate: 0.7987, distance: 9.7480, memory: -0.0290, power: 0.2958, lr: 0.000100, took: 111.927s
[COMPETITIVE] Epoch 3, Batch 950/1563, loss: 2.788, reward: 32.910, critic_reward: 32.718, revenue_rate: 0.8033, distance: 9.7613, memory: -0.0340, power: 0.2967, lr: 0.000100, took: 111.949s
[COMPETITIVE] Epoch 3, Batch 960/1563, loss: 2.548, reward: 33.010, critic_reward: 32.848, revenue_rate: 0.8109, distance: 9.9631, memory: -0.0240, power: 0.3002, lr: 0.000100, took: 117.954s
[COMPETITIVE] Epoch 3, Batch 970/1563, loss: 5.311, reward: 32.606, critic_reward: 31.739, revenue_rate: 0.7991, distance: 9.7628, memory: -0.0333, power: 0.2944, lr: 0.000100, took: 113.044s
[COMPETITIVE] Epoch 3, Batch 980/1563, loss: 3.638, reward: 31.887, critic_reward: 32.561, revenue_rate: 0.7802, distance: 9.3494, memory: -0.0373, power: 0.2861, lr: 0.000100, took: 107.628s
[COMPETITIVE] Epoch 3, Batch 990/1563, loss: 3.394, reward: 32.267, critic_reward: 32.416, revenue_rate: 0.7892, distance: 9.5397, memory: -0.0402, power: 0.2884, lr: 0.000100, took: 109.336s
[COMPETITIVE] Epoch 3, Batch 1000/1563, loss: 3.942, reward: 32.943, critic_reward: 32.146, revenue_rate: 0.8109, distance: 9.9277, memory: -0.0213, power: 0.2991, lr: 0.000100, took: 112.740s
[COMPETITIVE] Epoch 3, Batch 1010/1563, loss: 2.762, reward: 33.885, critic_reward: 34.178, revenue_rate: 0.8355, distance: 10.3722, memory: -0.0136, power: 0.3168, lr: 0.000100, took: 117.453s
[COMPETITIVE] Epoch 3, Batch 1020/1563, loss: 2.739, reward: 33.956, critic_reward: 33.591, revenue_rate: 0.8348, distance: 10.2973, memory: -0.0139, power: 0.3133, lr: 0.000100, took: 118.852s
[COMPETITIVE] Epoch 3, Batch 1030/1563, loss: 3.348, reward: 34.119, critic_reward: 34.442, revenue_rate: 0.8370, distance: 10.3415, memory: -0.0174, power: 0.3143, lr: 0.000100, took: 120.847s
[COMPETITIVE] Epoch 3, Batch 1040/1563, loss: 6.649, reward: 33.703, critic_reward: 34.874, revenue_rate: 0.8287, distance: 10.2229, memory: -0.0187, power: 0.3086, lr: 0.000100, took: 117.472s
[COMPETITIVE] Epoch 3, Batch 1050/1563, loss: 4.629, reward: 32.576, critic_reward: 31.702, revenue_rate: 0.7997, distance: 9.6560, memory: -0.0241, power: 0.2955, lr: 0.000100, took: 111.644s
[COMPETITIVE] Epoch 3, Batch 1060/1563, loss: 5.696, reward: 32.652, critic_reward: 34.101, revenue_rate: 0.8003, distance: 9.7209, memory: -0.0287, power: 0.2971, lr: 0.000100, took: 111.286s
[COMPETITIVE] Epoch 3, Batch 1070/1563, loss: 2.577, reward: 32.025, critic_reward: 31.645, revenue_rate: 0.7831, distance: 9.4247, memory: -0.0349, power: 0.2868, lr: 0.000100, took: 107.148s
[COMPETITIVE] Epoch 3, Batch 1080/1563, loss: 2.734, reward: 32.768, critic_reward: 32.918, revenue_rate: 0.8056, distance: 9.8922, memory: -0.0213, power: 0.3012, lr: 0.000100, took: 113.434s
[COMPETITIVE] Epoch 3, Batch 1090/1563, loss: 2.122, reward: 33.457, critic_reward: 33.753, revenue_rate: 0.8242, distance: 10.3313, memory: -0.0053, power: 0.3135, lr: 0.000100, took: 117.006s
[COMPETITIVE] Epoch 3, Batch 1100/1563, loss: 3.509, reward: 32.869, critic_reward: 32.989, revenue_rate: 0.8082, distance: 9.8819, memory: -0.0250, power: 0.3013, lr: 0.000100, took: 112.884s
[COMPETITIVE] Epoch 3, Batch 1110/1563, loss: 2.740, reward: 32.026, critic_reward: 31.936, revenue_rate: 0.7827, distance: 9.4205, memory: -0.0463, power: 0.2842, lr: 0.000100, took: 106.092s
[COMPETITIVE] Epoch 3, Batch 1120/1563, loss: 2.957, reward: 31.773, critic_reward: 31.370, revenue_rate: 0.7725, distance: 9.1222, memory: -0.0474, power: 0.2814, lr: 0.000100, took: 105.953s
[COMPETITIVE] Epoch 3, Batch 1130/1563, loss: 3.150, reward: 31.912, critic_reward: 32.531, revenue_rate: 0.7821, distance: 9.4079, memory: -0.0400, power: 0.2877, lr: 0.000100, took: 107.571s
[COMPETITIVE] Epoch 3, Batch 1140/1563, loss: 2.530, reward: 31.594, critic_reward: 31.704, revenue_rate: 0.7734, distance: 9.2019, memory: -0.0474, power: 0.2815, lr: 0.000100, took: 104.176s
[COMPETITIVE] Epoch 3, Batch 1150/1563, loss: 6.273, reward: 32.551, critic_reward: 33.177, revenue_rate: 0.7948, distance: 9.6207, memory: -0.0328, power: 0.2928, lr: 0.000100, took: 109.537s
[COMPETITIVE] Epoch 3, Batch 1160/1563, loss: 3.722, reward: 31.776, critic_reward: 31.260, revenue_rate: 0.7815, distance: 9.4562, memory: -0.0380, power: 0.2875, lr: 0.000100, took: 106.656s
[COMPETITIVE] Epoch 3, Batch 1170/1563, loss: 4.072, reward: 31.376, critic_reward: 32.639, revenue_rate: 0.7670, distance: 9.3140, memory: -0.0408, power: 0.2828, lr: 0.000100, took: 104.402s
[COMPETITIVE] Epoch 3, Batch 1180/1563, loss: 2.478, reward: 30.643, critic_reward: 30.059, revenue_rate: 0.7484, distance: 8.9645, memory: -0.0545, power: 0.2737, lr: 0.000100, took: 103.262s
[COMPETITIVE] Epoch 3, Batch 1190/1563, loss: 2.777, reward: 32.148, critic_reward: 31.911, revenue_rate: 0.7843, distance: 9.4690, memory: -0.0387, power: 0.2877, lr: 0.000100, took: 107.189s
[COMPETITIVE] Epoch 3, Batch 1200/1563, loss: 4.580, reward: 33.985, critic_reward: 34.770, revenue_rate: 0.8379, distance: 10.3699, memory: -0.0170, power: 0.3159, lr: 0.000100, took: 120.060s
[COMPETITIVE] Epoch 3, Batch 1210/1563, loss: 3.616, reward: 34.346, critic_reward: 33.645, revenue_rate: 0.8452, distance: 10.4561, memory: -0.0169, power: 0.3198, lr: 0.000100, took: 118.389s
[COMPETITIVE] Epoch 3, Batch 1220/1563, loss: 3.392, reward: 32.423, critic_reward: 31.952, revenue_rate: 0.7903, distance: 9.5107, memory: -0.0331, power: 0.2912, lr: 0.000100, took: 109.640s
[COMPETITIVE] Epoch 3, Batch 1230/1563, loss: 2.572, reward: 31.732, critic_reward: 31.747, revenue_rate: 0.7738, distance: 9.2429, memory: -0.0447, power: 0.2825, lr: 0.000100, took: 105.067s
[COMPETITIVE] Epoch 3, Batch 1240/1563, loss: 2.832, reward: 33.117, critic_reward: 33.135, revenue_rate: 0.8106, distance: 9.8222, memory: -0.0290, power: 0.3010, lr: 0.000100, took: 111.996s
[COMPETITIVE] Epoch 3, Batch 1250/1563, loss: 2.915, reward: 34.588, critic_reward: 35.176, revenue_rate: 0.8525, distance: 10.6464, memory: 0.0016, power: 0.3250, lr: 0.000100, took: 122.395s
[COMPETITIVE] Epoch 3, Batch 1260/1563, loss: 2.527, reward: 34.527, critic_reward: 34.947, revenue_rate: 0.8523, distance: 10.6310, memory: -0.0024, power: 0.3227, lr: 0.000100, took: 125.422s
[COMPETITIVE] Epoch 3, Batch 1270/1563, loss: 3.122, reward: 34.009, critic_reward: 33.646, revenue_rate: 0.8335, distance: 10.2116, memory: -0.0233, power: 0.3115, lr: 0.000100, took: 118.246s
[COMPETITIVE] Epoch 3, Batch 1280/1563, loss: 2.494, reward: 35.052, critic_reward: 34.961, revenue_rate: 0.8630, distance: 10.7218, memory: 0.0018, power: 0.3249, lr: 0.000100, took: 125.146s
[COMPETITIVE] Epoch 3, Batch 1290/1563, loss: 2.831, reward: 33.743, critic_reward: 33.410, revenue_rate: 0.8309, distance: 10.2625, memory: -0.0110, power: 0.3119, lr: 0.000100, took: 121.878s
[COMPETITIVE] Epoch 3, Batch 1300/1563, loss: 2.671, reward: 32.558, critic_reward: 32.823, revenue_rate: 0.7954, distance: 9.5427, memory: -0.0370, power: 0.2919, lr: 0.000100, took: 104.600s
[COMPETITIVE] Epoch 3, Batch 1310/1563, loss: 2.950, reward: 31.138, critic_reward: 31.056, revenue_rate: 0.7583, distance: 9.0585, memory: -0.0485, power: 0.2747, lr: 0.000100, took: 101.969s
[COMPETITIVE] Epoch 3, Batch 1320/1563, loss: 3.028, reward: 30.237, critic_reward: 30.015, revenue_rate: 0.7372, distance: 8.6986, memory: -0.0601, power: 0.2650, lr: 0.000100, took: 93.902s
[COMPETITIVE] Epoch 3, Batch 1330/1563, loss: 2.678, reward: 30.698, critic_reward: 31.029, revenue_rate: 0.7490, distance: 8.8458, memory: -0.0650, power: 0.2691, lr: 0.000100, took: 99.361s
[COMPETITIVE] Epoch 3, Batch 1340/1563, loss: 2.509, reward: 31.313, critic_reward: 31.184, revenue_rate: 0.7651, distance: 9.1885, memory: -0.0473, power: 0.2787, lr: 0.000100, took: 104.483s
[COMPETITIVE] Epoch 3, Batch 1350/1563, loss: 3.774, reward: 33.055, critic_reward: 32.450, revenue_rate: 0.8108, distance: 9.9504, memory: -0.0209, power: 0.3017, lr: 0.000100, took: 92.960s
[COMPETITIVE] Epoch 3, Batch 1360/1563, loss: 3.239, reward: 34.031, critic_reward: 34.045, revenue_rate: 0.8345, distance: 10.2464, memory: -0.0071, power: 0.3162, lr: 0.000100, took: 88.997s
[COMPETITIVE] Epoch 3, Batch 1370/1563, loss: 2.650, reward: 33.191, critic_reward: 33.141, revenue_rate: 0.8146, distance: 9.9457, memory: -0.0166, power: 0.3026, lr: 0.000100, took: 84.088s
[COMPETITIVE] Epoch 3, Batch 1380/1563, loss: 2.865, reward: 33.253, critic_reward: 32.980, revenue_rate: 0.8127, distance: 9.9133, memory: -0.0205, power: 0.3010, lr: 0.000100, took: 80.941s
[COMPETITIVE] Epoch 3, Batch 1390/1563, loss: 3.277, reward: 32.894, critic_reward: 32.522, revenue_rate: 0.8080, distance: 9.8224, memory: -0.0130, power: 0.2980, lr: 0.000100, took: 82.992s
[COMPETITIVE] Epoch 3, Batch 1400/1563, loss: 3.006, reward: 32.741, critic_reward: 32.645, revenue_rate: 0.8000, distance: 9.6735, memory: -0.0325, power: 0.2939, lr: 0.000100, took: 83.805s
[COMPETITIVE] Epoch 3, Batch 1410/1563, loss: 3.466, reward: 33.394, critic_reward: 33.549, revenue_rate: 0.8168, distance: 9.9485, memory: -0.0308, power: 0.3042, lr: 0.000100, took: 86.620s
[COMPETITIVE] Epoch 3, Batch 1420/1563, loss: 3.208, reward: 33.969, critic_reward: 34.389, revenue_rate: 0.8305, distance: 10.1031, memory: -0.0324, power: 0.3075, lr: 0.000100, took: 89.750s
[COMPETITIVE] Epoch 3, Batch 1430/1563, loss: 2.522, reward: 33.879, critic_reward: 33.304, revenue_rate: 0.8342, distance: 10.2656, memory: -0.0139, power: 0.3138, lr: 0.000100, took: 92.198s
[COMPETITIVE] Epoch 3, Batch 1440/1563, loss: 4.695, reward: 33.732, critic_reward: 33.172, revenue_rate: 0.8291, distance: 10.1623, memory: -0.0156, power: 0.3081, lr: 0.000100, took: 87.872s
[COMPETITIVE] Epoch 3, Batch 1450/1563, loss: 4.997, reward: 31.686, critic_reward: 32.518, revenue_rate: 0.7779, distance: 9.3348, memory: -0.0403, power: 0.2841, lr: 0.000100, took: 85.821s
[COMPETITIVE] Epoch 3, Batch 1460/1563, loss: 3.667, reward: 30.054, critic_reward: 29.927, revenue_rate: 0.7372, distance: 8.7507, memory: -0.0677, power: 0.2655, lr: 0.000100, took: 77.490s
[COMPETITIVE] Epoch 3, Batch 1470/1563, loss: 3.790, reward: 29.883, critic_reward: 28.993, revenue_rate: 0.7321, distance: 8.7215, memory: -0.0563, power: 0.2617, lr: 0.000100, took: 75.271s
[COMPETITIVE] Epoch 3, Batch 1480/1563, loss: 5.976, reward: 31.334, critic_reward: 32.594, revenue_rate: 0.7671, distance: 9.2460, memory: -0.0446, power: 0.2802, lr: 0.000100, took: 82.573s
[COMPETITIVE] Epoch 3, Batch 1490/1563, loss: 4.197, reward: 32.706, critic_reward: 31.759, revenue_rate: 0.8046, distance: 9.8456, memory: -0.0168, power: 0.3015, lr: 0.000100, took: 91.341s
[COMPETITIVE] Epoch 3, Batch 1500/1563, loss: 6.405, reward: 34.383, critic_reward: 35.782, revenue_rate: 0.8458, distance: 10.4828, memory: -0.0091, power: 0.3182, lr: 0.000100, took: 91.784s
[COMPETITIVE] Epoch 3, Batch 1510/1563, loss: 2.834, reward: 35.240, critic_reward: 34.602, revenue_rate: 0.8668, distance: 10.8999, memory: -0.0008, power: 0.3316, lr: 0.000100, took: 100.371s
[COMPETITIVE] Epoch 3, Batch 1520/1563, loss: 5.800, reward: 33.805, critic_reward: 35.024, revenue_rate: 0.8337, distance: 10.3428, memory: -0.0118, power: 0.3127, lr: 0.000100, took: 86.301s
[COMPETITIVE] Epoch 3, Batch 1530/1563, loss: 3.483, reward: 33.470, critic_reward: 32.891, revenue_rate: 0.8236, distance: 10.0684, memory: -0.0209, power: 0.3055, lr: 0.000100, took: 83.370s
[COMPETITIVE] Epoch 3, Batch 1540/1563, loss: 2.911, reward: 32.726, critic_reward: 32.768, revenue_rate: 0.8034, distance: 9.7248, memory: -0.0245, power: 0.2956, lr: 0.000100, took: 80.720s
[COMPETITIVE] Epoch 3, Batch 1550/1563, loss: 4.320, reward: 32.215, critic_reward: 33.044, revenue_rate: 0.7849, distance: 9.4342, memory: -0.0356, power: 0.2880, lr: 0.000100, took: 80.543s
[COMPETITIVE] Epoch 3, Batch 1560/1563, loss: 4.722, reward: 30.977, critic_reward: 29.899, revenue_rate: 0.7566, distance: 8.9809, memory: -0.0531, power: 0.2761, lr: 0.000100, took: 76.969s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 32.104, revenue_rate: 0.7858, distance: 9.4173, memory: -0.0385, power: 0.2866
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39 (验证集奖励: 32.1037)
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-14 15:38:43
训练总耗时: 12:17:34.756956
训练过程统计:
  最终训练奖励: 35.6853
  最佳验证奖励: 32.1037
  训练轮数完成: 4689
  奖励提升: 24.7541
  平均每轮提升: 0.0053
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 10000
  测试批次数: 157
  可视化样本数: 5
测试开始时间: 2025-08-14 15:39:23
测试结束时间: 2025-08-14 16:01:24
测试耗时: 0:22:01.590079

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 32.1037
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39
测试结果:
  平均收益率: 0.7902
  平均距离: 9.5017
  平均内存使用: -0.0384
  平均功耗: 0.2890
模型信息:
  Actor参数: 3,730,953
  Critic参数: 494,285
  总参数: 4,225,238
综合性能评分: 2.9879
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-14 16:07:53
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/1563, loss: 697.619, reward: 15.073, critic_reward: 17.007, revenue_rate: 0.3763, distance: 5.2950, memory: -0.1088, power: 0.1629, lr: 0.000100, took: 46.677s
[HYBRID] Epoch 1, Batch 20/1563, loss: 175.823, reward: 18.761, critic_reward: 14.183, revenue_rate: 0.4672, distance: 6.4308, memory: -0.1354, power: 0.1946, lr: 0.000100, took: 52.355s
[HYBRID] Epoch 1, Batch 30/1563, loss: 111.752, reward: 21.387, critic_reward: 21.710, revenue_rate: 0.5308, distance: 7.1649, memory: -0.1202, power: 0.2153, lr: 0.000100, took: 57.980s
[HYBRID] Epoch 1, Batch 40/1563, loss: 61.013, reward: 24.456, critic_reward: 24.354, revenue_rate: 0.6078, distance: 8.0934, memory: -0.0909, power: 0.2446, lr: 0.000100, took: 65.879s
[HYBRID] Epoch 1, Batch 50/1563, loss: 42.867, reward: 26.089, critic_reward: 26.832, revenue_rate: 0.6461, distance: 8.3903, memory: -0.0767, power: 0.2539, lr: 0.000100, took: 67.917s
[HYBRID] Epoch 1, Batch 60/1563, loss: 55.553, reward: 26.514, critic_reward: 28.501, revenue_rate: 0.6565, distance: 8.4284, memory: -0.0737, power: 0.2553, lr: 0.000100, took: 68.992s
[HYBRID] Epoch 1, Batch 70/1563, loss: 20.719, reward: 24.395, critic_reward: 23.432, revenue_rate: 0.6006, distance: 7.5340, memory: -0.1039, power: 0.2248, lr: 0.000100, took: 60.365s
[HYBRID] Epoch 1, Batch 80/1563, loss: 4.910, reward: 27.083, critic_reward: 27.397, revenue_rate: 0.6655, distance: 8.3907, memory: -0.0748, power: 0.2548, lr: 0.000100, took: 68.625s
[HYBRID] Epoch 1, Batch 90/1563, loss: 11.346, reward: 26.925, critic_reward: 26.655, revenue_rate: 0.6638, distance: 8.3337, memory: -0.0726, power: 0.2547, lr: 0.000100, took: 69.146s
[HYBRID] Epoch 1, Batch 100/1563, loss: 19.019, reward: 25.755, critic_reward: 24.395, revenue_rate: 0.6322, distance: 8.0832, memory: -0.0864, power: 0.2456, lr: 0.000100, took: 67.565s
[HYBRID] Epoch 1, Batch 110/1563, loss: 10.118, reward: 25.675, critic_reward: 26.696, revenue_rate: 0.6331, distance: 7.9733, memory: -0.0844, power: 0.2430, lr: 0.000100, took: 66.571s
[HYBRID] Epoch 1, Batch 120/1563, loss: 15.027, reward: 27.535, critic_reward: 29.094, revenue_rate: 0.6782, distance: 8.5788, memory: -0.0773, power: 0.2587, lr: 0.000100, took: 69.625s
[HYBRID] Epoch 1, Batch 130/1563, loss: 9.578, reward: 25.986, critic_reward: 25.599, revenue_rate: 0.6432, distance: 8.3944, memory: -0.0846, power: 0.2536, lr: 0.000100, took: 70.334s
[HYBRID] Epoch 1, Batch 140/1563, loss: 6.016, reward: 25.254, critic_reward: 24.963, revenue_rate: 0.6240, distance: 7.8177, memory: -0.0824, power: 0.2342, lr: 0.000100, took: 62.756s
[HYBRID] Epoch 1, Batch 150/1563, loss: 6.006, reward: 27.202, critic_reward: 26.682, revenue_rate: 0.6677, distance: 8.2400, memory: -0.0846, power: 0.2477, lr: 0.000100, took: 68.410s
[HYBRID] Epoch 1, Batch 160/1563, loss: 7.589, reward: 28.682, critic_reward: 27.571, revenue_rate: 0.7039, distance: 8.8196, memory: -0.0588, power: 0.2675, lr: 0.000100, took: 72.019s
[HYBRID] Epoch 1, Batch 170/1563, loss: 7.826, reward: 28.348, critic_reward: 27.697, revenue_rate: 0.6967, distance: 8.6153, memory: -0.0659, power: 0.2595, lr: 0.000100, took: 70.177s
[HYBRID] Epoch 1, Batch 180/1563, loss: 11.141, reward: 27.817, critic_reward: 27.540, revenue_rate: 0.6848, distance: 8.4906, memory: -0.0669, power: 0.2558, lr: 0.000100, took: 69.120s
[HYBRID] Epoch 1, Batch 190/1563, loss: 15.735, reward: 27.992, critic_reward: 30.608, revenue_rate: 0.6844, distance: 8.4092, memory: -0.0771, power: 0.2537, lr: 0.000100, took: 69.334s
[HYBRID] Epoch 1, Batch 200/1563, loss: 14.267, reward: 28.060, critic_reward: 25.825, revenue_rate: 0.6891, distance: 8.4924, memory: -0.0750, power: 0.2553, lr: 0.000100, took: 69.079s
[HYBRID] Epoch 1, Batch 210/1563, loss: 5.394, reward: 29.491, critic_reward: 28.948, revenue_rate: 0.7248, distance: 9.1361, memory: -0.0472, power: 0.2751, lr: 0.000100, took: 76.404s
[HYBRID] Epoch 1, Batch 220/1563, loss: 8.075, reward: 28.731, critic_reward: 29.398, revenue_rate: 0.7077, distance: 8.8492, memory: -0.0543, power: 0.2669, lr: 0.000100, took: 72.131s
[HYBRID] Epoch 1, Batch 230/1563, loss: 3.646, reward: 26.314, critic_reward: 26.225, revenue_rate: 0.6454, distance: 7.9519, memory: -0.0934, power: 0.2416, lr: 0.000100, took: 69.151s
[HYBRID] Epoch 1, Batch 240/1563, loss: 6.051, reward: 27.808, critic_reward: 26.899, revenue_rate: 0.6826, distance: 8.3794, memory: -0.0755, power: 0.2532, lr: 0.000100, took: 67.867s
[HYBRID] Epoch 1, Batch 250/1563, loss: 11.181, reward: 31.392, critic_reward: 31.385, revenue_rate: 0.7743, distance: 9.6951, memory: -0.0318, power: 0.2934, lr: 0.000100, took: 80.406s
[HYBRID] Epoch 1, Batch 260/1563, loss: 10.727, reward: 31.686, critic_reward: 33.237, revenue_rate: 0.7794, distance: 9.9590, memory: -0.0306, power: 0.2995, lr: 0.000100, took: 84.675s
[HYBRID] Epoch 1, Batch 270/1563, loss: 6.462, reward: 30.018, critic_reward: 29.361, revenue_rate: 0.7409, distance: 9.2034, memory: -0.0504, power: 0.2782, lr: 0.000100, took: 75.974s
[HYBRID] Epoch 1, Batch 280/1563, loss: 6.000, reward: 29.095, critic_reward: 29.457, revenue_rate: 0.7125, distance: 8.7151, memory: -0.0650, power: 0.2651, lr: 0.000100, took: 71.203s
[HYBRID] Epoch 1, Batch 290/1563, loss: 17.285, reward: 29.855, critic_reward: 29.952, revenue_rate: 0.7300, distance: 8.9862, memory: -0.0515, power: 0.2708, lr: 0.000100, took: 73.377s
[HYBRID] Epoch 1, Batch 300/1563, loss: 28.319, reward: 31.599, critic_reward: 32.320, revenue_rate: 0.7769, distance: 9.7960, memory: -0.0372, power: 0.2948, lr: 0.000100, took: 80.430s
[HYBRID] Epoch 1, Batch 310/1563, loss: 9.441, reward: 31.575, critic_reward: 31.665, revenue_rate: 0.7776, distance: 9.7216, memory: -0.0290, power: 0.2928, lr: 0.000100, took: 80.315s
[HYBRID] Epoch 1, Batch 320/1563, loss: 5.155, reward: 31.401, critic_reward: 31.642, revenue_rate: 0.7705, distance: 9.5904, memory: -0.0334, power: 0.2905, lr: 0.000100, took: 79.387s
[HYBRID] Epoch 1, Batch 330/1563, loss: 4.442, reward: 30.681, critic_reward: 30.669, revenue_rate: 0.7531, distance: 9.3520, memory: -0.0438, power: 0.2850, lr: 0.000100, took: 76.575s
[HYBRID] Epoch 1, Batch 340/1563, loss: 6.619, reward: 28.594, critic_reward: 28.914, revenue_rate: 0.7018, distance: 8.7568, memory: -0.0656, power: 0.2655, lr: 0.000100, took: 71.722s
[HYBRID] Epoch 1, Batch 350/1563, loss: 7.582, reward: 28.023, critic_reward: 27.661, revenue_rate: 0.6900, distance: 8.7213, memory: -0.0679, power: 0.2625, lr: 0.000100, took: 72.432s
[HYBRID] Epoch 1, Batch 360/1563, loss: 4.390, reward: 30.090, critic_reward: 30.320, revenue_rate: 0.7425, distance: 9.4252, memory: -0.0334, power: 0.2825, lr: 0.000100, took: 77.216s
[HYBRID] Epoch 1, Batch 370/1563, loss: 4.612, reward: 32.128, critic_reward: 32.294, revenue_rate: 0.7943, distance: 10.1356, memory: -0.0231, power: 0.3048, lr: 0.000100, took: 86.571s
[HYBRID] Epoch 1, Batch 380/1563, loss: 6.801, reward: 31.347, critic_reward: 30.436, revenue_rate: 0.7714, distance: 9.7099, memory: -0.0351, power: 0.2933, lr: 0.000100, took: 79.519s
[HYBRID] Epoch 1, Batch 390/1563, loss: 6.993, reward: 29.108, critic_reward: 28.801, revenue_rate: 0.7130, distance: 8.7509, memory: -0.0617, power: 0.2667, lr: 0.000100, took: 71.619s
[HYBRID] Epoch 1, Batch 400/1563, loss: 9.887, reward: 28.949, critic_reward: 30.568, revenue_rate: 0.7070, distance: 8.6075, memory: -0.0607, power: 0.2633, lr: 0.000100, took: 70.708s
[HYBRID] Epoch 1, Batch 410/1563, loss: 8.949, reward: 27.575, critic_reward: 28.726, revenue_rate: 0.6749, distance: 8.1818, memory: -0.0808, power: 0.2467, lr: 0.000100, took: 66.491s
[HYBRID] Epoch 1, Batch 420/1563, loss: 11.005, reward: 27.688, critic_reward: 25.737, revenue_rate: 0.6791, distance: 8.2844, memory: -0.0717, power: 0.2498, lr: 0.000100, took: 67.286s
[HYBRID] Epoch 1, Batch 430/1563, loss: 8.252, reward: 29.032, critic_reward: 30.123, revenue_rate: 0.7116, distance: 8.7413, memory: -0.0605, power: 0.2652, lr: 0.000100, took: 71.589s
[HYBRID] Epoch 1, Batch 440/1563, loss: 4.173, reward: 28.392, critic_reward: 28.205, revenue_rate: 0.6969, distance: 8.4046, memory: -0.0685, power: 0.2537, lr: 0.000100, took: 68.185s
[HYBRID] Epoch 1, Batch 450/1563, loss: 5.218, reward: 30.207, critic_reward: 29.797, revenue_rate: 0.7416, distance: 9.1167, memory: -0.0553, power: 0.2770, lr: 0.000100, took: 75.007s
[HYBRID] Epoch 1, Batch 460/1563, loss: 5.289, reward: 32.379, critic_reward: 33.031, revenue_rate: 0.8015, distance: 10.1566, memory: -0.0112, power: 0.3069, lr: 0.000100, took: 84.664s
[HYBRID] Epoch 1, Batch 470/1563, loss: 4.313, reward: 31.696, critic_reward: 30.983, revenue_rate: 0.7824, distance: 9.8403, memory: -0.0188, power: 0.2973, lr: 0.000100, took: 83.064s
[HYBRID] Epoch 1, Batch 480/1563, loss: 5.055, reward: 31.685, critic_reward: 31.465, revenue_rate: 0.7826, distance: 9.9367, memory: -0.0131, power: 0.3013, lr: 0.000100, took: 82.589s
[HYBRID] Epoch 1, Batch 490/1563, loss: 4.690, reward: 29.834, critic_reward: 30.605, revenue_rate: 0.7325, distance: 9.0232, memory: -0.0470, power: 0.2745, lr: 0.000100, took: 75.920s
[HYBRID] Epoch 1, Batch 500/1563, loss: 9.604, reward: 30.970, critic_reward: 30.820, revenue_rate: 0.7604, distance: 9.5965, memory: -0.0340, power: 0.2884, lr: 0.000100, took: 78.250s
[HYBRID] Epoch 1, Batch 510/1563, loss: 9.298, reward: 31.240, critic_reward: 30.532, revenue_rate: 0.7703, distance: 9.7527, memory: -0.0366, power: 0.2988, lr: 0.000100, took: 81.650s
[HYBRID] Epoch 1, Batch 520/1563, loss: 30.047, reward: 28.677, critic_reward: 33.175, revenue_rate: 0.7080, distance: 8.9491, memory: -0.0518, power: 0.2725, lr: 0.000100, took: 73.457s
[HYBRID] Epoch 1, Batch 530/1563, loss: 13.411, reward: 30.633, critic_reward: 28.413, revenue_rate: 0.7544, distance: 9.2007, memory: -0.0492, power: 0.2808, lr: 0.000100, took: 76.367s
[HYBRID] Epoch 1, Batch 540/1563, loss: 12.022, reward: 29.997, critic_reward: 29.293, revenue_rate: 0.7355, distance: 8.9618, memory: -0.0475, power: 0.2716, lr: 0.000100, took: 73.413s
[HYBRID] Epoch 1, Batch 550/1563, loss: 17.121, reward: 29.154, critic_reward: 31.533, revenue_rate: 0.7159, distance: 8.8303, memory: -0.0535, power: 0.2662, lr: 0.000100, took: 72.311s
[HYBRID] Epoch 1, Batch 560/1563, loss: 3.879, reward: 29.387, critic_reward: 29.104, revenue_rate: 0.7208, distance: 8.8262, memory: -0.0541, power: 0.2690, lr: 0.000100, took: 72.817s
[HYBRID] Epoch 1, Batch 570/1563, loss: 9.485, reward: 29.157, critic_reward: 31.062, revenue_rate: 0.7155, distance: 8.7699, memory: -0.0599, power: 0.2655, lr: 0.000100, took: 71.940s
[HYBRID] Epoch 1, Batch 580/1563, loss: 8.962, reward: 28.262, critic_reward: 26.526, revenue_rate: 0.6904, distance: 8.3360, memory: -0.0736, power: 0.2542, lr: 0.000100, took: 68.209s
[HYBRID] Epoch 1, Batch 590/1563, loss: 6.770, reward: 28.731, critic_reward: 29.785, revenue_rate: 0.7016, distance: 8.5070, memory: -0.0668, power: 0.2589, lr: 0.000100, took: 71.358s
[HYBRID] Epoch 1, Batch 600/1563, loss: 5.358, reward: 30.893, critic_reward: 30.386, revenue_rate: 0.7553, distance: 9.2534, memory: -0.0529, power: 0.2798, lr: 0.000100, took: 76.428s
[HYBRID] Epoch 1, Batch 610/1563, loss: 4.700, reward: 31.072, critic_reward: 31.135, revenue_rate: 0.7600, distance: 9.3282, memory: -0.0429, power: 0.2846, lr: 0.000100, took: 78.586s
[HYBRID] Epoch 1, Batch 620/1563, loss: 4.780, reward: 30.479, critic_reward: 30.355, revenue_rate: 0.7543, distance: 9.2808, memory: -0.0394, power: 0.2828, lr: 0.000100, took: 76.778s
[HYBRID] Epoch 1, Batch 630/1563, loss: 6.217, reward: 32.069, critic_reward: 32.328, revenue_rate: 0.7870, distance: 9.7798, memory: -0.0208, power: 0.2970, lr: 0.000100, took: 81.644s
[HYBRID] Epoch 1, Batch 640/1563, loss: 3.703, reward: 29.445, critic_reward: 29.652, revenue_rate: 0.7223, distance: 8.8383, memory: -0.0596, power: 0.2679, lr: 0.000100, took: 71.842s
[HYBRID] Epoch 1, Batch 650/1563, loss: 4.541, reward: 30.024, critic_reward: 29.576, revenue_rate: 0.7365, distance: 9.0802, memory: -0.0509, power: 0.2743, lr: 0.000100, took: 74.029s
[HYBRID] Epoch 1, Batch 660/1563, loss: 6.564, reward: 31.338, critic_reward: 31.348, revenue_rate: 0.7707, distance: 9.4872, memory: -0.0353, power: 0.2859, lr: 0.000100, took: 77.939s
[HYBRID] Epoch 1, Batch 670/1563, loss: 3.576, reward: 30.645, critic_reward: 30.430, revenue_rate: 0.7501, distance: 9.2126, memory: -0.0404, power: 0.2799, lr: 0.000100, took: 75.631s
[HYBRID] Epoch 1, Batch 680/1563, loss: 5.535, reward: 32.204, critic_reward: 32.368, revenue_rate: 0.7936, distance: 9.8642, memory: -0.0225, power: 0.2989, lr: 0.000100, took: 82.156s
[HYBRID] Epoch 1, Batch 690/1563, loss: 4.251, reward: 33.562, critic_reward: 33.930, revenue_rate: 0.8304, distance: 10.6129, memory: -0.0077, power: 0.3190, lr: 0.000100, took: 88.443s
[HYBRID] Epoch 1, Batch 700/1563, loss: 4.908, reward: 31.173, critic_reward: 31.419, revenue_rate: 0.7676, distance: 9.5576, memory: -0.0406, power: 0.2886, lr: 0.000100, took: 81.499s
[HYBRID] Epoch 1, Batch 710/1563, loss: 3.690, reward: 28.707, critic_reward: 28.603, revenue_rate: 0.7006, distance: 8.3868, memory: -0.0802, power: 0.2538, lr: 0.000100, took: 68.924s
[HYBRID] Epoch 1, Batch 720/1563, loss: 7.217, reward: 28.674, critic_reward: 29.183, revenue_rate: 0.6986, distance: 8.3941, memory: -0.0650, power: 0.2554, lr: 0.000100, took: 69.011s
[HYBRID] Epoch 1, Batch 730/1563, loss: 2.709, reward: 28.751, critic_reward: 28.673, revenue_rate: 0.7041, distance: 8.4498, memory: -0.0719, power: 0.2558, lr: 0.000100, took: 70.510s
[HYBRID] Epoch 1, Batch 740/1563, loss: 2.947, reward: 28.852, critic_reward: 28.707, revenue_rate: 0.7067, distance: 8.5195, memory: -0.0685, power: 0.2566, lr: 0.000100, took: 69.011s
[HYBRID] Epoch 1, Batch 750/1563, loss: 4.168, reward: 31.355, critic_reward: 31.564, revenue_rate: 0.7696, distance: 9.4709, memory: -0.0366, power: 0.2854, lr: 0.000100, took: 79.668s
[HYBRID] Epoch 1, Batch 760/1563, loss: 6.468, reward: 32.855, critic_reward: 32.730, revenue_rate: 0.8116, distance: 10.2901, memory: -0.0106, power: 0.3087, lr: 0.000100, took: 88.213s
[HYBRID] Epoch 1, Batch 770/1563, loss: 5.675, reward: 31.882, critic_reward: 30.846, revenue_rate: 0.7861, distance: 9.8461, memory: -0.0253, power: 0.2986, lr: 0.000100, took: 84.334s
[HYBRID] Epoch 1, Batch 780/1563, loss: 4.715, reward: 30.140, critic_reward: 29.533, revenue_rate: 0.7401, distance: 9.0554, memory: -0.0468, power: 0.2746, lr: 0.000100, took: 74.131s
[HYBRID] Epoch 1, Batch 790/1563, loss: 3.398, reward: 31.141, critic_reward: 31.135, revenue_rate: 0.7613, distance: 9.2392, memory: -0.0514, power: 0.2799, lr: 0.000100, took: 76.178s
[HYBRID] Epoch 1, Batch 800/1563, loss: 5.530, reward: 32.174, critic_reward: 31.949, revenue_rate: 0.7894, distance: 9.7779, memory: -0.0336, power: 0.2961, lr: 0.000100, took: 81.187s
[HYBRID] Epoch 1, Batch 810/1563, loss: 3.758, reward: 29.805, critic_reward: 29.506, revenue_rate: 0.7297, distance: 8.8604, memory: -0.0531, power: 0.2695, lr: 0.000100, took: 72.161s
[HYBRID] Epoch 1, Batch 820/1563, loss: 4.928, reward: 31.168, critic_reward: 31.788, revenue_rate: 0.7636, distance: 9.3642, memory: -0.0419, power: 0.2827, lr: 0.000100, took: 78.770s
[HYBRID] Epoch 1, Batch 830/1563, loss: 4.960, reward: 32.232, critic_reward: 32.382, revenue_rate: 0.7936, distance: 9.8723, memory: -0.0254, power: 0.2971, lr: 0.000100, took: 80.838s
[HYBRID] Epoch 1, Batch 840/1563, loss: 4.243, reward: 31.954, critic_reward: 31.686, revenue_rate: 0.7850, distance: 9.7736, memory: -0.0323, power: 0.2963, lr: 0.000100, took: 82.796s
[HYBRID] Epoch 1, Batch 850/1563, loss: 2.555, reward: 32.790, critic_reward: 32.940, revenue_rate: 0.8062, distance: 10.0307, memory: -0.0233, power: 0.3061, lr: 0.000100, took: 83.766s
[HYBRID] Epoch 1, Batch 860/1563, loss: 5.406, reward: 32.738, critic_reward: 32.676, revenue_rate: 0.8068, distance: 10.1247, memory: -0.0201, power: 0.3069, lr: 0.000100, took: 83.726s
[HYBRID] Epoch 1, Batch 870/1563, loss: 3.125, reward: 31.908, critic_reward: 31.992, revenue_rate: 0.7863, distance: 9.7992, memory: -0.0211, power: 0.2959, lr: 0.000100, took: 80.778s
[HYBRID] Epoch 1, Batch 880/1563, loss: 5.058, reward: 32.589, critic_reward: 32.523, revenue_rate: 0.8021, distance: 10.0287, memory: -0.0211, power: 0.3038, lr: 0.000100, took: 83.377s
[HYBRID] Epoch 1, Batch 890/1563, loss: 9.162, reward: 30.364, critic_reward: 30.062, revenue_rate: 0.7465, distance: 9.0297, memory: -0.0503, power: 0.2764, lr: 0.000100, took: 74.125s
[HYBRID] Epoch 1, Batch 900/1563, loss: 7.738, reward: 29.583, critic_reward: 28.426, revenue_rate: 0.7254, distance: 8.7772, memory: -0.0518, power: 0.2647, lr: 0.000100, took: 71.777s
[HYBRID] Epoch 1, Batch 910/1563, loss: 2.264, reward: 31.059, critic_reward: 31.227, revenue_rate: 0.7608, distance: 9.1839, memory: -0.0528, power: 0.2808, lr: 0.000100, took: 75.772s
[HYBRID] Epoch 1, Batch 920/1563, loss: 3.507, reward: 29.047, critic_reward: 28.610, revenue_rate: 0.7133, distance: 8.6304, memory: -0.0631, power: 0.2604, lr: 0.000100, took: 70.367s
[HYBRID] Epoch 1, Batch 930/1563, loss: 7.828, reward: 29.338, critic_reward: 30.651, revenue_rate: 0.7156, distance: 8.6443, memory: -0.0617, power: 0.2619, lr: 0.000100, took: 70.373s
[HYBRID] Epoch 1, Batch 940/1563, loss: 9.988, reward: 28.441, critic_reward: 27.966, revenue_rate: 0.6950, distance: 8.2482, memory: -0.0669, power: 0.2521, lr: 0.000100, took: 69.553s
[HYBRID] Epoch 1, Batch 950/1563, loss: 8.002, reward: 28.507, critic_reward: 28.975, revenue_rate: 0.6969, distance: 8.2948, memory: -0.0732, power: 0.2522, lr: 0.000100, took: 67.647s
[HYBRID] Epoch 1, Batch 960/1563, loss: 4.202, reward: 30.198, critic_reward: 30.315, revenue_rate: 0.7414, distance: 8.9487, memory: -0.0562, power: 0.2733, lr: 0.000100, took: 75.587s
[HYBRID] Epoch 1, Batch 970/1563, loss: 3.216, reward: 32.545, critic_reward: 32.355, revenue_rate: 0.7992, distance: 9.8679, memory: -0.0280, power: 0.2979, lr: 0.000100, took: 81.639s
[HYBRID] Epoch 1, Batch 980/1563, loss: 4.260, reward: 33.578, critic_reward: 33.605, revenue_rate: 0.8259, distance: 10.3210, memory: -0.0096, power: 0.3127, lr: 0.000100, took: 87.039s
[HYBRID] Epoch 1, Batch 990/1563, loss: 4.227, reward: 34.322, critic_reward: 34.808, revenue_rate: 0.8446, distance: 10.6153, memory: -0.0056, power: 0.3208, lr: 0.000100, took: 89.094s
[HYBRID] Epoch 1, Batch 1000/1563, loss: 3.558, reward: 33.725, critic_reward: 33.771, revenue_rate: 0.8318, distance: 10.5299, memory: -0.0018, power: 0.3166, lr: 0.000100, took: 88.037s
[HYBRID] Epoch 1, Batch 1010/1563, loss: 6.483, reward: 33.376, critic_reward: 32.059, revenue_rate: 0.8224, distance: 10.3907, memory: -0.0066, power: 0.3154, lr: 0.000100, took: 87.103s
[HYBRID] Epoch 1, Batch 1020/1563, loss: 11.652, reward: 32.933, critic_reward: 35.140, revenue_rate: 0.8094, distance: 10.1406, memory: -0.0281, power: 0.3048, lr: 0.000100, took: 83.160s
[HYBRID] Epoch 1, Batch 1030/1563, loss: 10.250, reward: 33.864, critic_reward: 33.244, revenue_rate: 0.8343, distance: 10.4406, memory: -0.0148, power: 0.3158, lr: 0.000100, took: 87.177s
[HYBRID] Epoch 1, Batch 1040/1563, loss: 7.430, reward: 33.525, critic_reward: 32.745, revenue_rate: 0.8248, distance: 10.2182, memory: -0.0091, power: 0.3102, lr: 0.000100, took: 86.183s
[HYBRID] Epoch 1, Batch 1050/1563, loss: 2.452, reward: 30.946, critic_reward: 30.990, revenue_rate: 0.7571, distance: 9.1775, memory: -0.0433, power: 0.2772, lr: 0.000100, took: 77.288s
[HYBRID] Epoch 1, Batch 1060/1563, loss: 12.099, reward: 29.981, critic_reward: 31.003, revenue_rate: 0.7346, distance: 8.8757, memory: -0.0530, power: 0.2686, lr: 0.000100, took: 73.034s
[HYBRID] Epoch 1, Batch 1070/1563, loss: 6.852, reward: 31.791, critic_reward: 31.410, revenue_rate: 0.7839, distance: 9.8000, memory: -0.0367, power: 0.2940, lr: 0.000100, took: 82.884s
[HYBRID] Epoch 1, Batch 1080/1563, loss: 4.455, reward: 30.783, critic_reward: 30.074, revenue_rate: 0.7567, distance: 9.3004, memory: -0.0450, power: 0.2813, lr: 0.000100, took: 75.842s
[HYBRID] Epoch 1, Batch 1090/1563, loss: 5.329, reward: 31.689, critic_reward: 31.633, revenue_rate: 0.7764, distance: 9.5121, memory: -0.0362, power: 0.2882, lr: 0.000100, took: 78.390s
[HYBRID] Epoch 1, Batch 1100/1563, loss: 6.191, reward: 30.030, critic_reward: 30.655, revenue_rate: 0.7374, distance: 8.8257, memory: -0.0552, power: 0.2665, lr: 0.000100, took: 72.345s
[HYBRID] Epoch 1, Batch 1110/1563, loss: 4.576, reward: 27.861, critic_reward: 27.054, revenue_rate: 0.6807, distance: 8.0458, memory: -0.0940, power: 0.2437, lr: 0.000100, took: 65.518s
[HYBRID] Epoch 1, Batch 1120/1563, loss: 9.662, reward: 27.974, critic_reward: 28.933, revenue_rate: 0.6824, distance: 8.1082, memory: -0.0846, power: 0.2452, lr: 0.000100, took: 65.436s
[HYBRID] Epoch 1, Batch 1130/1563, loss: 9.091, reward: 30.379, critic_reward: 28.323, revenue_rate: 0.7464, distance: 8.9908, memory: -0.0539, power: 0.2730, lr: 0.000100, took: 73.637s
[HYBRID] Epoch 1, Batch 1140/1563, loss: 2.684, reward: 32.354, critic_reward: 32.513, revenue_rate: 0.7935, distance: 9.6456, memory: -0.0335, power: 0.2957, lr: 0.000100, took: 80.971s
[HYBRID] Epoch 1, Batch 1150/1563, loss: 4.436, reward: 32.474, critic_reward: 32.362, revenue_rate: 0.8028, distance: 9.9511, memory: -0.0261, power: 0.3030, lr: 0.000100, took: 83.373s
[HYBRID] Epoch 1, Batch 1160/1563, loss: 4.740, reward: 32.054, critic_reward: 31.810, revenue_rate: 0.7888, distance: 9.7706, memory: -0.0337, power: 0.2947, lr: 0.000100, took: 82.936s
[HYBRID] Epoch 1, Batch 1170/1563, loss: 5.491, reward: 31.846, critic_reward: 32.418, revenue_rate: 0.7813, distance: 9.6817, memory: -0.0332, power: 0.2949, lr: 0.000100, took: 83.039s
[HYBRID] Epoch 1, Batch 1180/1563, loss: 4.252, reward: 32.777, critic_reward: 32.177, revenue_rate: 0.8077, distance: 10.0143, memory: -0.0249, power: 0.3045, lr: 0.000100, took: 83.560s
[HYBRID] Epoch 1, Batch 1190/1563, loss: 5.487, reward: 31.922, critic_reward: 32.681, revenue_rate: 0.7811, distance: 9.6762, memory: -0.0363, power: 0.2923, lr: 0.000100, took: 81.939s
[HYBRID] Epoch 1, Batch 1200/1563, loss: 5.796, reward: 30.496, critic_reward: 29.341, revenue_rate: 0.7475, distance: 9.1016, memory: -0.0429, power: 0.2773, lr: 0.000100, took: 74.607s
[HYBRID] Epoch 1, Batch 1210/1563, loss: 10.504, reward: 31.572, critic_reward: 33.029, revenue_rate: 0.7769, distance: 9.5771, memory: -0.0306, power: 0.2901, lr: 0.000100, took: 79.394s
[HYBRID] Epoch 1, Batch 1220/1563, loss: 10.312, reward: 32.011, critic_reward: 32.892, revenue_rate: 0.7870, distance: 9.6786, memory: -0.0394, power: 0.2964, lr: 0.000100, took: 80.336s
[HYBRID] Epoch 1, Batch 1230/1563, loss: 6.995, reward: 30.011, critic_reward: 30.675, revenue_rate: 0.7369, distance: 9.0499, memory: -0.0503, power: 0.2727, lr: 0.000100, took: 74.149s
[HYBRID] Epoch 1, Batch 1240/1563, loss: 3.046, reward: 30.596, critic_reward: 30.860, revenue_rate: 0.7472, distance: 9.0851, memory: -0.0499, power: 0.2760, lr: 0.000100, took: 74.757s
[HYBRID] Epoch 1, Batch 1250/1563, loss: 2.928, reward: 30.035, critic_reward: 29.562, revenue_rate: 0.7333, distance: 8.7728, memory: -0.0564, power: 0.2708, lr: 0.000100, took: 71.969s
[HYBRID] Epoch 1, Batch 1260/1563, loss: 3.834, reward: 30.698, critic_reward: 31.126, revenue_rate: 0.7489, distance: 9.0281, memory: -0.0516, power: 0.2757, lr: 0.000100, took: 73.830s
[HYBRID] Epoch 1, Batch 1270/1563, loss: 6.563, reward: 32.294, critic_reward: 31.703, revenue_rate: 0.7939, distance: 9.8237, memory: -0.0292, power: 0.2946, lr: 0.000100, took: 80.979s
[HYBRID] Epoch 1, Batch 1280/1563, loss: 4.838, reward: 32.052, critic_reward: 31.837, revenue_rate: 0.7857, distance: 9.6099, memory: -0.0291, power: 0.2920, lr: 0.000100, took: 81.546s
[HYBRID] Epoch 1, Batch 1290/1563, loss: 3.549, reward: 32.308, critic_reward: 32.780, revenue_rate: 0.7958, distance: 9.8458, memory: -0.0302, power: 0.2975, lr: 0.000100, took: 81.596s
[HYBRID] Epoch 1, Batch 1300/1563, loss: 4.382, reward: 30.505, critic_reward: 30.008, revenue_rate: 0.7504, distance: 9.2955, memory: -0.0437, power: 0.2841, lr: 0.000100, took: 78.353s
[HYBRID] Epoch 1, Batch 1310/1563, loss: 3.662, reward: 31.538, critic_reward: 31.070, revenue_rate: 0.7741, distance: 9.4788, memory: -0.0394, power: 0.2880, lr: 0.000100, took: 78.443s
[HYBRID] Epoch 1, Batch 1320/1563, loss: 5.353, reward: 31.750, critic_reward: 32.693, revenue_rate: 0.7795, distance: 9.5517, memory: -0.0354, power: 0.2872, lr: 0.000100, took: 78.522s
[HYBRID] Epoch 1, Batch 1330/1563, loss: 6.782, reward: 32.590, critic_reward: 32.633, revenue_rate: 0.8012, distance: 9.8780, memory: -0.0230, power: 0.3022, lr: 0.000100, took: 82.630s
[HYBRID] Epoch 1, Batch 1340/1563, loss: 5.154, reward: 33.738, critic_reward: 33.428, revenue_rate: 0.8313, distance: 10.4495, memory: -0.0166, power: 0.3159, lr: 0.000100, took: 87.413s
[HYBRID] Epoch 1, Batch 1350/1563, loss: 3.231, reward: 33.172, critic_reward: 33.399, revenue_rate: 0.8172, distance: 10.2608, memory: -0.0056, power: 0.3080, lr: 0.000100, took: 85.085s
[HYBRID] Epoch 1, Batch 1360/1563, loss: 6.052, reward: 31.449, critic_reward: 31.726, revenue_rate: 0.7718, distance: 9.3352, memory: -0.0413, power: 0.2851, lr: 0.000100, took: 77.936s
[HYBRID] Epoch 1, Batch 1370/1563, loss: 8.925, reward: 29.692, critic_reward: 28.119, revenue_rate: 0.7240, distance: 8.5629, memory: -0.0629, power: 0.2618, lr: 0.000100, took: 70.982s
[HYBRID] Epoch 1, Batch 1380/1563, loss: 3.336, reward: 31.692, critic_reward: 32.244, revenue_rate: 0.7749, distance: 9.4892, memory: -0.0482, power: 0.2876, lr: 0.000100, took: 78.028s
[HYBRID] Epoch 1, Batch 1390/1563, loss: 3.580, reward: 32.551, critic_reward: 32.514, revenue_rate: 0.7967, distance: 9.8279, memory: -0.0427, power: 0.2975, lr: 0.000100, took: 83.440s
[HYBRID] Epoch 1, Batch 1400/1563, loss: 3.686, reward: 32.304, critic_reward: 32.718, revenue_rate: 0.7890, distance: 9.6721, memory: -0.0419, power: 0.2942, lr: 0.000100, took: 80.095s
[HYBRID] Epoch 1, Batch 1410/1563, loss: 4.457, reward: 32.881, critic_reward: 33.192, revenue_rate: 0.8113, distance: 9.9580, memory: -0.0269, power: 0.3052, lr: 0.000100, took: 86.677s
[HYBRID] Epoch 1, Batch 1420/1563, loss: 3.171, reward: 33.375, critic_reward: 33.765, revenue_rate: 0.8214, distance: 10.2710, memory: -0.0132, power: 0.3114, lr: 0.000100, took: 89.503s
[HYBRID] Epoch 1, Batch 1430/1563, loss: 5.994, reward: 34.212, critic_reward: 33.452, revenue_rate: 0.8468, distance: 10.7906, memory: -0.0016, power: 0.3285, lr: 0.000100, took: 95.096s
[HYBRID] Epoch 1, Batch 1440/1563, loss: 9.316, reward: 33.742, critic_reward: 35.006, revenue_rate: 0.8299, distance: 10.4313, memory: -0.0085, power: 0.3162, lr: 0.000100, took: 90.862s
[HYBRID] Epoch 1, Batch 1450/1563, loss: 3.157, reward: 33.950, critic_reward: 34.057, revenue_rate: 0.8351, distance: 10.4709, memory: -0.0051, power: 0.3150, lr: 0.000100, took: 91.170s
[HYBRID] Epoch 1, Batch 1460/1563, loss: 11.736, reward: 32.339, critic_reward: 32.848, revenue_rate: 0.7945, distance: 9.7413, memory: -0.0360, power: 0.2946, lr: 0.000100, took: 83.732s
[HYBRID] Epoch 1, Batch 1470/1563, loss: 6.207, reward: 31.782, critic_reward: 31.145, revenue_rate: 0.7805, distance: 9.5704, memory: -0.0321, power: 0.2903, lr: 0.000100, took: 83.821s
[HYBRID] Epoch 1, Batch 1480/1563, loss: 3.238, reward: 33.801, critic_reward: 34.250, revenue_rate: 0.8352, distance: 10.4844, memory: -0.0004, power: 0.3189, lr: 0.000100, took: 91.165s
[HYBRID] Epoch 1, Batch 1490/1563, loss: 2.911, reward: 30.954, critic_reward: 31.351, revenue_rate: 0.7587, distance: 9.1296, memory: -0.0478, power: 0.2804, lr: 0.000100, took: 77.923s
[HYBRID] Epoch 1, Batch 1500/1563, loss: 3.784, reward: 30.714, critic_reward: 31.003, revenue_rate: 0.7547, distance: 9.0932, memory: -0.0592, power: 0.2754, lr: 0.000100, took: 79.014s
[HYBRID] Epoch 1, Batch 1510/1563, loss: 3.266, reward: 33.021, critic_reward: 33.014, revenue_rate: 0.8128, distance: 10.1261, memory: -0.0202, power: 0.3066, lr: 0.000100, took: 87.442s
[HYBRID] Epoch 1, Batch 1520/1563, loss: 3.175, reward: 32.502, critic_reward: 32.711, revenue_rate: 0.8025, distance: 9.9160, memory: -0.0288, power: 0.2984, lr: 0.000100, took: 87.291s
[HYBRID] Epoch 1, Batch 1530/1563, loss: 3.652, reward: 32.594, critic_reward: 32.025, revenue_rate: 0.7977, distance: 9.8263, memory: -0.0316, power: 0.2961, lr: 0.000100, took: 84.551s
[HYBRID] Epoch 1, Batch 1540/1563, loss: 2.949, reward: 32.495, critic_reward: 32.329, revenue_rate: 0.7994, distance: 9.8805, memory: -0.0278, power: 0.3000, lr: 0.000100, took: 82.166s
[HYBRID] Epoch 1, Batch 1550/1563, loss: 4.951, reward: 30.997, critic_reward: 31.173, revenue_rate: 0.7572, distance: 9.1377, memory: -0.0413, power: 0.2776, lr: 0.000100, took: 75.968s
[HYBRID] Epoch 1, Batch 1560/1563, loss: 2.433, reward: 30.915, critic_reward: 31.166, revenue_rate: 0.7569, distance: 9.1339, memory: -0.0498, power: 0.2793, lr: 0.000100, took: 76.002s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 30.448, revenue_rate: 0.7480, distance: 9.1702, memory: -0.0398, power: 0.2781
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24 (验证集奖励: 30.4475)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/1563, loss: 9.858, reward: 31.022, critic_reward: 29.949, revenue_rate: 0.7595, distance: 9.3419, memory: -0.0399, power: 0.2825, lr: 0.000100, took: 83.857s
[HYBRID] Epoch 2, Batch 20/1563, loss: 15.071, reward: 30.874, critic_reward: 29.972, revenue_rate: 0.7573, distance: 9.1806, memory: -0.0535, power: 0.2793, lr: 0.000100, took: 80.513s
[HYBRID] Epoch 2, Batch 30/1563, loss: 7.222, reward: 31.279, critic_reward: 31.270, revenue_rate: 0.7656, distance: 9.2951, memory: -0.0419, power: 0.2827, lr: 0.000100, took: 76.303s
[HYBRID] Epoch 2, Batch 40/1563, loss: 6.166, reward: 31.284, critic_reward: 29.743, revenue_rate: 0.7664, distance: 9.2457, memory: -0.0435, power: 0.2821, lr: 0.000100, took: 76.400s
[HYBRID] Epoch 2, Batch 50/1563, loss: 3.013, reward: 34.014, critic_reward: 33.755, revenue_rate: 0.8367, distance: 10.4029, memory: -0.0136, power: 0.3162, lr: 0.000100, took: 88.578s
[HYBRID] Epoch 2, Batch 60/1563, loss: 6.049, reward: 34.386, critic_reward: 34.008, revenue_rate: 0.8488, distance: 10.6543, memory: -0.0142, power: 0.3227, lr: 0.000100, took: 89.680s
[HYBRID] Epoch 2, Batch 70/1563, loss: 2.593, reward: 33.077, critic_reward: 33.579, revenue_rate: 0.8099, distance: 10.0203, memory: -0.0246, power: 0.3040, lr: 0.000100, took: 84.037s
[HYBRID] Epoch 2, Batch 80/1563, loss: 7.511, reward: 31.548, critic_reward: 30.217, revenue_rate: 0.7750, distance: 9.3963, memory: -0.0459, power: 0.2849, lr: 0.000100, took: 78.526s
[HYBRID] Epoch 2, Batch 90/1563, loss: 3.044, reward: 31.066, critic_reward: 31.234, revenue_rate: 0.7606, distance: 9.2350, memory: -0.0452, power: 0.2801, lr: 0.000100, took: 77.200s
[HYBRID] Epoch 2, Batch 100/1563, loss: 5.542, reward: 30.549, critic_reward: 29.516, revenue_rate: 0.7519, distance: 9.1725, memory: -0.0339, power: 0.2783, lr: 0.000100, took: 75.821s
[HYBRID] Epoch 2, Batch 110/1563, loss: 5.684, reward: 31.101, critic_reward: 31.943, revenue_rate: 0.7640, distance: 9.3787, memory: -0.0384, power: 0.2847, lr: 0.000100, took: 79.540s
[HYBRID] Epoch 2, Batch 120/1563, loss: 17.909, reward: 32.349, critic_reward: 29.828, revenue_rate: 0.7945, distance: 9.7016, memory: -0.0463, power: 0.2963, lr: 0.000100, took: 81.402s
[HYBRID] Epoch 2, Batch 130/1563, loss: 5.052, reward: 32.254, critic_reward: 33.026, revenue_rate: 0.7916, distance: 9.6337, memory: -0.0323, power: 0.2930, lr: 0.000100, took: 81.587s
[HYBRID] Epoch 2, Batch 140/1563, loss: 4.475, reward: 32.130, critic_reward: 32.157, revenue_rate: 0.7895, distance: 9.7200, memory: -0.0383, power: 0.2946, lr: 0.000100, took: 80.413s
[HYBRID] Epoch 2, Batch 150/1563, loss: 5.481, reward: 32.769, critic_reward: 33.033, revenue_rate: 0.8037, distance: 9.9782, memory: -0.0305, power: 0.3017, lr: 0.000100, took: 84.245s
[HYBRID] Epoch 2, Batch 160/1563, loss: 3.289, reward: 31.394, critic_reward: 30.889, revenue_rate: 0.7714, distance: 9.4164, memory: -0.0383, power: 0.2852, lr: 0.000100, took: 78.636s
[HYBRID] Epoch 2, Batch 170/1563, loss: 2.631, reward: 31.574, critic_reward: 31.759, revenue_rate: 0.7746, distance: 9.4856, memory: -0.0423, power: 0.2871, lr: 0.000100, took: 78.598s
[HYBRID] Epoch 2, Batch 180/1563, loss: 3.300, reward: 32.432, critic_reward: 32.752, revenue_rate: 0.7935, distance: 9.6763, memory: -0.0294, power: 0.2940, lr: 0.000100, took: 81.148s
[HYBRID] Epoch 2, Batch 190/1563, loss: 3.192, reward: 31.595, critic_reward: 31.887, revenue_rate: 0.7737, distance: 9.4433, memory: -0.0356, power: 0.2860, lr: 0.000100, took: 78.086s
[HYBRID] Epoch 2, Batch 200/1563, loss: 6.578, reward: 33.043, critic_reward: 34.580, revenue_rate: 0.8091, distance: 9.9331, memory: -0.0241, power: 0.3006, lr: 0.000100, took: 84.476s
[HYBRID] Epoch 2, Batch 210/1563, loss: 3.823, reward: 33.402, critic_reward: 33.231, revenue_rate: 0.8200, distance: 10.0931, memory: -0.0240, power: 0.3072, lr: 0.000100, took: 85.214s
[HYBRID] Epoch 2, Batch 220/1563, loss: 3.653, reward: 31.062, critic_reward: 31.003, revenue_rate: 0.7606, distance: 9.3011, memory: -0.0446, power: 0.2809, lr: 0.000100, took: 79.324s
[HYBRID] Epoch 2, Batch 230/1563, loss: 3.386, reward: 29.458, critic_reward: 29.183, revenue_rate: 0.7202, distance: 8.7104, memory: -0.0556, power: 0.2647, lr: 0.000100, took: 71.547s
[HYBRID] Epoch 2, Batch 240/1563, loss: 3.492, reward: 29.863, critic_reward: 29.891, revenue_rate: 0.7311, distance: 8.7590, memory: -0.0637, power: 0.2651, lr: 0.000100, took: 72.377s
[HYBRID] Epoch 2, Batch 250/1563, loss: 4.875, reward: 30.571, critic_reward: 30.489, revenue_rate: 0.7454, distance: 8.9990, memory: -0.0568, power: 0.2718, lr: 0.000100, took: 76.262s
[HYBRID] Epoch 2, Batch 260/1563, loss: 2.933, reward: 32.682, critic_reward: 32.671, revenue_rate: 0.8015, distance: 9.8499, memory: -0.0213, power: 0.2996, lr: 0.000100, took: 82.953s
[HYBRID] Epoch 2, Batch 270/1563, loss: 7.342, reward: 33.583, critic_reward: 33.808, revenue_rate: 0.8271, distance: 10.2843, memory: -0.0081, power: 0.3125, lr: 0.000100, took: 87.006s
[HYBRID] Epoch 2, Batch 280/1563, loss: 13.683, reward: 30.898, critic_reward: 32.593, revenue_rate: 0.7546, distance: 9.1114, memory: -0.0492, power: 0.2775, lr: 0.000100, took: 75.445s
[HYBRID] Epoch 2, Batch 290/1563, loss: 11.291, reward: 31.574, critic_reward: 29.696, revenue_rate: 0.7758, distance: 9.4029, memory: -0.0326, power: 0.2869, lr: 0.000100, took: 78.867s
[HYBRID] Epoch 2, Batch 300/1563, loss: 17.037, reward: 31.143, critic_reward: 34.510, revenue_rate: 0.7634, distance: 9.2597, memory: -0.0377, power: 0.2816, lr: 0.000100, took: 77.605s
[HYBRID] Epoch 2, Batch 310/1563, loss: 7.413, reward: 31.312, critic_reward: 30.445, revenue_rate: 0.7650, distance: 9.2061, memory: -0.0553, power: 0.2816, lr: 0.000100, took: 77.900s
[HYBRID] Epoch 2, Batch 320/1563, loss: 11.620, reward: 29.983, critic_reward: 31.715, revenue_rate: 0.7339, distance: 8.8108, memory: -0.0525, power: 0.2676, lr: 0.000100, took: 74.988s
[HYBRID] Epoch 2, Batch 330/1563, loss: 4.222, reward: 28.709, critic_reward: 28.820, revenue_rate: 0.7025, distance: 8.4330, memory: -0.0688, power: 0.2559, lr: 0.000100, took: 70.769s
[HYBRID] Epoch 2, Batch 340/1563, loss: 21.322, reward: 31.144, critic_reward: 27.062, revenue_rate: 0.7633, distance: 9.3263, memory: -0.0407, power: 0.2832, lr: 0.000100, took: 82.608s
[HYBRID] Epoch 2, Batch 350/1563, loss: 5.215, reward: 33.534, critic_reward: 34.968, revenue_rate: 0.8209, distance: 10.1506, memory: -0.0123, power: 0.3086, lr: 0.000100, took: 86.695s
[HYBRID] Epoch 2, Batch 360/1563, loss: 7.353, reward: 34.569, critic_reward: 33.624, revenue_rate: 0.8536, distance: 10.7744, memory: -0.0072, power: 0.3239, lr: 0.000100, took: 93.697s
[HYBRID] Epoch 2, Batch 370/1563, loss: 3.282, reward: 34.298, critic_reward: 34.283, revenue_rate: 0.8445, distance: 10.5980, memory: -0.0063, power: 0.3216, lr: 0.000100, took: 90.847s
[HYBRID] Epoch 2, Batch 380/1563, loss: 3.148, reward: 33.737, critic_reward: 33.798, revenue_rate: 0.8311, distance: 10.3860, memory: -0.0140, power: 0.3152, lr: 0.000100, took: 86.716s
[HYBRID] Epoch 2, Batch 390/1563, loss: 4.598, reward: 33.274, critic_reward: 32.407, revenue_rate: 0.8148, distance: 10.0357, memory: -0.0227, power: 0.3063, lr: 0.000100, took: 84.734s
[HYBRID] Epoch 2, Batch 400/1563, loss: 6.497, reward: 33.705, critic_reward: 34.924, revenue_rate: 0.8302, distance: 10.4280, memory: -0.0075, power: 0.3163, lr: 0.000100, took: 87.204s
[HYBRID] Epoch 2, Batch 410/1563, loss: 7.938, reward: 34.029, critic_reward: 33.050, revenue_rate: 0.8364, distance: 10.5408, memory: -0.0038, power: 0.3203, lr: 0.000100, took: 88.962s
[HYBRID] Epoch 2, Batch 420/1563, loss: 3.814, reward: 34.221, critic_reward: 34.200, revenue_rate: 0.8437, distance: 10.5604, memory: -0.0147, power: 0.3190, lr: 0.000100, took: 88.188s
[HYBRID] Epoch 2, Batch 430/1563, loss: 6.503, reward: 32.664, critic_reward: 32.797, revenue_rate: 0.8001, distance: 9.7993, memory: -0.0258, power: 0.2962, lr: 0.000100, took: 81.904s
[HYBRID] Epoch 2, Batch 440/1563, loss: 3.122, reward: 30.119, critic_reward: 30.401, revenue_rate: 0.7344, distance: 8.7480, memory: -0.0561, power: 0.2666, lr: 0.000100, took: 74.743s
[HYBRID] Epoch 2, Batch 450/1563, loss: 2.766, reward: 30.578, critic_reward: 30.596, revenue_rate: 0.7504, distance: 9.0751, memory: -0.0434, power: 0.2748, lr: 0.000100, took: 74.023s
[HYBRID] Epoch 2, Batch 460/1563, loss: 5.468, reward: 32.407, critic_reward: 31.796, revenue_rate: 0.7936, distance: 9.6742, memory: -0.0178, power: 0.2959, lr: 0.000100, took: 80.985s
[HYBRID] Epoch 2, Batch 470/1563, loss: 5.741, reward: 32.403, critic_reward: 31.860, revenue_rate: 0.7945, distance: 9.7676, memory: -0.0247, power: 0.2965, lr: 0.000100, took: 82.992s
[HYBRID] Epoch 2, Batch 480/1563, loss: 2.845, reward: 32.732, critic_reward: 33.444, revenue_rate: 0.8054, distance: 9.8584, memory: -0.0212, power: 0.3002, lr: 0.000100, took: 81.914s
[HYBRID] Epoch 2, Batch 490/1563, loss: 5.617, reward: 33.394, critic_reward: 32.204, revenue_rate: 0.8168, distance: 10.0697, memory: -0.0260, power: 0.3071, lr: 0.000100, took: 84.408s
[HYBRID] Epoch 2, Batch 500/1563, loss: 3.165, reward: 33.807, critic_reward: 33.407, revenue_rate: 0.8316, distance: 10.3740, memory: -0.0104, power: 0.3131, lr: 0.000100, took: 86.653s
[HYBRID] Epoch 2, Batch 510/1563, loss: 3.589, reward: 33.578, critic_reward: 34.243, revenue_rate: 0.8244, distance: 10.1890, memory: -0.0098, power: 0.3102, lr: 0.000100, took: 85.534s
[HYBRID] Epoch 2, Batch 520/1563, loss: 4.885, reward: 32.072, critic_reward: 32.773, revenue_rate: 0.7869, distance: 9.5972, memory: -0.0359, power: 0.2916, lr: 0.000100, took: 79.517s
[HYBRID] Epoch 2, Batch 530/1563, loss: 2.751, reward: 31.293, critic_reward: 30.830, revenue_rate: 0.7627, distance: 9.1076, memory: -0.0454, power: 0.2768, lr: 0.000100, took: 75.401s
[HYBRID] Epoch 2, Batch 540/1563, loss: 2.604, reward: 32.015, critic_reward: 32.051, revenue_rate: 0.7842, distance: 9.4820, memory: -0.0445, power: 0.2867, lr: 0.000100, took: 78.384s
[HYBRID] Epoch 2, Batch 550/1563, loss: 4.098, reward: 32.432, critic_reward: 33.057, revenue_rate: 0.7969, distance: 9.7129, memory: -0.0253, power: 0.2958, lr: 0.000100, took: 82.216s
[HYBRID] Epoch 2, Batch 560/1563, loss: 2.448, reward: 32.677, critic_reward: 32.592, revenue_rate: 0.7999, distance: 9.8426, memory: -0.0219, power: 0.2973, lr: 0.000100, took: 81.547s
[HYBRID] Epoch 2, Batch 570/1563, loss: 2.446, reward: 31.520, critic_reward: 31.922, revenue_rate: 0.7738, distance: 9.4466, memory: -0.0402, power: 0.2837, lr: 0.000100, took: 77.122s
[HYBRID] Epoch 2, Batch 580/1563, loss: 3.818, reward: 30.079, critic_reward: 29.655, revenue_rate: 0.7377, distance: 8.8652, memory: -0.0576, power: 0.2690, lr: 0.000100, took: 74.756s
[HYBRID] Epoch 2, Batch 590/1563, loss: 3.761, reward: 29.459, critic_reward: 29.581, revenue_rate: 0.7210, distance: 8.6277, memory: -0.0631, power: 0.2627, lr: 0.000100, took: 70.942s
[HYBRID] Epoch 2, Batch 600/1563, loss: 5.398, reward: 31.514, critic_reward: 30.766, revenue_rate: 0.7732, distance: 9.3929, memory: -0.0458, power: 0.2860, lr: 0.000100, took: 77.671s
[HYBRID] Epoch 2, Batch 610/1563, loss: 5.997, reward: 34.021, critic_reward: 34.717, revenue_rate: 0.8394, distance: 10.5135, memory: 0.0027, power: 0.3185, lr: 0.000100, took: 87.867s
[HYBRID] Epoch 2, Batch 620/1563, loss: 3.255, reward: 34.187, critic_reward: 34.210, revenue_rate: 0.8403, distance: 10.5339, memory: -0.0009, power: 0.3214, lr: 0.000100, took: 88.889s
[HYBRID] Epoch 2, Batch 630/1563, loss: 3.777, reward: 32.960, critic_reward: 33.023, revenue_rate: 0.8067, distance: 9.9037, memory: -0.0225, power: 0.3013, lr: 0.000100, took: 82.804s
[HYBRID] Epoch 2, Batch 640/1563, loss: 4.916, reward: 31.665, critic_reward: 32.332, revenue_rate: 0.7753, distance: 9.4338, memory: -0.0412, power: 0.2857, lr: 0.000100, took: 77.766s
[HYBRID] Epoch 2, Batch 650/1563, loss: 4.038, reward: 31.524, critic_reward: 30.765, revenue_rate: 0.7697, distance: 9.3341, memory: -0.0459, power: 0.2833, lr: 0.000100, took: 76.906s
[HYBRID] Epoch 2, Batch 660/1563, loss: 4.408, reward: 32.626, critic_reward: 32.953, revenue_rate: 0.8001, distance: 9.7401, memory: -0.0282, power: 0.2971, lr: 0.000100, took: 81.373s
[HYBRID] Epoch 2, Batch 670/1563, loss: 2.886, reward: 31.944, critic_reward: 31.406, revenue_rate: 0.7853, distance: 9.6448, memory: -0.0261, power: 0.2908, lr: 0.000100, took: 82.374s
[HYBRID] Epoch 2, Batch 680/1563, loss: 5.048, reward: 31.353, critic_reward: 32.649, revenue_rate: 0.7663, distance: 9.2061, memory: -0.0487, power: 0.2842, lr: 0.000100, took: 76.980s
[HYBRID] Epoch 2, Batch 690/1563, loss: 3.557, reward: 30.304, critic_reward: 29.822, revenue_rate: 0.7405, distance: 8.8712, memory: -0.0464, power: 0.2705, lr: 0.000100, took: 75.195s
[HYBRID] Epoch 2, Batch 700/1563, loss: 3.050, reward: 31.277, critic_reward: 30.531, revenue_rate: 0.7636, distance: 9.1598, memory: -0.0511, power: 0.2780, lr: 0.000100, took: 75.986s
[HYBRID] Epoch 2, Batch 710/1563, loss: 6.160, reward: 32.490, critic_reward: 32.691, revenue_rate: 0.7983, distance: 9.7282, memory: -0.0363, power: 0.2950, lr: 0.000100, took: 80.853s
[HYBRID] Epoch 2, Batch 720/1563, loss: 3.947, reward: 32.263, critic_reward: 32.373, revenue_rate: 0.7915, distance: 9.7050, memory: -0.0302, power: 0.2953, lr: 0.000100, took: 80.094s
[HYBRID] Epoch 2, Batch 730/1563, loss: 3.981, reward: 32.351, critic_reward: 31.351, revenue_rate: 0.7930, distance: 9.5966, memory: -0.0282, power: 0.2931, lr: 0.000100, took: 80.555s
[HYBRID] Epoch 2, Batch 740/1563, loss: 2.806, reward: 33.232, critic_reward: 33.143, revenue_rate: 0.8186, distance: 10.1987, memory: -0.0053, power: 0.3103, lr: 0.000100, took: 85.422s
[HYBRID] Epoch 2, Batch 750/1563, loss: 2.677, reward: 32.513, critic_reward: 32.567, revenue_rate: 0.7989, distance: 9.8111, memory: -0.0210, power: 0.2973, lr: 0.000100, took: 82.097s
[HYBRID] Epoch 2, Batch 760/1563, loss: 5.201, reward: 32.453, critic_reward: 31.779, revenue_rate: 0.7981, distance: 9.8281, memory: -0.0177, power: 0.2965, lr: 0.000100, took: 81.423s
[HYBRID] Epoch 2, Batch 770/1563, loss: 8.575, reward: 32.574, critic_reward: 32.321, revenue_rate: 0.8007, distance: 9.8109, memory: -0.0257, power: 0.2992, lr: 0.000100, took: 81.989s
[HYBRID] Epoch 2, Batch 780/1563, loss: 11.338, reward: 32.867, critic_reward: 32.959, revenue_rate: 0.8100, distance: 9.9892, memory: -0.0217, power: 0.3033, lr: 0.000100, took: 85.718s
[HYBRID] Epoch 2, Batch 790/1563, loss: 6.568, reward: 31.060, critic_reward: 31.238, revenue_rate: 0.7588, distance: 9.1218, memory: -0.0474, power: 0.2760, lr: 0.000100, took: 75.168s
[HYBRID] Epoch 2, Batch 800/1563, loss: 4.281, reward: 30.658, critic_reward: 30.875, revenue_rate: 0.7483, distance: 8.9031, memory: -0.0529, power: 0.2728, lr: 0.000100, took: 76.234s
[HYBRID] Epoch 2, Batch 810/1563, loss: 2.751, reward: 29.518, critic_reward: 29.327, revenue_rate: 0.7203, distance: 8.5264, memory: -0.0666, power: 0.2613, lr: 0.000100, took: 69.709s
[HYBRID] Epoch 2, Batch 820/1563, loss: 5.003, reward: 30.991, critic_reward: 31.780, revenue_rate: 0.7591, distance: 9.1048, memory: -0.0527, power: 0.2764, lr: 0.000100, took: 74.619s
[HYBRID] Epoch 2, Batch 830/1563, loss: 4.101, reward: 30.409, critic_reward: 30.911, revenue_rate: 0.7438, distance: 8.9485, memory: -0.0526, power: 0.2723, lr: 0.000100, took: 73.335s
[HYBRID] Epoch 2, Batch 840/1563, loss: 4.734, reward: 29.140, critic_reward: 28.862, revenue_rate: 0.7129, distance: 8.4736, memory: -0.0603, power: 0.2579, lr: 0.000100, took: 69.378s
[HYBRID] Epoch 2, Batch 850/1563, loss: 2.190, reward: 29.992, critic_reward: 29.836, revenue_rate: 0.7308, distance: 8.7510, memory: -0.0566, power: 0.2644, lr: 0.000100, took: 71.206s
[HYBRID] Epoch 2, Batch 860/1563, loss: 3.666, reward: 31.602, critic_reward: 32.004, revenue_rate: 0.7752, distance: 9.3388, memory: -0.0452, power: 0.2835, lr: 0.000100, took: 76.983s
[HYBRID] Epoch 2, Batch 870/1563, loss: 4.110, reward: 31.784, critic_reward: 31.037, revenue_rate: 0.7781, distance: 9.3522, memory: -0.0459, power: 0.2861, lr: 0.000100, took: 77.582s
[HYBRID] Epoch 2, Batch 880/1563, loss: 3.554, reward: 33.056, critic_reward: 33.005, revenue_rate: 0.8150, distance: 9.9834, memory: -0.0229, power: 0.3030, lr: 0.000100, took: 83.424s
[HYBRID] Epoch 2, Batch 890/1563, loss: 5.941, reward: 34.264, critic_reward: 33.984, revenue_rate: 0.8453, distance: 10.5286, memory: -0.0114, power: 0.3189, lr: 0.000100, took: 88.439s
[HYBRID] Epoch 2, Batch 900/1563, loss: 3.586, reward: 33.815, critic_reward: 33.732, revenue_rate: 0.8319, distance: 10.3636, memory: -0.0115, power: 0.3129, lr: 0.000100, took: 88.845s
[HYBRID] Epoch 2, Batch 910/1563, loss: 3.964, reward: 31.677, critic_reward: 32.000, revenue_rate: 0.7765, distance: 9.4336, memory: -0.0372, power: 0.2836, lr: 0.000100, took: 78.200s
[HYBRID] Epoch 2, Batch 920/1563, loss: 2.657, reward: 32.579, critic_reward: 32.595, revenue_rate: 0.7997, distance: 9.8641, memory: -0.0358, power: 0.2970, lr: 0.000100, took: 83.865s
[HYBRID] Epoch 2, Batch 930/1563, loss: 4.545, reward: 33.011, critic_reward: 33.682, revenue_rate: 0.8117, distance: 10.0290, memory: -0.0197, power: 0.3049, lr: 0.000100, took: 84.302s
[HYBRID] Epoch 2, Batch 940/1563, loss: 5.997, reward: 31.900, critic_reward: 30.763, revenue_rate: 0.7850, distance: 9.7941, memory: -0.0252, power: 0.2950, lr: 0.000100, took: 81.118s
[HYBRID] Epoch 2, Batch 950/1563, loss: 3.502, reward: 32.944, critic_reward: 32.585, revenue_rate: 0.8109, distance: 10.0730, memory: -0.0202, power: 0.3049, lr: 0.000100, took: 83.664s
[HYBRID] Epoch 2, Batch 960/1563, loss: 9.963, reward: 34.313, critic_reward: 36.332, revenue_rate: 0.8446, distance: 10.5050, memory: -0.0111, power: 0.3223, lr: 0.000100, took: 88.195s
[HYBRID] Epoch 2, Batch 970/1563, loss: 3.381, reward: 33.152, critic_reward: 32.760, revenue_rate: 0.8140, distance: 10.0223, memory: -0.0208, power: 0.3026, lr: 0.000100, took: 83.695s
[HYBRID] Epoch 2, Batch 980/1563, loss: 5.329, reward: 30.785, critic_reward: 31.993, revenue_rate: 0.7513, distance: 9.0083, memory: -0.0472, power: 0.2738, lr: 0.000100, took: 73.955s
[HYBRID] Epoch 2, Batch 990/1563, loss: 3.660, reward: 30.593, critic_reward: 30.547, revenue_rate: 0.7473, distance: 8.9373, memory: -0.0522, power: 0.2728, lr: 0.000100, took: 73.539s
[HYBRID] Epoch 2, Batch 1000/1563, loss: 6.445, reward: 29.783, critic_reward: 28.056, revenue_rate: 0.7265, distance: 8.5879, memory: -0.0629, power: 0.2633, lr: 0.000100, took: 70.602s
[HYBRID] Epoch 2, Batch 1010/1563, loss: 6.884, reward: 29.656, critic_reward: 31.487, revenue_rate: 0.7234, distance: 8.6216, memory: -0.0583, power: 0.2612, lr: 0.000100, took: 72.554s
[HYBRID] Epoch 2, Batch 1020/1563, loss: 3.954, reward: 30.039, critic_reward: 30.018, revenue_rate: 0.7336, distance: 8.7700, memory: -0.0559, power: 0.2652, lr: 0.000100, took: 71.976s
[HYBRID] Epoch 2, Batch 1030/1563, loss: 4.478, reward: 30.597, critic_reward: 30.366, revenue_rate: 0.7472, distance: 8.8610, memory: -0.0648, power: 0.2713, lr: 0.000100, took: 75.198s
[HYBRID] Epoch 2, Batch 1040/1563, loss: 3.549, reward: 28.923, critic_reward: 28.791, revenue_rate: 0.7058, distance: 8.3628, memory: -0.0661, power: 0.2555, lr: 0.000100, took: 68.999s
[HYBRID] Epoch 2, Batch 1050/1563, loss: 3.413, reward: 29.800, critic_reward: 30.535, revenue_rate: 0.7268, distance: 8.6125, memory: -0.0561, power: 0.2652, lr: 0.000100, took: 71.148s
[HYBRID] Epoch 2, Batch 1060/1563, loss: 2.267, reward: 31.000, critic_reward: 31.068, revenue_rate: 0.7601, distance: 9.0655, memory: -0.0435, power: 0.2786, lr: 0.000100, took: 75.195s
[HYBRID] Epoch 2, Batch 1070/1563, loss: 4.844, reward: 31.428, critic_reward: 31.523, revenue_rate: 0.7694, distance: 9.3061, memory: -0.0413, power: 0.2826, lr: 0.000100, took: 77.199s
[HYBRID] Epoch 2, Batch 1080/1563, loss: 2.683, reward: 31.342, critic_reward: 31.386, revenue_rate: 0.7691, distance: 9.2448, memory: -0.0446, power: 0.2806, lr: 0.000100, took: 75.973s
[HYBRID] Epoch 2, Batch 1090/1563, loss: 3.968, reward: 30.741, critic_reward: 31.592, revenue_rate: 0.7509, distance: 8.9904, memory: -0.0495, power: 0.2734, lr: 0.000100, took: 73.533s
[HYBRID] Epoch 2, Batch 1100/1563, loss: 4.973, reward: 30.795, critic_reward: 29.873, revenue_rate: 0.7540, distance: 9.0816, memory: -0.0515, power: 0.2770, lr: 0.000100, took: 74.805s
[HYBRID] Epoch 2, Batch 1110/1563, loss: 3.477, reward: 32.846, critic_reward: 32.991, revenue_rate: 0.8069, distance: 9.9448, memory: -0.0133, power: 0.3024, lr: 0.000100, took: 83.434s
[HYBRID] Epoch 2, Batch 1120/1563, loss: 2.403, reward: 34.258, critic_reward: 34.132, revenue_rate: 0.8421, distance: 10.5006, memory: -0.0107, power: 0.3192, lr: 0.000100, took: 88.383s
[HYBRID] Epoch 2, Batch 1130/1563, loss: 3.447, reward: 32.750, critic_reward: 32.777, revenue_rate: 0.8051, distance: 9.8712, memory: -0.0251, power: 0.2983, lr: 0.000100, took: 83.934s
[HYBRID] Epoch 2, Batch 1140/1563, loss: 4.131, reward: 31.640, critic_reward: 31.096, revenue_rate: 0.7748, distance: 9.3515, memory: -0.0458, power: 0.2829, lr: 0.000100, took: 76.974s
[HYBRID] Epoch 2, Batch 1150/1563, loss: 3.977, reward: 30.743, critic_reward: 31.596, revenue_rate: 0.7553, distance: 9.1344, memory: -0.0415, power: 0.2756, lr: 0.000100, took: 76.935s
[HYBRID] Epoch 2, Batch 1160/1563, loss: 3.036, reward: 32.030, critic_reward: 31.899, revenue_rate: 0.7850, distance: 9.5140, memory: -0.0333, power: 0.2891, lr: 0.000100, took: 79.052s
[HYBRID] Epoch 2, Batch 1170/1563, loss: 2.454, reward: 32.566, critic_reward: 32.398, revenue_rate: 0.8008, distance: 9.8197, memory: -0.0187, power: 0.2970, lr: 0.000100, took: 81.748s
[HYBRID] Epoch 2, Batch 1180/1563, loss: 2.898, reward: 32.670, critic_reward: 32.941, revenue_rate: 0.8018, distance: 9.8288, memory: -0.0219, power: 0.2982, lr: 0.000100, took: 81.977s
[HYBRID] Epoch 2, Batch 1190/1563, loss: 2.765, reward: 31.802, critic_reward: 31.403, revenue_rate: 0.7800, distance: 9.4395, memory: -0.0324, power: 0.2878, lr: 0.000100, took: 78.176s
[HYBRID] Epoch 2, Batch 1200/1563, loss: 3.830, reward: 33.639, critic_reward: 33.792, revenue_rate: 0.8256, distance: 10.1993, memory: -0.0182, power: 0.3085, lr: 0.000100, took: 85.615s
[HYBRID] Epoch 2, Batch 1210/1563, loss: 3.593, reward: 33.276, critic_reward: 33.172, revenue_rate: 0.8190, distance: 10.1661, memory: -0.0179, power: 0.3086, lr: 0.000100, took: 84.534s
[HYBRID] Epoch 2, Batch 1220/1563, loss: 3.257, reward: 31.099, critic_reward: 31.476, revenue_rate: 0.7635, distance: 9.2379, memory: -0.0460, power: 0.2828, lr: 0.000100, took: 76.543s
[HYBRID] Epoch 2, Batch 1230/1563, loss: 4.672, reward: 29.545, critic_reward: 29.816, revenue_rate: 0.7220, distance: 8.6064, memory: -0.0770, power: 0.2618, lr: 0.000100, took: 70.734s
[HYBRID] Epoch 2, Batch 1240/1563, loss: 4.362, reward: 30.838, critic_reward: 29.793, revenue_rate: 0.7544, distance: 9.1781, memory: -0.0420, power: 0.2792, lr: 0.000100, took: 78.822s
[HYBRID] Epoch 2, Batch 1250/1563, loss: 7.659, reward: 33.843, critic_reward: 35.319, revenue_rate: 0.8321, distance: 10.3382, memory: -0.0144, power: 0.3152, lr: 0.000100, took: 86.840s
[HYBRID] Epoch 2, Batch 1260/1563, loss: 3.272, reward: 34.521, critic_reward: 34.019, revenue_rate: 0.8533, distance: 10.9116, memory: 0.0130, power: 0.3304, lr: 0.000100, took: 93.563s
[HYBRID] Epoch 2, Batch 1270/1563, loss: 2.234, reward: 34.403, critic_reward: 34.465, revenue_rate: 0.8493, distance: 10.6497, memory: 0.0006, power: 0.3217, lr: 0.000100, took: 89.795s
[HYBRID] Epoch 2, Batch 1280/1563, loss: 4.462, reward: 31.856, critic_reward: 32.531, revenue_rate: 0.7819, distance: 9.5114, memory: -0.0351, power: 0.2873, lr: 0.000100, took: 78.542s
[HYBRID] Epoch 2, Batch 1290/1563, loss: 4.401, reward: 29.897, critic_reward: 29.157, revenue_rate: 0.7316, distance: 8.7851, memory: -0.0649, power: 0.2656, lr: 0.000100, took: 71.698s
[HYBRID] Epoch 2, Batch 1300/1563, loss: 2.941, reward: 28.845, critic_reward: 28.475, revenue_rate: 0.7049, distance: 8.3673, memory: -0.0690, power: 0.2525, lr: 0.000100, took: 68.512s
[HYBRID] Epoch 2, Batch 1310/1563, loss: 3.423, reward: 29.320, critic_reward: 28.912, revenue_rate: 0.7140, distance: 8.4906, memory: -0.0612, power: 0.2582, lr: 0.000100, took: 68.887s
[HYBRID] Epoch 2, Batch 1320/1563, loss: 2.930, reward: 30.030, critic_reward: 29.651, revenue_rate: 0.7335, distance: 8.7523, memory: -0.0506, power: 0.2667, lr: 0.000100, took: 72.167s
[HYBRID] Epoch 2, Batch 1330/1563, loss: 3.599, reward: 30.926, critic_reward: 30.873, revenue_rate: 0.7549, distance: 8.9868, memory: -0.0583, power: 0.2758, lr: 0.000100, took: 74.616s
[HYBRID] Epoch 2, Batch 1340/1563, loss: 3.698, reward: 32.340, critic_reward: 31.661, revenue_rate: 0.7936, distance: 9.6579, memory: -0.0216, power: 0.2960, lr: 0.000100, took: 80.819s
[HYBRID] Epoch 2, Batch 1350/1563, loss: 7.366, reward: 34.728, critic_reward: 36.494, revenue_rate: 0.8631, distance: 10.7743, memory: 0.0011, power: 0.3285, lr: 0.000100, took: 93.392s
[HYBRID] Epoch 2, Batch 1360/1563, loss: 3.767, reward: 33.570, critic_reward: 33.639, revenue_rate: 0.8261, distance: 10.2859, memory: -0.0135, power: 0.3108, lr: 0.000100, took: 85.702s
[HYBRID] Epoch 2, Batch 1370/1563, loss: 2.532, reward: 32.974, critic_reward: 33.184, revenue_rate: 0.8090, distance: 9.9343, memory: -0.0195, power: 0.3007, lr: 0.000100, took: 85.126s
[HYBRID] Epoch 2, Batch 1380/1563, loss: 3.535, reward: 32.099, critic_reward: 32.269, revenue_rate: 0.7876, distance: 9.5189, memory: -0.0363, power: 0.2886, lr: 0.000100, took: 79.208s
[HYBRID] Epoch 2, Batch 1390/1563, loss: 3.103, reward: 31.657, critic_reward: 31.212, revenue_rate: 0.7725, distance: 9.2650, memory: -0.0449, power: 0.2813, lr: 0.000100, took: 77.127s
[HYBRID] Epoch 2, Batch 1400/1563, loss: 2.151, reward: 31.994, critic_reward: 32.105, revenue_rate: 0.7801, distance: 9.4208, memory: -0.0454, power: 0.2868, lr: 0.000100, took: 78.214s
[HYBRID] Epoch 2, Batch 1410/1563, loss: 4.172, reward: 32.739, critic_reward: 32.618, revenue_rate: 0.8033, distance: 9.7889, memory: -0.0321, power: 0.2967, lr: 0.000100, took: 81.394s
[HYBRID] Epoch 2, Batch 1420/1563, loss: 4.704, reward: 31.443, critic_reward: 31.555, revenue_rate: 0.7701, distance: 9.3255, memory: -0.0458, power: 0.2822, lr: 0.000100, took: 76.968s
[HYBRID] Epoch 2, Batch 1430/1563, loss: 3.007, reward: 29.270, critic_reward: 28.671, revenue_rate: 0.7141, distance: 8.4666, memory: -0.0574, power: 0.2571, lr: 0.000100, took: 68.934s
[HYBRID] Epoch 2, Batch 1440/1563, loss: 3.435, reward: 30.178, critic_reward: 30.511, revenue_rate: 0.7375, distance: 8.7664, memory: -0.0595, power: 0.2665, lr: 0.000100, took: 71.785s
[HYBRID] Epoch 2, Batch 1450/1563, loss: 3.775, reward: 32.299, critic_reward: 31.828, revenue_rate: 0.7907, distance: 9.5906, memory: -0.0323, power: 0.2896, lr: 0.000100, took: 79.616s
[HYBRID] Epoch 2, Batch 1460/1563, loss: 3.668, reward: 33.535, critic_reward: 33.824, revenue_rate: 0.8202, distance: 10.1487, memory: -0.0227, power: 0.3064, lr: 0.000100, took: 84.050s
[HYBRID] Epoch 2, Batch 1470/1563, loss: 4.491, reward: 32.728, critic_reward: 31.941, revenue_rate: 0.8018, distance: 9.7625, memory: -0.0352, power: 0.2953, lr: 0.000100, took: 82.873s
[HYBRID] Epoch 2, Batch 1480/1563, loss: 3.180, reward: 32.238, critic_reward: 32.482, revenue_rate: 0.7917, distance: 9.5952, memory: -0.0317, power: 0.2919, lr: 0.000100, took: 79.990s
[HYBRID] Epoch 2, Batch 1490/1563, loss: 4.198, reward: 33.401, critic_reward: 34.040, revenue_rate: 0.8187, distance: 10.1318, memory: -0.0158, power: 0.3072, lr: 0.000100, took: 86.762s
[HYBRID] Epoch 2, Batch 1500/1563, loss: 3.404, reward: 33.134, critic_reward: 32.218, revenue_rate: 0.8114, distance: 9.8895, memory: -0.0301, power: 0.3015, lr: 0.000100, took: 82.381s
[HYBRID] Epoch 2, Batch 1510/1563, loss: 2.879, reward: 34.697, critic_reward: 34.287, revenue_rate: 0.8560, distance: 10.6771, memory: -0.0074, power: 0.3251, lr: 0.000100, took: 89.889s
[HYBRID] Epoch 2, Batch 1520/1563, loss: 4.377, reward: 34.593, critic_reward: 35.721, revenue_rate: 0.8561, distance: 10.6645, memory: -0.0004, power: 0.3242, lr: 0.000100, took: 90.368s
[HYBRID] Epoch 2, Batch 1530/1563, loss: 3.533, reward: 33.362, critic_reward: 33.898, revenue_rate: 0.8194, distance: 10.2078, memory: -0.0205, power: 0.3046, lr: 0.000100, took: 84.447s
[HYBRID] Epoch 2, Batch 1540/1563, loss: 2.251, reward: 32.571, critic_reward: 32.668, revenue_rate: 0.8008, distance: 9.7972, memory: -0.0266, power: 0.2965, lr: 0.000100, took: 81.649s
[HYBRID] Epoch 2, Batch 1550/1563, loss: 3.062, reward: 33.024, critic_reward: 32.262, revenue_rate: 0.8088, distance: 9.9171, memory: -0.0298, power: 0.3015, lr: 0.000100, took: 82.408s
[HYBRID] Epoch 2, Batch 1560/1563, loss: 13.040, reward: 30.857, critic_reward: 33.725, revenue_rate: 0.7559, distance: 9.0592, memory: -0.0528, power: 0.2774, lr: 0.000100, took: 75.036s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 27.963, revenue_rate: 0.6811, distance: 7.9583, memory: -0.0841, power: 0.2425
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/1563, loss: 3.919, reward: 27.643, critic_reward: 27.406, revenue_rate: 0.6740, distance: 7.7932, memory: -0.0834, power: 0.2397, lr: 0.000100, took: 70.015s
[HYBRID] Epoch 3, Batch 20/1563, loss: 4.091, reward: 28.945, critic_reward: 28.256, revenue_rate: 0.7055, distance: 8.3459, memory: -0.0796, power: 0.2536, lr: 0.000100, took: 69.563s
[HYBRID] Epoch 3, Batch 30/1563, loss: 3.901, reward: 31.716, critic_reward: 32.106, revenue_rate: 0.7758, distance: 9.3473, memory: -0.0434, power: 0.2848, lr: 0.000100, took: 77.842s
[HYBRID] Epoch 3, Batch 40/1563, loss: 5.562, reward: 33.351, critic_reward: 31.993, revenue_rate: 0.8203, distance: 10.1904, memory: -0.0168, power: 0.3081, lr: 0.000100, took: 84.655s
[HYBRID] Epoch 3, Batch 50/1563, loss: 3.140, reward: 33.977, critic_reward: 34.858, revenue_rate: 0.8363, distance: 10.3370, memory: -0.0091, power: 0.3150, lr: 0.000100, took: 86.675s
[HYBRID] Epoch 3, Batch 60/1563, loss: 2.656, reward: 31.861, critic_reward: 32.216, revenue_rate: 0.7828, distance: 9.4318, memory: -0.0377, power: 0.2870, lr: 0.000100, took: 77.912s
[HYBRID] Epoch 3, Batch 70/1563, loss: 10.402, reward: 31.638, critic_reward: 28.887, revenue_rate: 0.7734, distance: 9.2915, memory: -0.0483, power: 0.2833, lr: 0.000100, took: 76.471s
[HYBRID] Epoch 3, Batch 80/1563, loss: 7.801, reward: 33.701, critic_reward: 35.727, revenue_rate: 0.8256, distance: 10.0852, memory: -0.0127, power: 0.3070, lr: 0.000100, took: 84.556s
[HYBRID] Epoch 3, Batch 90/1563, loss: 7.730, reward: 35.071, critic_reward: 33.106, revenue_rate: 0.8656, distance: 10.9455, memory: 0.0132, power: 0.3289, lr: 0.000100, took: 91.477s
[HYBRID] Epoch 3, Batch 100/1563, loss: 4.361, reward: 34.682, critic_reward: 35.632, revenue_rate: 0.8554, distance: 10.7887, memory: -0.0059, power: 0.3268, lr: 0.000100, took: 92.884s
[HYBRID] Epoch 3, Batch 110/1563, loss: 5.086, reward: 34.016, critic_reward: 33.901, revenue_rate: 0.8351, distance: 10.3496, memory: -0.0090, power: 0.3169, lr: 0.000100, took: 88.389s
[HYBRID] Epoch 3, Batch 120/1563, loss: 2.673, reward: 33.935, critic_reward: 34.355, revenue_rate: 0.8296, distance: 10.2036, memory: -0.0104, power: 0.3131, lr: 0.000100, took: 88.405s
[HYBRID] Epoch 3, Batch 130/1563, loss: 3.238, reward: 31.949, critic_reward: 32.164, revenue_rate: 0.7826, distance: 9.4447, memory: -0.0378, power: 0.2880, lr: 0.000100, took: 78.054s
[HYBRID] Epoch 3, Batch 140/1563, loss: 3.540, reward: 30.533, critic_reward: 30.824, revenue_rate: 0.7485, distance: 8.8904, memory: -0.0524, power: 0.2723, lr: 0.000100, took: 73.190s
[HYBRID] Epoch 3, Batch 150/1563, loss: 2.331, reward: 30.124, critic_reward: 30.023, revenue_rate: 0.7373, distance: 8.8034, memory: -0.0589, power: 0.2664, lr: 0.000100, took: 71.983s
[HYBRID] Epoch 3, Batch 160/1563, loss: 5.914, reward: 30.198, critic_reward: 29.835, revenue_rate: 0.7394, distance: 8.8224, memory: -0.0603, power: 0.2678, lr: 0.000100, took: 72.751s
[HYBRID] Epoch 3, Batch 170/1563, loss: 9.753, reward: 31.564, critic_reward: 29.631, revenue_rate: 0.7690, distance: 9.2146, memory: -0.0453, power: 0.2808, lr: 0.000100, took: 76.611s
[HYBRID] Epoch 3, Batch 180/1563, loss: 5.639, reward: 32.446, critic_reward: 31.551, revenue_rate: 0.7964, distance: 9.6869, memory: -0.0297, power: 0.2923, lr: 0.000100, took: 80.394s
[HYBRID] Epoch 3, Batch 190/1563, loss: 3.726, reward: 33.755, critic_reward: 33.527, revenue_rate: 0.8276, distance: 10.1705, memory: -0.0087, power: 0.3113, lr: 0.000100, took: 86.059s
[HYBRID] Epoch 3, Batch 200/1563, loss: 3.991, reward: 35.329, critic_reward: 34.834, revenue_rate: 0.8721, distance: 11.0935, memory: 0.0168, power: 0.3369, lr: 0.000100, took: 94.837s
[HYBRID] Epoch 3, Batch 210/1563, loss: 2.780, reward: 36.751, critic_reward: 37.001, revenue_rate: 0.9136, distance: 12.1122, memory: 0.0436, power: 0.3668, lr: 0.000100, took: 108.775s
[HYBRID] Epoch 3, Batch 220/1563, loss: 5.749, reward: 37.230, critic_reward: 36.581, revenue_rate: 0.9253, distance: 12.2510, memory: 0.0454, power: 0.3699, lr: 0.000100, took: 109.118s
[HYBRID] Epoch 3, Batch 230/1563, loss: 2.594, reward: 35.547, critic_reward: 35.784, revenue_rate: 0.8785, distance: 11.2350, memory: 0.0219, power: 0.3392, lr: 0.000100, took: 96.757s
[HYBRID] Epoch 3, Batch 240/1563, loss: 2.662, reward: 34.253, critic_reward: 34.118, revenue_rate: 0.8408, distance: 10.4809, memory: 0.0035, power: 0.3193, lr: 0.000100, took: 89.335s
[HYBRID] Epoch 3, Batch 250/1563, loss: 3.448, reward: 33.352, critic_reward: 32.859, revenue_rate: 0.8190, distance: 10.0381, memory: -0.0129, power: 0.3063, lr: 0.000100, took: 85.792s
[HYBRID] Epoch 3, Batch 260/1563, loss: 5.812, reward: 33.413, critic_reward: 33.899, revenue_rate: 0.8219, distance: 10.1837, memory: 0.0005, power: 0.3057, lr: 0.000100, took: 86.549s
[HYBRID] Epoch 3, Batch 270/1563, loss: 2.701, reward: 33.114, critic_reward: 32.896, revenue_rate: 0.8131, distance: 9.9458, memory: -0.0217, power: 0.3017, lr: 0.000100, took: 84.530s
[HYBRID] Epoch 3, Batch 280/1563, loss: 2.764, reward: 32.362, critic_reward: 32.312, revenue_rate: 0.7919, distance: 9.4972, memory: -0.0344, power: 0.2905, lr: 0.000100, took: 80.521s
[HYBRID] Epoch 3, Batch 290/1563, loss: 3.513, reward: 30.547, critic_reward: 31.049, revenue_rate: 0.7434, distance: 8.7966, memory: -0.0621, power: 0.2685, lr: 0.000100, took: 73.767s
[HYBRID] Epoch 3, Batch 300/1563, loss: 3.791, reward: 29.419, critic_reward: 29.615, revenue_rate: 0.7180, distance: 8.5132, memory: -0.0719, power: 0.2563, lr: 0.000100, took: 70.592s
[HYBRID] Epoch 3, Batch 310/1563, loss: 3.679, reward: 30.352, critic_reward: 29.964, revenue_rate: 0.7423, distance: 8.8379, memory: -0.0535, power: 0.2685, lr: 0.000100, took: 75.780s
[HYBRID] Epoch 3, Batch 320/1563, loss: 2.582, reward: 32.095, critic_reward: 31.502, revenue_rate: 0.7876, distance: 9.5192, memory: -0.0370, power: 0.2875, lr: 0.000100, took: 79.905s
[HYBRID] Epoch 3, Batch 330/1563, loss: 2.910, reward: 32.579, critic_reward: 32.517, revenue_rate: 0.7983, distance: 9.7043, memory: -0.0273, power: 0.2948, lr: 0.000100, took: 83.968s
[HYBRID] Epoch 3, Batch 340/1563, loss: 3.607, reward: 32.928, critic_reward: 33.578, revenue_rate: 0.8066, distance: 9.9097, memory: -0.0226, power: 0.2994, lr: 0.000100, took: 84.078s
[HYBRID] Epoch 3, Batch 350/1563, loss: 4.923, reward: 32.861, critic_reward: 32.111, revenue_rate: 0.8082, distance: 9.8451, memory: -0.0240, power: 0.2980, lr: 0.000100, took: 83.873s
[HYBRID] Epoch 3, Batch 360/1563, loss: 3.522, reward: 33.441, critic_reward: 33.056, revenue_rate: 0.8239, distance: 10.2896, memory: -0.0136, power: 0.3118, lr: 0.000100, took: 87.556s
[HYBRID] Epoch 3, Batch 370/1563, loss: 4.174, reward: 33.892, critic_reward: 34.275, revenue_rate: 0.8305, distance: 10.2836, memory: -0.0162, power: 0.3119, lr: 0.000100, took: 87.978s
[HYBRID] Epoch 3, Batch 380/1563, loss: 4.076, reward: 32.803, critic_reward: 32.028, revenue_rate: 0.8074, distance: 9.9194, memory: -0.0284, power: 0.3006, lr: 0.000100, took: 84.105s
[HYBRID] Epoch 3, Batch 390/1563, loss: 2.647, reward: 32.471, critic_reward: 32.701, revenue_rate: 0.7948, distance: 9.6632, memory: -0.0317, power: 0.2937, lr: 0.000100, took: 81.608s
[HYBRID] Epoch 3, Batch 400/1563, loss: 2.398, reward: 32.178, critic_reward: 32.452, revenue_rate: 0.7878, distance: 9.5098, memory: -0.0413, power: 0.2898, lr: 0.000100, took: 80.620s
[HYBRID] Epoch 3, Batch 410/1563, loss: 2.813, reward: 32.007, critic_reward: 32.039, revenue_rate: 0.7850, distance: 9.4973, memory: -0.0430, power: 0.2862, lr: 0.000100, took: 79.694s
[HYBRID] Epoch 3, Batch 420/1563, loss: 4.420, reward: 31.740, critic_reward: 32.062, revenue_rate: 0.7742, distance: 9.3371, memory: -0.0463, power: 0.2827, lr: 0.000100, took: 79.732s
[HYBRID] Epoch 3, Batch 430/1563, loss: 3.105, reward: 31.225, critic_reward: 30.806, revenue_rate: 0.7629, distance: 9.1428, memory: -0.0555, power: 0.2769, lr: 0.000100, took: 76.364s
[HYBRID] Epoch 3, Batch 440/1563, loss: 5.726, reward: 31.661, critic_reward: 32.398, revenue_rate: 0.7747, distance: 9.2800, memory: -0.0516, power: 0.2801, lr: 0.000100, took: 79.787s
[HYBRID] Epoch 3, Batch 450/1563, loss: 3.792, reward: 32.710, critic_reward: 32.410, revenue_rate: 0.8044, distance: 9.7803, memory: -0.0290, power: 0.2971, lr: 0.000100, took: 82.858s
[HYBRID] Epoch 3, Batch 460/1563, loss: 3.257, reward: 34.451, critic_reward: 33.822, revenue_rate: 0.8460, distance: 10.4599, memory: -0.0011, power: 0.3198, lr: 0.000100, took: 90.395s
[HYBRID] Epoch 3, Batch 470/1563, loss: 3.471, reward: 33.349, critic_reward: 34.304, revenue_rate: 0.8189, distance: 10.1407, memory: -0.0171, power: 0.3060, lr: 0.000100, took: 85.862s
[HYBRID] Epoch 3, Batch 480/1563, loss: 2.543, reward: 30.165, critic_reward: 29.740, revenue_rate: 0.7385, distance: 8.8864, memory: -0.0522, power: 0.2688, lr: 0.000100, took: 73.939s
[HYBRID] Epoch 3, Batch 490/1563, loss: 2.521, reward: 28.716, critic_reward: 28.670, revenue_rate: 0.7003, distance: 8.2733, memory: -0.0753, power: 0.2524, lr: 0.000100, took: 68.743s
[HYBRID] Epoch 3, Batch 500/1563, loss: 4.497, reward: 29.787, critic_reward: 28.756, revenue_rate: 0.7259, distance: 8.5780, memory: -0.0720, power: 0.2602, lr: 0.000100, took: 71.233s
[HYBRID] Epoch 3, Batch 510/1563, loss: 2.881, reward: 32.005, critic_reward: 31.526, revenue_rate: 0.7831, distance: 9.3925, memory: -0.0321, power: 0.2858, lr: 0.000100, took: 79.593s
[HYBRID] Epoch 3, Batch 520/1563, loss: 3.292, reward: 33.479, critic_reward: 33.917, revenue_rate: 0.8233, distance: 10.0335, memory: -0.0171, power: 0.3044, lr: 0.000100, took: 85.809s
[HYBRID] Epoch 3, Batch 530/1563, loss: 3.937, reward: 34.472, critic_reward: 34.609, revenue_rate: 0.8492, distance: 10.4933, memory: -0.0125, power: 0.3215, lr: 0.000100, took: 91.427s
[HYBRID] Epoch 3, Batch 540/1563, loss: 6.422, reward: 34.206, critic_reward: 33.550, revenue_rate: 0.8397, distance: 10.3228, memory: -0.0117, power: 0.3136, lr: 0.000100, took: 88.585s
[HYBRID] Epoch 3, Batch 550/1563, loss: 2.627, reward: 33.918, critic_reward: 34.218, revenue_rate: 0.8341, distance: 10.3092, memory: -0.0092, power: 0.3128, lr: 0.000100, took: 89.349s
[HYBRID] Epoch 3, Batch 560/1563, loss: 2.859, reward: 34.428, critic_reward: 34.564, revenue_rate: 0.8449, distance: 10.6622, memory: -0.0056, power: 0.3243, lr: 0.000100, took: 90.765s
[HYBRID] Epoch 3, Batch 570/1563, loss: 2.954, reward: 33.849, critic_reward: 33.873, revenue_rate: 0.8330, distance: 10.3786, memory: -0.0106, power: 0.3158, lr: 0.000100, took: 89.242s
[HYBRID] Epoch 3, Batch 580/1563, loss: 3.207, reward: 34.096, critic_reward: 34.255, revenue_rate: 0.8421, distance: 10.5444, memory: -0.0077, power: 0.3211, lr: 0.000100, took: 89.876s
[HYBRID] Epoch 3, Batch 590/1563, loss: 2.882, reward: 32.358, critic_reward: 31.915, revenue_rate: 0.7933, distance: 9.5954, memory: -0.0234, power: 0.2943, lr: 0.000100, took: 81.335s
[HYBRID] Epoch 3, Batch 600/1563, loss: 4.395, reward: 33.443, critic_reward: 33.685, revenue_rate: 0.8222, distance: 10.0884, memory: -0.0199, power: 0.3029, lr: 0.000100, took: 85.036s
[HYBRID] Epoch 3, Batch 610/1563, loss: 3.218, reward: 34.646, critic_reward: 34.410, revenue_rate: 0.8514, distance: 10.5835, memory: -0.0170, power: 0.3225, lr: 0.000100, took: 91.131s
[HYBRID] Epoch 3, Batch 620/1563, loss: 2.718, reward: 33.208, critic_reward: 33.847, revenue_rate: 0.8164, distance: 9.9772, memory: -0.0188, power: 0.3024, lr: 0.000100, took: 85.561s
[HYBRID] Epoch 3, Batch 630/1563, loss: 4.475, reward: 31.568, critic_reward: 30.778, revenue_rate: 0.7742, distance: 9.4227, memory: -0.0408, power: 0.2858, lr: 0.000100, took: 81.387s
[HYBRID] Epoch 3, Batch 640/1563, loss: 5.799, reward: 32.169, critic_reward: 31.992, revenue_rate: 0.7862, distance: 9.4022, memory: -0.0443, power: 0.2882, lr: 0.000100, took: 79.917s
[HYBRID] Epoch 3, Batch 650/1563, loss: 3.201, reward: 31.447, critic_reward: 30.992, revenue_rate: 0.7701, distance: 9.3579, memory: -0.0423, power: 0.2819, lr: 0.000100, took: 77.833s
[HYBRID] Epoch 3, Batch 660/1563, loss: 3.070, reward: 32.839, critic_reward: 33.284, revenue_rate: 0.8051, distance: 9.8024, memory: -0.0261, power: 0.2980, lr: 0.000100, took: 84.670s
[HYBRID] Epoch 3, Batch 670/1563, loss: 3.320, reward: 33.580, critic_reward: 33.516, revenue_rate: 0.8220, distance: 9.9925, memory: -0.0195, power: 0.3061, lr: 0.000100, took: 86.481s
[HYBRID] Epoch 3, Batch 680/1563, loss: 4.826, reward: 33.361, critic_reward: 32.497, revenue_rate: 0.8188, distance: 10.1344, memory: -0.0223, power: 0.3053, lr: 0.000100, took: 85.593s
[HYBRID] Epoch 3, Batch 690/1563, loss: 3.646, reward: 32.970, critic_reward: 33.384, revenue_rate: 0.8080, distance: 9.9165, memory: -0.0151, power: 0.3018, lr: 0.000100, took: 83.983s
[HYBRID] Epoch 3, Batch 700/1563, loss: 3.028, reward: 32.374, critic_reward: 32.410, revenue_rate: 0.7941, distance: 9.7127, memory: -0.0288, power: 0.2939, lr: 0.000100, took: 81.533s
[HYBRID] Epoch 3, Batch 710/1563, loss: 3.774, reward: 32.683, critic_reward: 32.183, revenue_rate: 0.7997, distance: 9.8383, memory: -0.0182, power: 0.2980, lr: 0.000100, took: 83.383s
[HYBRID] Epoch 3, Batch 720/1563, loss: 7.936, reward: 32.452, critic_reward: 31.704, revenue_rate: 0.7967, distance: 9.7684, memory: -0.0373, power: 0.2951, lr: 0.000100, took: 82.430s
[HYBRID] Epoch 3, Batch 730/1563, loss: 6.602, reward: 33.272, critic_reward: 33.062, revenue_rate: 0.8183, distance: 9.9656, memory: -0.0232, power: 0.3023, lr: 0.000100, took: 85.326s
[HYBRID] Epoch 3, Batch 740/1563, loss: 8.879, reward: 32.761, critic_reward: 31.532, revenue_rate: 0.8057, distance: 9.8687, memory: -0.0269, power: 0.2980, lr: 0.000100, took: 85.789s
[HYBRID] Epoch 3, Batch 750/1563, loss: 5.678, reward: 33.990, critic_reward: 32.776, revenue_rate: 0.8370, distance: 10.3057, memory: -0.0077, power: 0.3133, lr: 0.000100, took: 88.858s
[HYBRID] Epoch 3, Batch 760/1563, loss: 3.625, reward: 35.897, critic_reward: 36.141, revenue_rate: 0.8875, distance: 11.4521, memory: 0.0245, power: 0.3485, lr: 0.000100, took: 101.810s
[HYBRID] Epoch 3, Batch 770/1563, loss: 2.976, reward: 36.060, critic_reward: 36.147, revenue_rate: 0.8917, distance: 11.4766, memory: 0.0183, power: 0.3484, lr: 0.000100, took: 99.902s
[HYBRID] Epoch 3, Batch 780/1563, loss: 3.731, reward: 34.285, critic_reward: 34.443, revenue_rate: 0.8483, distance: 10.5658, memory: -0.0042, power: 0.3209, lr: 0.000100, took: 90.305s
[HYBRID] Epoch 3, Batch 790/1563, loss: 3.856, reward: 34.551, critic_reward: 34.145, revenue_rate: 0.8468, distance: 10.4868, memory: -0.0130, power: 0.3214, lr: 0.000100, took: 90.435s
[HYBRID] Epoch 3, Batch 800/1563, loss: 3.571, reward: 35.663, critic_reward: 35.304, revenue_rate: 0.8814, distance: 11.1660, memory: 0.0110, power: 0.3378, lr: 0.000100, took: 96.235s
[HYBRID] Epoch 3, Batch 810/1563, loss: 3.290, reward: 35.809, critic_reward: 35.700, revenue_rate: 0.8862, distance: 11.3074, memory: 0.0159, power: 0.3413, lr: 0.000100, took: 96.861s
[HYBRID] Epoch 3, Batch 820/1563, loss: 3.143, reward: 34.965, critic_reward: 34.957, revenue_rate: 0.8615, distance: 10.8132, memory: 0.0060, power: 0.3285, lr: 0.000100, took: 92.621s
[HYBRID] Epoch 3, Batch 830/1563, loss: 3.464, reward: 33.661, critic_reward: 33.390, revenue_rate: 0.8261, distance: 10.1870, memory: -0.0181, power: 0.3068, lr: 0.000100, took: 86.518s
[HYBRID] Epoch 3, Batch 840/1563, loss: 3.240, reward: 33.499, critic_reward: 33.686, revenue_rate: 0.8234, distance: 9.9919, memory: -0.0274, power: 0.3063, lr: 0.000100, took: 88.796s
[HYBRID] Epoch 3, Batch 850/1563, loss: 3.256, reward: 33.749, critic_reward: 34.128, revenue_rate: 0.8305, distance: 10.2169, memory: -0.0139, power: 0.3123, lr: 0.000100, took: 87.653s
[HYBRID] Epoch 3, Batch 860/1563, loss: 4.456, reward: 33.201, critic_reward: 32.729, revenue_rate: 0.8148, distance: 9.8815, memory: -0.0250, power: 0.3035, lr: 0.000100, took: 87.247s
[HYBRID] Epoch 3, Batch 870/1563, loss: 2.548, reward: 32.257, critic_reward: 32.169, revenue_rate: 0.7871, distance: 9.5072, memory: -0.0350, power: 0.2900, lr: 0.000100, took: 80.389s
[HYBRID] Epoch 3, Batch 880/1563, loss: 3.455, reward: 32.736, critic_reward: 33.483, revenue_rate: 0.8018, distance: 9.7933, memory: -0.0321, power: 0.2940, lr: 0.000100, took: 82.190s
[HYBRID] Epoch 3, Batch 890/1563, loss: 6.804, reward: 33.589, critic_reward: 31.802, revenue_rate: 0.8229, distance: 10.1213, memory: -0.0188, power: 0.3078, lr: 0.000100, took: 86.227s
[HYBRID] Epoch 3, Batch 900/1563, loss: 2.819, reward: 34.038, critic_reward: 34.058, revenue_rate: 0.8372, distance: 10.3884, memory: 0.0064, power: 0.3144, lr: 0.000100, took: 88.579s
[HYBRID] Epoch 3, Batch 910/1563, loss: 4.234, reward: 34.013, critic_reward: 34.827, revenue_rate: 0.8347, distance: 10.2334, memory: -0.0174, power: 0.3125, lr: 0.000100, took: 87.711s
[HYBRID] Epoch 3, Batch 920/1563, loss: 2.568, reward: 32.450, critic_reward: 32.072, revenue_rate: 0.7945, distance: 9.6431, memory: -0.0250, power: 0.2929, lr: 0.000100, took: 81.552s
[HYBRID] Epoch 3, Batch 930/1563, loss: 3.226, reward: 33.168, critic_reward: 33.248, revenue_rate: 0.8132, distance: 9.9250, memory: -0.0257, power: 0.3029, lr: 0.000100, took: 84.201s
[HYBRID] Epoch 3, Batch 940/1563, loss: 2.272, reward: 33.843, critic_reward: 33.920, revenue_rate: 0.8321, distance: 10.2340, memory: -0.0174, power: 0.3132, lr: 0.000100, took: 91.216s
[HYBRID] Epoch 3, Batch 950/1563, loss: 3.900, reward: 33.278, critic_reward: 32.726, revenue_rate: 0.8163, distance: 10.0047, memory: -0.0206, power: 0.3090, lr: 0.000100, took: 85.899s
[HYBRID] Epoch 3, Batch 960/1563, loss: 3.987, reward: 32.810, critic_reward: 33.187, revenue_rate: 0.8039, distance: 9.8059, memory: -0.0297, power: 0.2988, lr: 0.000100, took: 84.154s
[HYBRID] Epoch 3, Batch 970/1563, loss: 3.338, reward: 32.515, critic_reward: 32.731, revenue_rate: 0.7965, distance: 9.7299, memory: -0.0351, power: 0.2941, lr: 0.000100, took: 81.584s
[HYBRID] Epoch 3, Batch 980/1563, loss: 3.093, reward: 34.386, critic_reward: 33.719, revenue_rate: 0.8473, distance: 10.4516, memory: -0.0118, power: 0.3163, lr: 0.000100, took: 89.277s
[HYBRID] Epoch 3, Batch 990/1563, loss: 2.886, reward: 33.045, critic_reward: 33.448, revenue_rate: 0.8091, distance: 9.9534, memory: -0.0196, power: 0.3012, lr: 0.000100, took: 84.083s
[HYBRID] Epoch 3, Batch 1000/1563, loss: 3.162, reward: 33.577, critic_reward: 33.801, revenue_rate: 0.8269, distance: 10.1963, memory: -0.0117, power: 0.3087, lr: 0.000100, took: 87.649s
[HYBRID] Epoch 3, Batch 1010/1563, loss: 4.952, reward: 35.445, critic_reward: 34.610, revenue_rate: 0.8746, distance: 11.0323, memory: 0.0114, power: 0.3372, lr: 0.000100, took: 96.395s
[HYBRID] Epoch 3, Batch 1020/1563, loss: 3.476, reward: 35.465, critic_reward: 36.212, revenue_rate: 0.8791, distance: 11.2924, memory: 0.0112, power: 0.3388, lr: 0.000100, took: 96.804s
[HYBRID] Epoch 3, Batch 1030/1563, loss: 3.249, reward: 33.890, critic_reward: 33.635, revenue_rate: 0.8322, distance: 10.3712, memory: -0.0110, power: 0.3177, lr: 0.000100, took: 88.834s
[HYBRID] Epoch 3, Batch 1040/1563, loss: 3.275, reward: 32.282, critic_reward: 32.022, revenue_rate: 0.7912, distance: 9.6486, memory: -0.0308, power: 0.2938, lr: 0.000100, took: 84.441s
[HYBRID] Epoch 3, Batch 1050/1563, loss: 2.621, reward: 30.797, critic_reward: 30.419, revenue_rate: 0.7541, distance: 9.0591, memory: -0.0412, power: 0.2751, lr: 0.000100, took: 75.694s
[HYBRID] Epoch 3, Batch 1060/1563, loss: 3.108, reward: 32.036, critic_reward: 32.011, revenue_rate: 0.7871, distance: 9.7034, memory: -0.0369, power: 0.2909, lr: 0.000100, took: 81.193s
[HYBRID] Epoch 3, Batch 1070/1563, loss: 4.053, reward: 33.938, critic_reward: 33.518, revenue_rate: 0.8335, distance: 10.3205, memory: -0.0166, power: 0.3141, lr: 0.000100, took: 90.088s
[HYBRID] Epoch 3, Batch 1080/1563, loss: 3.987, reward: 34.897, critic_reward: 35.645, revenue_rate: 0.8616, distance: 10.8769, memory: 0.0010, power: 0.3300, lr: 0.000100, took: 93.488s
[HYBRID] Epoch 3, Batch 1090/1563, loss: 2.050, reward: 34.604, critic_reward: 34.542, revenue_rate: 0.8504, distance: 10.6526, memory: -0.0016, power: 0.3225, lr: 0.000100, took: 92.038s
[HYBRID] Epoch 3, Batch 1100/1563, loss: 2.912, reward: 34.190, critic_reward: 34.443, revenue_rate: 0.8433, distance: 10.4494, memory: 0.0019, power: 0.3184, lr: 0.000100, took: 89.903s
[HYBRID] Epoch 3, Batch 1110/1563, loss: 2.646, reward: 34.247, critic_reward: 33.949, revenue_rate: 0.8430, distance: 10.5110, memory: -0.0115, power: 0.3163, lr: 0.000100, took: 89.572s
[HYBRID] Epoch 3, Batch 1120/1563, loss: 3.846, reward: 34.439, critic_reward: 33.890, revenue_rate: 0.8486, distance: 10.5504, memory: -0.0080, power: 0.3208, lr: 0.000100, took: 90.819s
[HYBRID] Epoch 3, Batch 1130/1563, loss: 4.543, reward: 34.196, critic_reward: 34.727, revenue_rate: 0.8402, distance: 10.4810, memory: -0.0100, power: 0.3175, lr: 0.000100, took: 90.357s
[HYBRID] Epoch 3, Batch 1140/1563, loss: 4.827, reward: 33.207, critic_reward: 33.655, revenue_rate: 0.8154, distance: 10.0386, memory: -0.0356, power: 0.3046, lr: 0.000100, took: 85.670s
[HYBRID] Epoch 3, Batch 1150/1563, loss: 2.597, reward: 31.877, critic_reward: 31.491, revenue_rate: 0.7806, distance: 9.3859, memory: -0.0342, power: 0.2872, lr: 0.000100, took: 82.179s
[HYBRID] Epoch 3, Batch 1160/1563, loss: 2.549, reward: 33.290, critic_reward: 33.614, revenue_rate: 0.8177, distance: 10.1852, memory: -0.0156, power: 0.3079, lr: 0.000100, took: 86.875s
[HYBRID] Epoch 3, Batch 1170/1563, loss: 3.859, reward: 33.482, critic_reward: 33.804, revenue_rate: 0.8233, distance: 10.1355, memory: -0.0182, power: 0.3088, lr: 0.000100, took: 87.962s
[HYBRID] Epoch 3, Batch 1180/1563, loss: 2.343, reward: 31.998, critic_reward: 31.878, revenue_rate: 0.7815, distance: 9.3873, memory: -0.0364, power: 0.2857, lr: 0.000100, took: 79.365s
[HYBRID] Epoch 3, Batch 1190/1563, loss: 2.475, reward: 32.009, critic_reward: 31.696, revenue_rate: 0.7837, distance: 9.5216, memory: -0.0318, power: 0.2884, lr: 0.000100, took: 79.934s
[HYBRID] Epoch 3, Batch 1200/1563, loss: 4.155, reward: 32.814, critic_reward: 33.283, revenue_rate: 0.8050, distance: 9.7288, memory: -0.0318, power: 0.2984, lr: 0.000100, took: 83.735s
[HYBRID] Epoch 3, Batch 1210/1563, loss: 2.645, reward: 32.333, critic_reward: 32.100, revenue_rate: 0.7905, distance: 9.5141, memory: -0.0428, power: 0.2903, lr: 0.000100, took: 80.428s
[HYBRID] Epoch 3, Batch 1220/1563, loss: 2.919, reward: 32.142, critic_reward: 32.443, revenue_rate: 0.7845, distance: 9.4503, memory: -0.0333, power: 0.2881, lr: 0.000100, took: 79.941s
[HYBRID] Epoch 3, Batch 1230/1563, loss: 3.101, reward: 31.945, critic_reward: 32.199, revenue_rate: 0.7795, distance: 9.3164, memory: -0.0412, power: 0.2848, lr: 0.000100, took: 79.031s
[HYBRID] Epoch 3, Batch 1240/1563, loss: 2.012, reward: 30.702, critic_reward: 30.561, revenue_rate: 0.7493, distance: 8.8483, memory: -0.0529, power: 0.2710, lr: 0.000100, took: 74.742s
[HYBRID] Epoch 3, Batch 1250/1563, loss: 4.090, reward: 31.459, critic_reward: 31.671, revenue_rate: 0.7712, distance: 9.1674, memory: -0.0515, power: 0.2809, lr: 0.000100, took: 77.954s
[HYBRID] Epoch 3, Batch 1260/1563, loss: 4.659, reward: 31.928, critic_reward: 32.803, revenue_rate: 0.7811, distance: 9.3912, memory: -0.0509, power: 0.2832, lr: 0.000100, took: 79.975s
[HYBRID] Epoch 3, Batch 1270/1563, loss: 2.594, reward: 32.773, critic_reward: 32.717, revenue_rate: 0.7989, distance: 9.7115, memory: -0.0344, power: 0.2942, lr: 0.000100, took: 81.816s
[HYBRID] Epoch 3, Batch 1280/1563, loss: 3.597, reward: 32.360, critic_reward: 32.182, revenue_rate: 0.7912, distance: 9.5020, memory: -0.0361, power: 0.2906, lr: 0.000100, took: 82.945s
[HYBRID] Epoch 3, Batch 1290/1563, loss: 4.746, reward: 31.900, critic_reward: 33.033, revenue_rate: 0.7815, distance: 9.5468, memory: -0.0396, power: 0.2878, lr: 0.000100, took: 80.343s
[HYBRID] Epoch 3, Batch 1300/1563, loss: 2.868, reward: 30.188, critic_reward: 30.808, revenue_rate: 0.7368, distance: 8.7438, memory: -0.0615, power: 0.2672, lr: 0.000100, took: 73.787s
[HYBRID] Epoch 3, Batch 1310/1563, loss: 2.427, reward: 30.138, critic_reward: 30.172, revenue_rate: 0.7359, distance: 8.7523, memory: -0.0551, power: 0.2675, lr: 0.000100, took: 73.805s
[HYBRID] Epoch 3, Batch 1320/1563, loss: 4.603, reward: 32.737, critic_reward: 32.044, revenue_rate: 0.8016, distance: 9.7171, memory: -0.0358, power: 0.2962, lr: 0.000100, took: 83.609s
[HYBRID] Epoch 3, Batch 1330/1563, loss: 5.870, reward: 32.933, critic_reward: 33.947, revenue_rate: 0.8080, distance: 9.9277, memory: -0.0260, power: 0.3019, lr: 0.000100, took: 84.526s
[HYBRID] Epoch 3, Batch 1340/1563, loss: 4.296, reward: 32.651, critic_reward: 32.882, revenue_rate: 0.8009, distance: 9.7164, memory: -0.0296, power: 0.2981, lr: 0.000100, took: 82.981s
[HYBRID] Epoch 3, Batch 1350/1563, loss: 3.609, reward: 33.174, critic_reward: 32.619, revenue_rate: 0.8103, distance: 9.9164, memory: -0.0222, power: 0.3007, lr: 0.000100, took: 84.774s
[HYBRID] Epoch 3, Batch 1360/1563, loss: 2.852, reward: 34.443, critic_reward: 34.165, revenue_rate: 0.8451, distance: 10.4933, memory: -0.0062, power: 0.3191, lr: 0.000100, took: 90.092s
[HYBRID] Epoch 3, Batch 1370/1563, loss: 3.009, reward: 34.037, critic_reward: 34.199, revenue_rate: 0.8392, distance: 10.3863, memory: -0.0072, power: 0.3157, lr: 0.000100, took: 91.668s
[HYBRID] Epoch 3, Batch 1380/1563, loss: 2.880, reward: 33.248, critic_reward: 33.430, revenue_rate: 0.8164, distance: 9.9859, memory: -0.0205, power: 0.3022, lr: 0.000100, took: 84.538s
[HYBRID] Epoch 3, Batch 1390/1563, loss: 4.351, reward: 32.671, critic_reward: 33.467, revenue_rate: 0.8015, distance: 9.6218, memory: -0.0307, power: 0.2940, lr: 0.000100, took: 84.860s
[HYBRID] Epoch 3, Batch 1400/1563, loss: 3.570, reward: 32.710, critic_reward: 32.343, revenue_rate: 0.8008, distance: 9.6855, memory: -0.0380, power: 0.2961, lr: 0.000100, took: 82.518s
[HYBRID] Epoch 3, Batch 1410/1563, loss: 3.235, reward: 33.060, critic_reward: 32.962, revenue_rate: 0.8126, distance: 9.8994, memory: -0.0227, power: 0.3012, lr: 0.000100, took: 84.861s
[HYBRID] Epoch 3, Batch 1420/1563, loss: 2.701, reward: 34.017, critic_reward: 33.578, revenue_rate: 0.8341, distance: 10.3479, memory: -0.0111, power: 0.3139, lr: 0.000100, took: 88.736s
[HYBRID] Epoch 3, Batch 1430/1563, loss: 4.373, reward: 33.828, critic_reward: 33.690, revenue_rate: 0.8344, distance: 10.3069, memory: -0.0143, power: 0.3129, lr: 0.000100, took: 88.795s
[HYBRID] Epoch 3, Batch 1440/1563, loss: 4.940, reward: 33.890, critic_reward: 33.046, revenue_rate: 0.8319, distance: 10.3277, memory: -0.0096, power: 0.3112, lr: 0.000100, took: 89.772s
[HYBRID] Epoch 3, Batch 1450/1563, loss: 3.238, reward: 33.950, critic_reward: 33.747, revenue_rate: 0.8321, distance: 10.2818, memory: -0.0163, power: 0.3134, lr: 0.000100, took: 89.571s
[HYBRID] Epoch 3, Batch 1460/1563, loss: 2.862, reward: 33.076, critic_reward: 33.548, revenue_rate: 0.8149, distance: 9.9772, memory: -0.0293, power: 0.3034, lr: 0.000100, took: 85.094s
[HYBRID] Epoch 3, Batch 1470/1563, loss: 3.414, reward: 32.852, critic_reward: 31.954, revenue_rate: 0.8076, distance: 9.8371, memory: -0.0254, power: 0.2984, lr: 0.000100, took: 86.791s
[HYBRID] Epoch 3, Batch 1480/1563, loss: 3.834, reward: 33.618, critic_reward: 34.103, revenue_rate: 0.8299, distance: 10.2743, memory: -0.0092, power: 0.3099, lr: 0.000100, took: 87.849s
[HYBRID] Epoch 3, Batch 1490/1563, loss: 6.623, reward: 33.780, critic_reward: 33.480, revenue_rate: 0.8325, distance: 10.2229, memory: -0.0141, power: 0.3101, lr: 0.000100, took: 90.111s
[HYBRID] Epoch 3, Batch 1500/1563, loss: 3.670, reward: 33.570, critic_reward: 33.627, revenue_rate: 0.8236, distance: 10.0972, memory: -0.0225, power: 0.3077, lr: 0.000100, took: 86.225s
[HYBRID] Epoch 3, Batch 1510/1563, loss: 2.178, reward: 33.318, critic_reward: 33.162, revenue_rate: 0.8187, distance: 10.0279, memory: -0.0202, power: 0.3050, lr: 0.000100, took: 85.893s
[HYBRID] Epoch 3, Batch 1520/1563, loss: 2.648, reward: 33.683, critic_reward: 33.392, revenue_rate: 0.8277, distance: 10.1738, memory: -0.0183, power: 0.3093, lr: 0.000100, took: 88.874s
[HYBRID] Epoch 3, Batch 1530/1563, loss: 5.877, reward: 31.973, critic_reward: 33.204, revenue_rate: 0.7845, distance: 9.4595, memory: -0.0427, power: 0.2884, lr: 0.000100, took: 80.045s
[HYBRID] Epoch 3, Batch 1540/1563, loss: 3.732, reward: 29.674, critic_reward: 29.354, revenue_rate: 0.7258, distance: 8.5290, memory: -0.0631, power: 0.2594, lr: 0.000100, took: 71.535s
[HYBRID] Epoch 3, Batch 1550/1563, loss: 3.085, reward: 28.306, critic_reward: 27.870, revenue_rate: 0.6890, distance: 8.0101, memory: -0.0840, power: 0.2446, lr: 0.000100, took: 66.969s
[HYBRID] Epoch 3, Batch 1560/1563, loss: 2.395, reward: 28.858, critic_reward: 28.573, revenue_rate: 0.7034, distance: 8.2134, memory: -0.0712, power: 0.2503, lr: 0.000100, took: 68.583s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 30.843, revenue_rate: 0.7532, distance: 8.9075, memory: -0.0536, power: 0.2712
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24 (验证集奖励: 30.8434)
[HYBRID] 训练完成
训练结束时间: 2025-08-15 03:36:39
训练总耗时: 11:28:45.800289
训练过程统计:
  最终训练奖励: 28.2854
  最佳验证奖励: 30.8434
  训练轮数完成: 4689
  奖励提升: 16.7754
  平均每轮提升: 0.0036
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 10000
  测试批次数: 157
  可视化样本数: 5
测试开始时间: 2025-08-15 03:37:19
测试结束时间: 2025-08-15 03:57:58
测试耗时: 0:20:39.547612

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 30.8434
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24
测试结果:
  平均收益率: 0.7528
  平均距离: 8.9065
  平均内存使用: -0.0534
  平均功耗: 0.2710
模型信息:
  Actor参数: 3,927,817
  Critic参数: 691,149
  总参数: 4,618,966
综合性能评分: 2.9123
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 1 day, 12:53:18.332208
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  33.4630  0.8238   10.0802  -0.0234  0.3052   4,225,238 
competitive  32.1037  0.7902   9.5017   -0.0384  0.2890   4,225,238 
hybrid       30.8434  0.7528   8.9065   -0.0534  0.2710   4,618,966 

性能排名:
🏆 最高奖励: COOPERATIVE (33.4630)
💰 最高收益率: COOPERATIVE (0.8238)
🚀 最短距离: HYBRID (8.9065)
⚡ 最低功耗: HYBRID (0.2710)

💡 推荐模式: COOPERATIVE
   理由: 在奖励和收益率两个关键指标上都表现最佳

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48
   对比分析: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\comparison_results
   全局日志: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48
   competitive 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39
   hybrid 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24
