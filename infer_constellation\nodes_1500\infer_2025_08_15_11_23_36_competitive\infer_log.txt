推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 122.9231
  收益率: 0.2081
  距离: 32.0112
  内存使用: 0.5967
  能量使用: 0.9459
  推理时间: 2.3762秒

批次 2:
  奖励值: 119.1207
  收益率: 0.1998
  距离: 29.8295
  内存使用: 0.6100
  能量使用: 0.9191
  推理时间: 2.5464秒

批次 3:
  奖励值: 130.0923
  收益率: 0.2220
  距离: 32.2726
  内存使用: 0.6674
  能量使用: 1.0752
  推理时间: 2.7812秒

批次 4:
  奖励值: 125.5760
  收益率: 0.2048
  距离: 28.5673
  内存使用: 0.6919
  能量使用: 0.9886
  推理时间: 2.4363秒

批次 5:
  奖励值: 144.7589
  收益率: 0.2445
  距离: 38.1776
  内存使用: 0.7863
  能量使用: 1.1183
  推理时间: 3.0556秒

批次 6:
  奖励值: 130.0392
  收益率: 0.2109
  距离: 30.4427
  内存使用: 0.6994
  能量使用: 0.9793
  推理时间: 2.5569秒

批次 7:
  奖励值: 131.5207
  收益率: 0.2174
  距离: 32.8830
  内存使用: 0.6927
  能量使用: 0.9829
  推理时间: 2.5682秒

批次 8:
  奖励值: 119.0289
  收益率: 0.1987
  距离: 29.5330
  内存使用: 0.8996
  能量使用: 0.9095
  推理时间: 2.5961秒

批次 9:
  奖励值: 141.6050
  收益率: 0.2380
  距离: 36.5251
  内存使用: 0.7836
  能量使用: 1.0812
  推理时间: 2.7561秒

批次 10:
  奖励值: 122.0670
  收益率: 0.2066
  距离: 30.6885
  内存使用: 0.6315
  能量使用: 0.9460
  推理时间: 2.3464秒

批次 11:
  奖励值: 134.8994
  收益率: 0.2241
  距离: 30.5003
  内存使用: 0.6912
  能量使用: 1.0097
  推理时间: 2.7988秒

批次 12:
  奖励值: 127.7176
  收益率: 0.2090
  距离: 28.3963
  内存使用: 0.6471
  能量使用: 0.9215
  推理时间: 2.4033秒

批次 13:
  奖励值: 128.4860
  收益率: 0.2161
  距离: 32.5237
  内存使用: 0.6519
  能量使用: 1.0525
  推理时间: 2.8119秒

批次 14:
  奖励值: 120.9273
  收益率: 0.1989
  距离: 26.7129
  内存使用: 0.8997
  能量使用: 0.9328
  推理时间: 2.3400秒

批次 15:
  奖励值: 133.8468
  收益率: 0.2239
  距离: 33.1842
  内存使用: 0.6234
  能量使用: 1.0988
  推理时间: 2.8373秒

批次 16:
  奖励值: 116.0557
  收益率: 0.1953
  距离: 27.7128
  内存使用: 0.8544
  能量使用: 0.9213
  推理时间: 2.3030秒

批次 17:
  奖励值: 119.0234
  收益率: 0.1985
  距离: 31.2146
  内存使用: 0.6335
  能量使用: 0.9050
  推理时间: 2.3519秒

批次 18:
  奖励值: 121.9607
  收益率: 0.2113
  距离: 32.9244
  内存使用: 0.8848
  能量使用: 0.8694
  推理时间: 2.4145秒

批次 19:
  奖励值: 126.5860
  收益率: 0.2119
  距离: 30.4457
  内存使用: 0.6203
  能量使用: 0.9620
  推理时间: 2.4374秒

批次 20:
  奖励值: 131.8453
  收益率: 0.2199
  距离: 30.8396
  内存使用: 0.6348
  能量使用: 1.0349
  推理时间: 2.8263秒

批次 21:
  奖励值: 139.5189
  收益率: 0.2331
  距离: 33.7470
  内存使用: 0.7647
  能量使用: 1.0505
  推理时间: 2.8999秒

批次 22:
  奖励值: 120.5396
  收益率: 0.1965
  距离: 28.6560
  内存使用: 0.8997
  能量使用: 0.9649
  推理时间: 2.3634秒

批次 23:
  奖励值: 117.8522
  收益率: 0.1952
  距离: 29.7528
  内存使用: 0.5888
  能量使用: 0.9538
  推理时间: 2.3269秒

批次 24:
  奖励值: 121.3632
  收益率: 0.2087
  距离: 31.1790
  内存使用: 0.8959
  能量使用: 0.9988
  推理时间: 2.4315秒

批次 25:
  奖励值: 122.2199
  收益率: 0.2006
  距离: 29.7096
  内存使用: 0.8990
  能量使用: 0.9516
  推理时间: 2.4008秒

批次 26:
  奖励值: 109.7953
  收益率: 0.1871
  距离: 32.1667
  内存使用: 0.8990
  能量使用: 0.9436
  推理时间: 2.2643秒

批次 27:
  奖励值: 136.2923
  收益率: 0.2285
  距离: 32.8767
  内存使用: 0.6881
  能量使用: 1.0506
  推理时间: 2.9283秒

批次 28:
  奖励值: 133.2596
  收益率: 0.2233
  距离: 33.9375
  内存使用: 0.7101
  能量使用: 1.0331
  推理时间: 2.5843秒

批次 29:
  奖励值: 138.1463
  收益率: 0.2361
  距离: 33.0111
  内存使用: 0.6834
  能量使用: 1.0130
  推理时间: 2.6190秒

批次 30:
  奖励值: 117.3668
  收益率: 0.1996
  距离: 28.3993
  内存使用: 0.8994
  能量使用: 0.9676
  推理时间: 2.3312秒

批次 31:
  奖励值: 112.8059
  收益率: 0.1938
  距离: 31.3989
  内存使用: 0.5700
  能量使用: 0.8192
  推理时间: 2.2261秒

批次 32:
  奖励值: 140.6842
  收益率: 0.2304
  距离: 33.6235
  内存使用: 0.7202
  能量使用: 1.0878
  推理时间: 2.7229秒

批次 33:
  奖励值: 116.5639
  收益率: 0.1946
  距离: 26.8631
  内存使用: 0.8997
  能量使用: 0.9094
  推理时间: 2.3405秒

批次 34:
  奖励值: 128.2280
  收益率: 0.2173
  距离: 34.8166
  内存使用: 0.7024
  能量使用: 1.0249
  推理时间: 2.5364秒

批次 35:
  奖励值: 116.3933
  收益率: 0.1964
  距离: 31.5772
  内存使用: 0.8995
  能量使用: 0.9366
  推理时间: 2.3423秒

批次 36:
  奖励值: 128.2910
  收益率: 0.2175
  距离: 33.7436
  内存使用: 0.6685
  能量使用: 1.0058
  推理时间: 2.5163秒

批次 37:
  奖励值: 124.1990
  收益率: 0.2044
  距离: 31.3973
  内存使用: 0.6514
  能量使用: 0.9534
  推理时间: 2.6549秒

批次 38:
  奖励值: 122.9431
  收益率: 0.2021
  距离: 29.3961
  内存使用: 0.6333
  能量使用: 0.8844
  推理时间: 2.3235秒

批次 39:
  奖励值: 113.8830
  收益率: 0.1919
  距离: 30.1027
  内存使用: 0.8989
  能量使用: 0.8324
  推理时间: 2.4929秒

批次 40:
  奖励值: 134.9192
  收益率: 0.2241
  距离: 32.7992
  内存使用: 0.7204
  能量使用: 1.0418
  推理时间: 2.8423秒

批次 41:
  奖励值: 119.9306
  收益率: 0.1983
  距离: 29.5060
  内存使用: 0.8697
  能量使用: 0.9630
  推理时间: 2.3816秒

批次 42:
  奖励值: 131.7420
  收益率: 0.2185
  距离: 31.2964
  内存使用: 0.7397
  能量使用: 1.0799
  推理时间: 2.5335秒

批次 43:
  奖励值: 124.8728
  收益率: 0.2128
  距离: 32.3977
  内存使用: 0.8997
  能量使用: 0.9569
  推理时间: 2.5153秒

批次 44:
  奖励值: 120.6108
  收益率: 0.2045
  距离: 30.1313
  内存使用: 0.8674
  能量使用: 0.9797
  推理时间: 2.4058秒

批次 45:
  奖励值: 138.2380
  收益率: 0.2343
  距离: 32.1309
  内存使用: 0.7318
  能量使用: 1.0613
  推理时间: 2.6868秒

批次 46:
  奖励值: 125.3270
  收益率: 0.2084
  距离: 34.2337
  内存使用: 0.8988
  能量使用: 1.0134
  推理时间: 2.8063秒

批次 47:
  奖励值: 123.8009
  收益率: 0.2098
  距离: 30.6279
  内存使用: 0.5891
  能量使用: 0.9811
  推理时间: 2.6246秒

批次 48:
  奖励值: 134.6337
  收益率: 0.2238
  距离: 31.7578
  内存使用: 0.7218
  能量使用: 1.0529
  推理时间: 2.8060秒

批次 49:
  奖励值: 118.5971
  收益率: 0.1967
  距离: 30.2817
  内存使用: 0.6278
  能量使用: 0.8963
  推理时间: 2.2925秒

批次 50:
  奖励值: 113.7521
  收益率: 0.1935
  距离: 29.2429
  内存使用: 0.8994
  能量使用: 0.9533
  推理时间: 2.3313秒

批次 51:
  奖励值: 119.8729
  收益率: 0.2015
  距离: 28.9032
  内存使用: 0.8579
  能量使用: 0.8929
  推理时间: 2.4243秒

批次 52:
  奖励值: 127.8576
  收益率: 0.2192
  距离: 32.5615
  内存使用: 0.6846
  能量使用: 1.0386
  推理时间: 2.5775秒

批次 53:
  奖励值: 122.4549
  收益率: 0.1985
  距离: 28.4920
  内存使用: 0.8744
  能量使用: 0.9238
  推理时间: 2.7043秒

批次 54:
  奖励值: 127.7776
  收益率: 0.2095
  距离: 28.4523
  内存使用: 0.6070
  能量使用: 0.9853
  推理时间: 2.5004秒

批次 55:
  奖励值: 130.3204
  收益率: 0.2146
  距离: 30.3560
  内存使用: 0.6543
  能量使用: 0.9488
  推理时间: 2.4583秒

批次 56:
  奖励值: 139.3648
  收益率: 0.2367
  距离: 36.4671
  内存使用: 0.7421
  能量使用: 1.0612
  推理时间: 2.7668秒

批次 57:
  奖励值: 132.3609
  收益率: 0.2202
  距离: 30.6502
  内存使用: 0.6695
  能量使用: 0.9583
  推理时间: 2.7462秒

批次 58:
  奖励值: 115.3219
  收益率: 0.1915
  距离: 28.5165
  内存使用: 0.8993
  能量使用: 0.9404
  推理时间: 2.3265秒

批次 59:
  奖励值: 119.0882
  收益率: 0.1968
  距离: 28.8972
  内存使用: 0.6421
  能量使用: 0.8843
  推理时间: 2.3178秒

批次 60:
  奖励值: 137.8975
  收益率: 0.2238
  距离: 29.3249
  内存使用: 0.7082
  能量使用: 0.9719
  推理时间: 2.6296秒

批次 61:
  奖励值: 121.0754
  收益率: 0.2033
  距离: 28.4075
  内存使用: 0.8937
  能量使用: 0.9479
  推理时间: 2.4139秒

批次 62:
  奖励值: 134.9791
  收益率: 0.2259
  距离: 31.1824
  内存使用: 0.6350
  能量使用: 1.0244
  推理时间: 2.6101秒

批次 63:
  奖励值: 114.9748
  收益率: 0.1916
  距离: 27.2431
  内存使用: 0.8989
  能量使用: 0.9085
  推理时间: 2.2820秒

批次 64:
  奖励值: 130.6608
  收益率: 0.2222
  距离: 34.1335
  内存使用: 0.6755
  能量使用: 1.0692
  推理时间: 2.5694秒

批次 65:
  奖励值: 135.4804
  收益率: 0.2210
  距离: 28.9163
  内存使用: 0.7070
  能量使用: 1.0158
  推理时间: 2.6054秒

批次 66:
  奖励值: 117.5434
  收益率: 0.1906
  距离: 27.3092
  内存使用: 0.8901
  能量使用: 0.9139
  推理时间: 2.5773秒

批次 67:
  奖励值: 131.6770
  收益率: 0.2186
  距离: 30.2689
  内存使用: 0.6472
  能量使用: 0.9297
  推理时间: 4.2283秒

批次 68:
  奖励值: 126.3098
  收益率: 0.2064
  距离: 28.0833
  内存使用: 0.5952
  能量使用: 0.9018
  推理时间: 2.6674秒

批次 69:
  奖励值: 123.5997
  收益率: 0.2070
  距离: 28.5655
  内存使用: 0.6643
  能量使用: 0.9708
  推理时间: 2.4158秒

批次 70:
  奖励值: 125.0388
  收益率: 0.2098
  距离: 30.1926
  内存使用: 0.5823
  能量使用: 0.9475
  推理时间: 2.4378秒

批次 71:
  奖励值: 127.1345
  收益率: 0.2130
  距离: 30.0883
  内存使用: 0.6486
  能量使用: 0.9940
  推理时间: 2.4639秒

批次 72:
  奖励值: 121.4564
  收益率: 0.1996
  距离: 27.6070
  内存使用: 0.6308
  能量使用: 0.8494
  推理时间: 2.5919秒

批次 73:
  奖励值: 114.5006
  收益率: 0.1912
  距离: 26.0320
  内存使用: 0.8989
  能量使用: 0.8678
  推理时间: 2.5227秒

批次 74:
  奖励值: 134.8171
  收益率: 0.2235
  距离: 31.1589
  内存使用: 0.7591
  能量使用: 1.0477
  推理时间: 2.6110秒

批次 75:
  奖励值: 125.7403
  收益率: 0.2091
  距离: 28.3560
  内存使用: 0.5950
  能量使用: 0.9682
  推理时间: 2.6306秒

批次 76:
  奖励值: 137.7474
  收益率: 0.2269
  距离: 32.7091
  内存使用: 0.7505
  能量使用: 1.0456
  推理时间: 2.6511秒

批次 77:
  奖励值: 118.9923
  收益率: 0.2024
  距离: 34.1674
  内存使用: 0.6255
  能量使用: 0.8225
  推理时间: 2.5799秒

批次 78:
  奖励值: 128.0454
  收益率: 0.2157
  距离: 34.0364
  内存使用: 0.6870
  能量使用: 0.9858
  推理时间: 2.4698秒

批次 79:
  奖励值: 120.1693
  收益率: 0.2034
  距离: 29.2718
  内存使用: 0.6643
  能量使用: 0.9252
  推理时间: 2.3273秒

批次 80:
  奖励值: 128.3347
  收益率: 0.2176
  距离: 29.8975
  内存使用: 0.6882
  能量使用: 0.9450
  推理时间: 2.7818秒

批次 81:
  奖励值: 122.1145
  收益率: 0.2059
  距离: 29.3360
  内存使用: 0.6339
  能量使用: 1.0010
  推理时间: 2.4383秒

批次 82:
  奖励值: 118.4995
  收益率: 0.1947
  距离: 30.3405
  内存使用: 0.8995
  能量使用: 0.9611
  推理时间: 2.4999秒

批次 83:
  奖励值: 121.4054
  收益率: 0.2006
  距离: 33.2802
  内存使用: 0.8985
  能量使用: 1.0105
  推理时间: 2.4887秒

批次 84:
  奖励值: 123.0030
  收益率: 0.2052
  距离: 32.3774
  内存使用: 0.6746
  能量使用: 0.9420
  推理时间: 2.4450秒

批次 85:
  奖励值: 122.1959
  收益率: 0.2023
  距离: 33.1881
  内存使用: 0.6278
  能量使用: 0.9922
  推理时间: 2.5240秒

批次 86:
  奖励值: 123.3554
  收益率: 0.2023
  距离: 28.4373
  内存使用: 0.8996
  能量使用: 0.9523
  推理时间: 2.7100秒

批次 87:
  奖励值: 130.4200
  收益率: 0.2135
  距离: 32.2390
  内存使用: 0.6583
  能量使用: 1.0627
  推理时间: 2.5718秒

批次 88:
  奖励值: 124.7271
  收益率: 0.2130
  距离: 31.2801
  内存使用: 0.6687
  能量使用: 0.9970
  推理时间: 2.6994秒

批次 89:
  奖励值: 141.4068
  收益率: 0.2349
  距离: 34.0742
  内存使用: 0.7376
  能量使用: 1.1745
  推理时间: 2.7417秒

批次 90:
  奖励值: 131.5731
  收益率: 0.2148
  距离: 30.5810
  内存使用: 0.6531
  能量使用: 1.0196
  推理时间: 2.5347秒

批次 91:
  奖励值: 129.3920
  收益率: 0.2183
  距离: 31.2292
  内存使用: 0.6521
  能量使用: 0.9347
  推理时间: 2.5357秒

批次 92:
  奖励值: 116.1283
  收益率: 0.1950
  距离: 30.7531
  内存使用: 0.8996
  能量使用: 0.8906
  推理时间: 2.3244秒

批次 93:
  奖励值: 138.2099
  收益率: 0.2302
  距离: 31.9709
  内存使用: 0.7237
  能量使用: 1.0855
  推理时间: 2.6754秒

批次 94:
  奖励值: 116.2316
  收益率: 0.1938
  距离: 29.8023
  内存使用: 0.6052
  能量使用: 0.9291
  推理时间: 2.3498秒

批次 95:
  奖励值: 126.6130
  收益率: 0.2139
  距离: 32.7758
  内存使用: 0.8995
  能量使用: 0.8987
  推理时间: 2.4960秒

批次 96:
  奖励值: 120.4920
  收益率: 0.2028
  距离: 31.1062
  内存使用: 0.6007
  能量使用: 0.9091
  推理时间: 2.6023秒

批次 97:
  奖励值: 132.0790
  收益率: 0.2185
  距离: 30.2556
  内存使用: 0.6269
  能量使用: 0.9594
  推理时间: 2.5535秒

批次 98:
  奖励值: 135.1858
  收益率: 0.2247
  距离: 32.3583
  内存使用: 0.6387
  能量使用: 1.0313
  推理时间: 2.5927秒

批次 99:
  奖励值: 134.8575
  收益率: 0.2281
  距离: 33.5459
  内存使用: 0.6854
  能量使用: 1.0536
  推理时间: 2.7455秒

批次 100:
  奖励值: 118.4941
  收益率: 0.1991
  距离: 30.2748
  内存使用: 0.5854
  能量使用: 0.9516
  推理时间: 2.5351秒


==================== 总结 ====================
平均收益率: 0.2106
平均能量使用: 0.9746
平均推理时间: 2.5603秒
