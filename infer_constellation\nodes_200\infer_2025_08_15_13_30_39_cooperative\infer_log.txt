推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 47.9559
  收益率: 0.6163
  距离: 13.7758
  内存使用: 0.1326
  能量使用: 0.3797
  推理时间: 1.1288秒

批次 2:
  奖励值: 52.2185
  收益率: 0.6462
  距离: 14.7886
  内存使用: 0.1442
  能量使用: 0.4302
  推理时间: 1.2616秒

批次 3:
  奖励值: 52.1152
  收益率: 0.6678
  距离: 13.7755
  内存使用: 0.1578
  能量使用: 0.4364
  推理时间: 1.2610秒

批次 4:
  奖励值: 55.4652
  收益率: 0.6691
  距离: 15.0112
  内存使用: 0.2121
  能量使用: 0.4463
  推理时间: 1.2840秒

批次 5:
  奖励值: 50.5666
  收益率: 0.6585
  距离: 15.8860
  内存使用: 0.1742
  能量使用: 0.4066
  推理时间: 1.2058秒

批次 6:
  奖励值: 59.5308
  收益率: 0.6974
  距离: 14.8725
  内存使用: 0.1656
  能量使用: 0.5004
  推理时间: 1.3137秒

批次 7:
  奖励值: 60.7411
  收益率: 0.6987
  距离: 16.0573
  内存使用: 0.1725
  能量使用: 0.4880
  推理时间: 1.5582秒

批次 8:
  奖励值: 57.0605
  收益率: 0.7076
  距离: 17.7757
  内存使用: 0.2154
  能量使用: 0.4878
  推理时间: 1.4565秒

批次 9:
  奖励值: 51.1993
  收益率: 0.6232
  距离: 11.8609
  内存使用: 0.1219
  能量使用: 0.4185
  推理时间: 1.4266秒

批次 10:
  奖励值: 56.1333
  收益率: 0.6691
  距离: 16.8844
  内存使用: 0.2183
  能量使用: 0.4307
  推理时间: 1.3748秒

批次 11:
  奖励值: 49.3849
  收益率: 0.6230
  距离: 15.7187
  内存使用: 0.1293
  能量使用: 0.3821
  推理时间: 1.1517秒

批次 12:
  奖励值: 51.1938
  收益率: 0.6368
  距离: 13.4324
  内存使用: 0.1476
  能量使用: 0.4324
  推理时间: 1.1079秒

批次 13:
  奖励值: 53.0698
  收益率: 0.6692
  距离: 15.4747
  内存使用: 0.1512
  能量使用: 0.4344
  推理时间: 1.3191秒

批次 14:
  奖励值: 60.4845
  收益率: 0.6903
  距离: 13.0377
  内存使用: 0.1560
  能量使用: 0.4373
  推理时间: 1.2985秒

批次 15:
  奖励值: 49.2313
  收益率: 0.6121
  距离: 13.9916
  内存使用: 0.1317
  能量使用: 0.4140
  推理时间: 1.1101秒

批次 16:
  奖励值: 52.8959
  收益率: 0.6471
  距离: 15.0186
  内存使用: 0.1474
  能量使用: 0.4421
  推理时间: 1.2064秒

批次 17:
  奖励值: 55.5236
  收益率: 0.6524
  距离: 13.7417
  内存使用: 0.1775
  能量使用: 0.4336
  推理时间: 1.2508秒

批次 18:
  奖励值: 52.8782
  收益率: 0.6348
  距离: 15.6063
  内存使用: 0.1477
  能量使用: 0.4896
  推理时间: 1.2538秒

批次 19:
  奖励值: 49.7892
  收益率: 0.6294
  距离: 13.6618
  内存使用: 0.1107
  能量使用: 0.4492
  推理时间: 1.1320秒

批次 20:
  奖励值: 52.3576
  收益率: 0.6376
  距离: 13.3257
  内存使用: 0.1026
  能量使用: 0.4565
  推理时间: 1.2942秒

批次 21:
  奖励值: 60.8386
  收益率: 0.7118
  距离: 15.3745
  内存使用: 0.2506
  能量使用: 0.4977
  推理时间: 1.5007秒

批次 22:
  奖励值: 53.0284
  收益率: 0.6641
  距离: 16.3928
  内存使用: 0.1712
  能量使用: 0.3791
  推理时间: 1.2022秒

批次 23:
  奖励值: 51.4266
  收益率: 0.6416
  距离: 15.7525
  内存使用: 0.1483
  能量使用: 0.4458
  推理时间: 1.1984秒

批次 24:
  奖励值: 56.6934
  收益率: 0.6904
  距离: 15.7368
  内存使用: 0.1958
  能量使用: 0.4805
  推理时间: 1.3416秒

批次 25:
  奖励值: 53.9872
  收益率: 0.6736
  距离: 13.5488
  内存使用: 0.1909
  能量使用: 0.4858
  推理时间: 1.3241秒

批次 26:
  奖励值: 52.2681
  收益率: 0.6406
  距离: 14.2962
  内存使用: 0.1282
  能量使用: 0.4394
  推理时间: 1.2505秒

批次 27:
  奖励值: 52.3938
  收益率: 0.6485
  距离: 13.7795
  内存使用: 0.1736
  能量使用: 0.4594
  推理时间: 1.4660秒

批次 28:
  奖励值: 46.0219
  收益率: 0.5982
  距离: 14.0201
  内存使用: 0.0951
  能量使用: 0.4410
  推理时间: 1.1749秒

批次 29:
  奖励值: 56.1912
  收益率: 0.6629
  距离: 17.1953
  内存使用: 0.1493
  能量使用: 0.4729
  推理时间: 1.4245秒

批次 30:
  奖励值: 54.9282
  收益率: 0.6933
  距离: 14.3048
  内存使用: 0.1954
  能量使用: 0.4435
  推理时间: 1.5452秒

批次 31:
  奖励值: 49.7673
  收益率: 0.6244
  距离: 13.2811
  内存使用: 0.1123
  能量使用: 0.4721
  推理时间: 1.3383秒

批次 32:
  奖励值: 49.3828
  收益率: 0.6379
  距离: 15.9042
  内存使用: 0.1201
  能量使用: 0.4099
  推理时间: 1.1303秒

批次 33:
  奖励值: 52.2018
  收益率: 0.6458
  距离: 12.8586
  内存使用: 0.1091
  能量使用: 0.4288
  推理时间: 1.3228秒

批次 34:
  奖励值: 53.8606
  收益率: 0.6685
  距离: 13.9173
  内存使用: 0.1705
  能量使用: 0.3909
  推理时间: 1.3232秒

批次 35:
  奖励值: 53.2708
  收益率: 0.6674
  距离: 13.9980
  内存使用: 0.1678
  能量使用: 0.4925
  推理时间: 1.2150秒

批次 36:
  奖励值: 49.9749
  收益率: 0.6301
  距离: 10.8702
  内存使用: 0.1133
  能量使用: 0.4666
  推理时间: 1.0969秒

批次 37:
  奖励值: 55.3846
  收益率: 0.6821
  距离: 12.6370
  内存使用: 0.1015
  能量使用: 0.4921
  推理时间: 1.3110秒

批次 38:
  奖励值: 50.5297
  收益率: 0.6240
  距离: 12.5791
  内存使用: 0.1271
  能量使用: 0.4345
  推理时间: 1.1439秒

批次 39:
  奖励值: 45.7427
  收益率: 0.5828
  距离: 13.6146
  内存使用: 0.0968
  能量使用: 0.4031
  推理时间: 1.2823秒

批次 40:
  奖励值: 49.4365
  收益率: 0.6346
  距离: 14.1536
  内存使用: 0.1285
  能量使用: 0.4449
  推理时间: 1.2455秒

批次 41:
  奖励值: 53.7242
  收益率: 0.6913
  距离: 13.8309
  内存使用: 0.0660
  能量使用: 0.4549
  推理时间: 1.3214秒

批次 42:
  奖励值: 52.3148
  收益率: 0.6476
  距离: 14.5615
  内存使用: 0.1649
  能量使用: 0.4439
  推理时间: 1.3709秒

批次 43:
  奖励值: 54.5683
  收益率: 0.6717
  距离: 14.2972
  内存使用: 0.1324
  能量使用: 0.4339
  推理时间: 1.2268秒

批次 44:
  奖励值: 48.1261
  收益率: 0.6092
  距离: 14.2547
  内存使用: 0.1192
  能量使用: 0.3738
  推理时间: 1.1032秒

批次 45:
  奖励值: 49.9037
  收益率: 0.6112
  距离: 11.3027
  内存使用: 0.1156
  能量使用: 0.3743
  推理时间: 1.1045秒

批次 46:
  奖励值: 54.9543
  收益率: 0.6684
  距离: 14.5288
  内存使用: 0.1110
  能量使用: 0.4515
  推理时间: 1.2926秒

批次 47:
  奖励值: 49.3307
  收益率: 0.6271
  距离: 13.8029
  内存使用: 0.0850
  能量使用: 0.4507
  推理时间: 1.1344秒

批次 48:
  奖励值: 49.8916
  收益率: 0.6426
  距离: 13.6854
  内存使用: 0.0825
  能量使用: 0.4488
  推理时间: 1.1863秒

批次 49:
  奖励值: 46.1292
  收益率: 0.5902
  距离: 13.2432
  内存使用: 0.0720
  能量使用: 0.4264
  推理时间: 1.0963秒

批次 50:
  奖励值: 53.6733
  收益率: 0.6776
  距离: 16.0124
  内存使用: 0.1663
  能量使用: 0.4747
  推理时间: 1.3579秒

批次 51:
  奖励值: 51.0292
  收益率: 0.6226
  距离: 13.2190
  内存使用: 0.1156
  能量使用: 0.4069
  推理时间: 1.2525秒

批次 52:
  奖励值: 54.4004
  收益率: 0.6702
  距离: 16.5416
  内存使用: 0.2054
  能量使用: 0.4523
  推理时间: 1.2191秒

批次 53:
  奖励值: 62.4593
  收益率: 0.6915
  距离: 13.3398
  内存使用: 0.2145
  能量使用: 0.5270
  推理时间: 1.4249秒

批次 54:
  奖励值: 53.0842
  收益率: 0.6553
  距离: 12.6085
  内存使用: 0.1501
  能量使用: 0.4729
  推理时间: 1.2902秒

批次 55:
  奖励值: 47.2567
  收益率: 0.5989
  距离: 12.2179
  内存使用: 0.1187
  能量使用: 0.3500
  推理时间: 1.0947秒

批次 56:
  奖励值: 54.5905
  收益率: 0.6351
  距离: 13.4350
  内存使用: 0.1508
  能量使用: 0.4332
  推理时间: 1.2270秒

批次 57:
  奖励值: 55.0328
  收益率: 0.6846
  距离: 14.2976
  内存使用: 0.1510
  能量使用: 0.4859
  推理时间: 1.2973秒

批次 58:
  奖励值: 53.8169
  收益率: 0.6688
  距离: 15.3360
  内存使用: 0.1350
  能量使用: 0.4952
  推理时间: 1.2335秒

批次 59:
  奖励值: 49.1070
  收益率: 0.6134
  距离: 12.7252
  内存使用: 0.0568
  能量使用: 0.4040
  推理时间: 1.0673秒

批次 60:
  奖励值: 55.2612
  收益率: 0.6859
  距离: 15.0777
  内存使用: 0.1477
  能量使用: 0.4523
  推理时间: 1.1625秒

批次 61:
  奖励值: 48.1895
  收益率: 0.5830
  距离: 11.3245
  内存使用: 0.0964
  能量使用: 0.4002
  推理时间: 1.0902秒

批次 62:
  奖励值: 50.1887
  收益率: 0.6270
  距离: 15.0689
  内存使用: 0.1276
  能量使用: 0.4261
  推理时间: 1.2109秒

批次 63:
  奖励值: 46.5426
  收益率: 0.6240
  距离: 14.5849
  内存使用: 0.0952
  能量使用: 0.4348
  推理时间: 1.0450秒

批次 64:
  奖励值: 56.5070
  收益率: 0.7011
  距离: 15.2591
  内存使用: 0.2318
  能量使用: 0.4950
  推理时间: 1.3165秒

批次 65:
  奖励值: 52.6496
  收益率: 0.6447
  距离: 17.1163
  内存使用: 0.1556
  能量使用: 0.4289
  推理时间: 1.2162秒

批次 66:
  奖励值: 50.1588
  收益率: 0.6246
  距离: 14.9284
  内存使用: 0.1490
  能量使用: 0.4051
  推理时间: 1.0998秒

批次 67:
  奖励值: 48.2724
  收益率: 0.6087
  距离: 14.3942
  内存使用: 0.0849
  能量使用: 0.3761
  推理时间: 1.0555秒

批次 68:
  奖励值: 51.8510
  收益率: 0.6652
  距离: 16.8603
  内存使用: 0.1434
  能量使用: 0.4943
  推理时间: 1.1825秒

批次 69:
  奖励值: 50.1711
  收益率: 0.6280
  距离: 14.8889
  内存使用: 0.1209
  能量使用: 0.4480
  推理时间: 1.1240秒

批次 70:
  奖励值: 48.0107
  收益率: 0.6030
  距离: 14.6279
  内存使用: 0.1274
  能量使用: 0.4233
  推理时间: 1.1357秒

批次 71:
  奖励值: 46.4471
  收益率: 0.5725
  距离: 11.6782
  内存使用: 0.0422
  能量使用: 0.3853
  推理时间: 0.9991秒

批次 72:
  奖励值: 45.3204
  收益率: 0.6059
  距离: 15.2232
  内存使用: 0.1184
  能量使用: 0.4176
  推理时间: 1.1278秒

批次 73:
  奖励值: 52.4252
  收益率: 0.6576
  距离: 15.5207
  内存使用: 0.1391
  能量使用: 0.4898
  推理时间: 1.2631秒

批次 74:
  奖励值: 49.3048
  收益率: 0.6187
  距离: 14.4324
  内存使用: 0.1024
  能量使用: 0.4555
  推理时间: 1.0825秒

批次 75:
  奖励值: 52.5487
  收益率: 0.6471
  距离: 15.2738
  内存使用: 0.1683
  能量使用: 0.4605
  推理时间: 2.4603秒

批次 76:
  奖励值: 49.8689
  收益率: 0.6299
  距离: 15.1206
  内存使用: 0.1200
  能量使用: 0.4216
  推理时间: 1.1204秒

批次 77:
  奖励值: 47.4804
  收益率: 0.6086
  距离: 14.8903
  内存使用: 0.1196
  能量使用: 0.4292
  推理时间: 1.1352秒

批次 78:
  奖励值: 48.6592
  收益率: 0.6211
  距离: 12.9614
  内存使用: 0.0890
  能量使用: 0.4312
  推理时间: 1.0840秒

批次 79:
  奖励值: 46.0465
  收益率: 0.5842
  距离: 12.1716
  内存使用: 0.1259
  能量使用: 0.4095
  推理时间: 1.0121秒

批次 80:
  奖励值: 57.7670
  收益率: 0.6857
  距离: 15.9693
  内存使用: 0.1887
  能量使用: 0.5079
  推理时间: 1.2651秒

批次 81:
  奖励值: 46.2975
  收益率: 0.6116
  距离: 14.0659
  内存使用: 0.3747
  能量使用: 0.3644
  推理时间: 1.0832秒

批次 82:
  奖励值: 52.4717
  收益率: 0.6488
  距离: 15.6787
  内存使用: 0.1700
  能量使用: 0.4079
  推理时间: 1.2095秒

批次 83:
  奖励值: 56.6286
  收益率: 0.6819
  距离: 14.2906
  内存使用: 0.1799
  能量使用: 0.4722
  推理时间: 1.2128秒

批次 84:
  奖励值: 52.9610
  收益率: 0.6578
  距离: 11.8829
  内存使用: 0.1344
  能量使用: 0.4608
  推理时间: 1.1453秒

批次 85:
  奖励值: 49.8845
  收益率: 0.6360
  距离: 12.8696
  内存使用: 0.1174
  能量使用: 0.4439
  推理时间: 1.0667秒

批次 86:
  奖励值: 41.3150
  收益率: 0.5679
  距离: 12.3150
  内存使用: 0.1093
  能量使用: 0.3457
  推理时间: 0.9943秒

批次 87:
  奖励值: 52.9854
  收益率: 0.6460
  距离: 14.2364
  内存使用: 0.1570
  能量使用: 0.4357
  推理时间: 1.3237秒

批次 88:
  奖励值: 51.5748
  收益率: 0.6306
  距离: 14.0429
  内存使用: 0.1616
  能量使用: 0.4459
  推理时间: 1.2423秒

批次 89:
  奖励值: 44.1857
  收益率: 0.5402
  距离: 10.5111
  内存使用: 0.0597
  能量使用: 0.4578
  推理时间: 1.1483秒

批次 90:
  奖励值: 50.2034
  收益率: 0.6044
  距离: 14.2967
  内存使用: 0.0939
  能量使用: 0.4026
  推理时间: 1.1718秒

批次 91:
  奖励值: 46.7588
  收益率: 0.5832
  距离: 10.4473
  内存使用: 0.0679
  能量使用: 0.3673
  推理时间: 1.0397秒

批次 92:
  奖励值: 52.8555
  收益率: 0.6553
  距离: 13.1623
  内存使用: 0.1333
  能量使用: 0.3933
  推理时间: 1.1843秒

批次 93:
  奖励值: 44.9585
  收益率: 0.5829
  距离: 14.3861
  内存使用: 0.0914
  能量使用: 0.4055
  推理时间: 1.0439秒

批次 94:
  奖励值: 50.6575
  收益率: 0.6352
  距离: 15.1458
  内存使用: 0.1407
  能量使用: 0.3985
  推理时间: 1.2888秒

批次 95:
  奖励值: 52.3168
  收益率: 0.6345
  距离: 14.5989
  内存使用: 0.1206
  能量使用: 0.4468
  推理时间: 1.2559秒

批次 96:
  奖励值: 54.3256
  收益率: 0.6602
  距离: 12.3242
  内存使用: 0.1528
  能量使用: 0.4717
  推理时间: 1.1807秒

批次 97:
  奖励值: 55.5835
  收益率: 0.6838
  距离: 18.7578
  内存使用: 0.1971
  能量使用: 0.4781
  推理时间: 1.2564秒

批次 98:
  奖励值: 48.2037
  收益率: 0.6293
  距离: 15.5939
  内存使用: 0.0540
  能量使用: 0.3448
  推理时间: 1.0732秒

批次 99:
  奖励值: 54.3012
  收益率: 0.6401
  距离: 14.6375
  内存使用: 0.1261
  能量使用: 0.4341
  推理时间: 1.1290秒

批次 100:
  奖励值: 48.2737
  收益率: 0.6233
  距离: 13.1581
  内存使用: 0.0957
  能量使用: 0.3988
  推理时间: 1.0589秒


==================== 总结 ====================
平均收益率: 0.6417
平均能量使用: 0.4371
平均推理时间: 1.2283秒
