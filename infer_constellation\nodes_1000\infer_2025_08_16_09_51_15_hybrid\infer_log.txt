推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 111.9176
  收益率: 0.2876
  距离: 27.0923
  内存使用: 0.5773
  能量使用: 0.8953
  推理时间: 2.1108秒

批次 2:
  奖励值: 112.4176
  收益率: 0.2814
  距离: 29.5636
  内存使用: 0.5510
  能量使用: 0.8502
  推理时间: 2.0194秒

批次 3:
  奖励值: 112.5254
  收益率: 0.2798
  距离: 29.9594
  内存使用: 0.5360
  能量使用: 0.8538
  推理时间: 2.3033秒

批次 4:
  奖励值: 123.7793
  收益率: 0.3016
  距离: 26.5161
  内存使用: 0.6245
  能量使用: 0.9006
  推理时间: 2.2887秒

批次 5:
  奖励值: 115.2182
  收益率: 0.2928
  距离: 31.6885
  内存使用: 0.6264
  能量使用: 0.8487
  推理时间: 2.3465秒

批次 6:
  奖励值: 125.4345
  收益率: 0.3151
  距离: 30.9639
  内存使用: 0.6389
  能量使用: 0.9282
  推理时间: 2.2918秒

批次 7:
  奖励值: 112.4263
  收益率: 0.2858
  距离: 27.3410
  内存使用: 0.5738
  能量使用: 0.8326
  推理时间: 2.0839秒

批次 8:
  奖励值: 111.1830
  收益率: 0.2852
  距离: 31.9552
  内存使用: 0.5921
  能量使用: 0.8866
  推理时间: 2.0707秒

批次 9:
  奖励值: 124.7665
  收益率: 0.3097
  距离: 27.9681
  内存使用: 0.6020
  能量使用: 0.8969
  推理时间: 2.2212秒

批次 10:
  奖励值: 115.0842
  收益率: 0.2830
  距离: 27.2853
  内存使用: 0.5451
  能量使用: 0.8012
  推理时间: 2.2759秒

批次 11:
  奖励值: 108.7436
  收益率: 0.2653
  距离: 26.8280
  内存使用: 0.4999
  能量使用: 0.7848
  推理时间: 2.0232秒

批次 12:
  奖励值: 120.3033
  收益率: 0.3060
  距离: 30.2137
  内存使用: 0.6463
  能量使用: 0.9334
  推理时间: 2.3840秒

批次 13:
  奖励值: 107.5327
  收益率: 0.2728
  距离: 29.1879
  内存使用: 0.5667
  能量使用: 0.7800
  推理时间: 1.9789秒

批次 14:
  奖励值: 108.6623
  收益率: 0.2757
  距离: 27.9234
  内存使用: 0.5469
  能量使用: 0.8050
  推理时间: 2.1933秒

批次 15:
  奖励值: 125.5780
  收益率: 0.3161
  距离: 29.2871
  内存使用: 0.6306
  能量使用: 0.9263
  推理时间: 2.4962秒

批次 16:
  奖励值: 119.6109
  收益率: 0.3034
  距离: 32.1533
  内存使用: 0.5598
  能量使用: 0.8650
  推理时间: 2.3364秒

批次 17:
  奖励值: 121.4392
  收益率: 0.3048
  距离: 29.5712
  内存使用: 0.6181
  能量使用: 0.9323
  推理时间: 2.4463秒

批次 18:
  奖励值: 123.9861
  收益率: 0.3129
  距离: 28.8364
  内存使用: 0.6206
  能量使用: 0.9708
  推理时间: 2.1940秒

批次 19:
  奖励值: 123.5952
  收益率: 0.3066
  距离: 28.0918
  内存使用: 0.6200
  能量使用: 0.8977
  推理时间: 2.2629秒

批次 20:
  奖励值: 126.1344
  收益率: 0.3106
  距离: 30.2263
  内存使用: 0.5948
  能量使用: 0.9101
  推理时间: 2.4936秒

批次 21:
  奖励值: 126.0091
  收益率: 0.3003
  距离: 30.2295
  内存使用: 0.5842
  能量使用: 0.9681
  推理时间: 2.5488秒

批次 22:
  奖励值: 113.5666
  收益率: 0.2845
  距离: 30.6741
  内存使用: 0.5172
  能量使用: 0.8754
  推理时间: 2.0271秒

批次 23:
  奖励值: 113.3121
  收益率: 0.2873
  距离: 28.5842
  内存使用: 0.5526
  能量使用: 0.8388
  推理时间: 2.0844秒

批次 24:
  奖励值: 117.5691
  收益率: 0.2951
  距离: 31.8999
  内存使用: 0.6207
  能量使用: 0.9411
  推理时间: 2.1664秒

批次 25:
  奖励值: 127.9684
  收益率: 0.3053
  距离: 28.1667
  内存使用: 0.5667
  能量使用: 0.9297
  推理时间: 2.4932秒

批次 26:
  奖励值: 114.7797
  收益率: 0.2914
  距离: 29.7985
  内存使用: 0.5439
  能量使用: 0.8982
  推理时间: 2.2826秒

批次 27:
  奖励值: 116.1566
  收益率: 0.2953
  距离: 27.8599
  内存使用: 0.5646
  能量使用: 0.8622
  推理时间: 2.0769秒

批次 28:
  奖励值: 116.0167
  收益率: 0.2873
  距离: 26.8312
  内存使用: 0.5529
  能量使用: 0.7825
  推理时间: 2.2933秒

批次 29:
  奖励值: 130.0584
  收益率: 0.3189
  距离: 27.0273
  内存使用: 0.6489
  能量使用: 1.0070
  推理时间: 2.3588秒

批次 30:
  奖励值: 111.8864
  收益率: 0.2703
  距离: 26.6587
  内存使用: 0.5506
  能量使用: 0.8179
  推理时间: 2.0537秒

批次 31:
  奖励值: 123.7672
  收益率: 0.3122
  距离: 31.2609
  内存使用: 0.5547
  能量使用: 0.8499
  推理时间: 2.4769秒

批次 32:
  奖励值: 117.4176
  收益率: 0.2943
  距离: 30.5353
  内存使用: 0.5561
  能量使用: 0.8733
  推理时间: 2.1091秒

批次 33:
  奖励值: 114.3866
  收益率: 0.2858
  距离: 29.6154
  内存使用: 0.5333
  能量使用: 0.8779
  推理时间: 2.2549秒

批次 34:
  奖励值: 118.6634
  收益率: 0.2934
  距离: 28.7801
  内存使用: 0.6291
  能量使用: 0.8640
  推理时间: 2.3461秒

批次 35:
  奖励值: 120.3041
  收益率: 0.3004
  距离: 29.0761
  内存使用: 0.5938
  能量使用: 0.9220
  推理时间: 2.3397秒

批次 36:
  奖励值: 116.4014
  收益率: 0.2915
  距离: 28.5576
  内存使用: 0.5448
  能量使用: 0.8947
  推理时间: 2.1404秒

批次 37:
  奖励值: 127.3499
  收益率: 0.3224
  距离: 33.2187
  内存使用: 0.6611
  能量使用: 1.0204
  推理时间: 2.6139秒

批次 38:
  奖励值: 118.9817
  收益率: 0.2991
  距离: 28.1095
  内存使用: 0.6385
  能量使用: 0.8887
  推理时间: 2.3929秒

批次 39:
  奖励值: 114.5402
  收益率: 0.2924
  距离: 25.8331
  内存使用: 0.5983
  能量使用: 0.8881
  推理时间: 2.3181秒

批次 40:
  奖励值: 123.3954
  收益率: 0.3083
  距离: 31.2267
  内存使用: 0.5591
  能量使用: 1.0234
  推理时间: 2.4269秒

批次 41:
  奖励值: 122.6824
  收益率: 0.3034
  距离: 26.9256
  内存使用: 0.5630
  能量使用: 0.9465
  推理时间: 2.3825秒

批次 42:
  奖励值: 132.8006
  收益率: 0.3275
  距离: 32.0805
  内存使用: 0.6722
  能量使用: 1.0460
  推理时间: 2.3805秒

批次 43:
  奖励值: 102.5777
  收益率: 0.2599
  距离: 23.3301
  内存使用: 0.5442
  能量使用: 0.7702
  推理时间: 2.0969秒

批次 44:
  奖励值: 124.0108
  收益率: 0.3127
  距离: 28.6519
  内存使用: 0.5826
  能量使用: 0.9439
  推理时间: 2.4711秒

批次 45:
  奖励值: 116.2143
  收益率: 0.2913
  距离: 28.6525
  内存使用: 0.4509
  能量使用: 0.8681
  推理时间: 2.3770秒

批次 46:
  奖励值: 124.2047
  收益率: 0.3138
  距离: 30.2165
  内存使用: 0.5817
  能量使用: 0.9924
  推理时间: 2.4491秒

批次 47:
  奖励值: 122.0218
  收益率: 0.3065
  距离: 32.4242
  内存使用: 0.5543
  能量使用: 0.9779
  推理时间: 2.4700秒

批次 48:
  奖励值: 120.7337
  收益率: 0.3112
  距离: 29.2013
  内存使用: 0.6431
  能量使用: 0.8883
  推理时间: 2.2452秒

批次 49:
  奖励值: 116.2833
  收益率: 0.2909
  距离: 29.7848
  内存使用: 0.5923
  能量使用: 0.8562
  推理时间: 2.3334秒

批次 50:
  奖励值: 120.9483
  收益率: 0.2966
  距离: 30.8237
  内存使用: 0.5632
  能量使用: 0.9165
  推理时间: 2.4207秒

批次 51:
  奖励值: 121.7471
  收益率: 0.2961
  距离: 26.4609
  内存使用: 0.5854
  能量使用: 0.8596
  推理时间: 2.3885秒

批次 52:
  奖励值: 122.0581
  收益率: 0.3045
  距离: 32.3007
  内存使用: 0.6456
  能量使用: 0.9376
  推理时间: 2.4568秒

批次 53:
  奖励值: 112.5395
  收益率: 0.2867
  距离: 25.0304
  内存使用: 0.5113
  能量使用: 0.7792
  推理时间: 2.2159秒

批次 54:
  奖励值: 116.2508
  收益率: 0.2885
  距离: 28.0182
  内存使用: 0.5426
  能量使用: 0.9376
  推理时间: 2.3078秒

批次 55:
  奖励值: 112.7554
  收益率: 0.2789
  距离: 25.8903
  内存使用: 0.5724
  能量使用: 0.8264
  推理时间: 2.2156秒

批次 56:
  奖励值: 107.1031
  收益率: 0.2643
  距离: 25.4626
  内存使用: 0.5095
  能量使用: 0.8218
  推理时间: 2.1225秒

批次 57:
  奖励值: 117.5068
  收益率: 0.3058
  距离: 30.3860
  内存使用: 0.6458
  能量使用: 0.9545
  推理时间: 2.3941秒

批次 58:
  奖励值: 124.7972
  收益率: 0.3107
  距离: 30.6677
  内存使用: 0.5802
  能量使用: 0.9577
  推理时间: 2.4839秒

批次 59:
  奖励值: 117.9045
  收益率: 0.2942
  距离: 26.7920
  内存使用: 0.5362
  能量使用: 0.8629
  推理时间: 2.3325秒

批次 60:
  奖励值: 114.4944
  收益率: 0.2905
  距离: 29.0284
  内存使用: 0.5235
  能量使用: 0.8592
  推理时间: 2.0951秒

批次 61:
  奖励值: 128.4296
  收益率: 0.3204
  距离: 29.5346
  内存使用: 0.6895
  能量使用: 0.9167
  推理时间: 2.6138秒

批次 62:
  奖励值: 106.1059
  收益率: 0.2649
  距离: 25.1183
  内存使用: 0.5288
  能量使用: 0.7411
  推理时间: 2.2692秒

批次 63:
  奖励值: 121.2515
  收益率: 0.2990
  距离: 31.0053
  内存使用: 0.6128
  能量使用: 0.8788
  推理时间: 2.5137秒

批次 64:
  奖励值: 119.7386
  收益率: 0.3008
  距离: 29.2952
  内存使用: 0.5247
  能量使用: 0.9029
  推理时间: 2.3967秒

批次 65:
  奖励值: 120.5808
  收益率: 0.2981
  距离: 27.9406
  内存使用: 0.5735
  能量使用: 0.9244
  推理时间: 2.1981秒

批次 66:
  奖励值: 117.5682
  收益率: 0.2910
  距离: 26.4520
  内存使用: 0.5575
  能量使用: 0.8541
  推理时间: 2.2784秒

批次 67:
  奖励值: 120.6385
  收益率: 0.3005
  距离: 28.3779
  内存使用: 0.5459
  能量使用: 0.9991
  推理时间: 2.2276秒

批次 68:
  奖励值: 115.0477
  收益率: 0.2895
  距离: 28.5226
  内存使用: 0.5326
  能量使用: 0.8493
  推理时间: 2.3304秒

批次 69:
  奖励值: 113.7466
  收益率: 0.2833
  距离: 27.3165
  内存使用: 0.5704
  能量使用: 0.8200
  推理时间: 2.3965秒

批次 70:
  奖励值: 127.7490
  收益率: 0.3232
  距离: 34.7981
  内存使用: 0.6594
  能量使用: 1.0443
  推理时间: 2.6181秒

批次 71:
  奖励值: 122.7672
  收益率: 0.3018
  距离: 27.8076
  内存使用: 0.5621
  能量使用: 0.8959
  推理时间: 2.2226秒

批次 72:
  奖励值: 109.0872
  收益率: 0.2729
  距离: 25.6545
  内存使用: 0.5422
  能量使用: 0.8288
  推理时间: 2.1962秒

批次 73:
  奖励值: 113.7310
  收益率: 0.2930
  距离: 29.2499
  内存使用: 0.5864
  能量使用: 0.9016
  推理时间: 2.1121秒

批次 74:
  奖励值: 127.3696
  收益率: 0.3044
  距离: 28.3289
  内存使用: 0.6120
  能量使用: 0.9199
  推理时间: 2.2905秒

批次 75:
  奖励值: 109.2850
  收益率: 0.2604
  距离: 26.0821
  内存使用: 0.5445
  能量使用: 0.8034
  推理时间: 2.2268秒

批次 76:
  奖励值: 113.7676
  收益率: 0.2890
  距离: 29.1500
  内存使用: 0.5128
  能量使用: 0.8021
  推理时间: 2.2703秒

批次 77:
  奖励值: 117.0249
  收益率: 0.2869
  距离: 30.9308
  内存使用: 0.5635
  能量使用: 0.8559
  推理时间: 2.3266秒

批次 78:
  奖励值: 114.7595
  收益率: 0.2899
  距离: 28.6053
  内存使用: 0.5764
  能量使用: 0.8992
  推理时间: 2.1113秒

批次 79:
  奖励值: 114.0885
  收益率: 0.2818
  距离: 25.8062
  内存使用: 0.5520
  能量使用: 0.8647
  推理时间: 2.2821秒

批次 80:
  奖励值: 116.3269
  收益率: 0.2878
  距离: 28.9132
  内存使用: 0.5274
  能量使用: 0.9056
  推理时间: 2.0898秒

批次 81:
  奖励值: 125.3068
  收益率: 0.3124
  距离: 32.8300
  内存使用: 0.6331
  能量使用: 0.9232
  推理时间: 2.5236秒

批次 82:
  奖励值: 117.0091
  收益率: 0.2960
  距离: 27.8553
  内存使用: 0.5839
  能量使用: 0.9426
  推理时间: 2.3060秒

批次 83:
  奖励值: 122.7953
  收益率: 0.3095
  距离: 31.8871
  内存使用: 0.6042
  能量使用: 0.9182
  推理时间: 2.2569秒

批次 84:
  奖励值: 116.4621
  收益率: 0.2939
  距离: 27.2061
  内存使用: 0.5756
  能量使用: 0.8941
  推理时间: 2.0911秒

批次 85:
  奖励值: 113.5211
  收益率: 0.2820
  距离: 26.5218
  内存使用: 0.6028
  能量使用: 0.8332
  推理时间: 2.2461秒

批次 86:
  奖励值: 118.6651
  收益率: 0.3103
  距离: 31.9363
  内存使用: 0.5941
  能量使用: 0.9393
  推理时间: 2.4030秒

批次 87:
  奖励值: 116.3880
  收益率: 0.3007
  距离: 28.3793
  内存使用: 0.5229
  能量使用: 0.9217
  推理时间: 2.3778秒

批次 88:
  奖励值: 126.3274
  收益率: 0.3201
  距离: 31.1791
  内存使用: 0.6255
  能量使用: 0.9406
  推理时间: 2.5419秒

批次 89:
  奖励值: 122.9560
  收益率: 0.3073
  距离: 31.9750
  内存使用: 0.6128
  能量使用: 0.9001
  推理时间: 2.4945秒

批次 90:
  奖励值: 110.7029
  收益率: 0.2765
  距离: 27.9404
  内存使用: 0.5540
  能量使用: 0.8998
  推理时间: 2.0062秒

批次 91:
  奖励值: 120.5269
  收益率: 0.3017
  距离: 27.5142
  内存使用: 0.5526
  能量使用: 0.8794
  推理时间: 2.3829秒

批次 92:
  奖励值: 117.9507
  收益率: 0.2968
  距离: 29.9894
  内存使用: 0.6070
  能量使用: 0.9021
  推理时间: 2.1749秒

批次 93:
  奖励值: 115.5903
  收益率: 0.2831
  距离: 27.6099
  内存使用: 0.5633
  能量使用: 0.8461
  推理时间: 2.5152秒

批次 94:
  奖励值: 120.9336
  收益率: 0.2963
  距离: 28.4300
  内存使用: 0.6050
  能量使用: 0.8976
  推理时间: 2.3621秒

批次 95:
  奖励值: 124.3208
  收益率: 0.3107
  距离: 32.9826
  内存使用: 0.6241
  能量使用: 0.9516
  推理时间: 2.2492秒

批次 96:
  奖励值: 121.3534
  收益率: 0.3020
  距离: 27.6775
  内存使用: 0.5240
  能量使用: 0.8917
  推理时间: 2.4080秒

批次 97:
  奖励值: 120.7024
  收益率: 0.3014
  距离: 31.0299
  内存使用: 0.6096
  能量使用: 0.9557
  推理时间: 2.4443秒

批次 98:
  奖励值: 116.7503
  收益率: 0.2984
  距离: 29.6218
  内存使用: 0.5987
  能量使用: 0.8789
  推理时间: 2.3240秒

批次 99:
  奖励值: 113.3263
  收益率: 0.2788
  距离: 25.0569
  内存使用: 0.4894
  能量使用: 0.8010
  推理时间: 2.1594秒

批次 100:
  奖励值: 116.2793
  收益率: 0.2931
  距离: 26.9822
  内存使用: 0.5841
  能量使用: 0.8750
  推理时间: 2.1071秒


==================== 总结 ====================
平均收益率: 0.2958
平均能量使用: 0.8913
平均推理时间: 2.2961秒
