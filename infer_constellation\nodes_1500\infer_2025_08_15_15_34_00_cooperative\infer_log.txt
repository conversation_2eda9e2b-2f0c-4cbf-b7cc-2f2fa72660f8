推理数据数量: 100
每个序列任务数量: 1500
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 146.3005
  收益率: 0.2446
  距离: 33.9678
  内存使用: 0.7425
  能量使用: 1.0886
  推理时间: 2.8893秒

批次 2:
  奖励值: 122.4407
  收益率: 0.2058
  距离: 31.4164
  内存使用: 0.6055
  能量使用: 0.8966
  推理时间: 2.5007秒

批次 3:
  奖励值: 141.5272
  收益率: 0.2430
  距离: 37.0396
  内存使用: 0.7438
  能量使用: 1.1621
  推理时间: 2.8985秒

批次 4:
  奖励值: 125.2299
  收益率: 0.2084
  距离: 34.2101
  内存使用: 0.6924
  能量使用: 1.0377
  推理时间: 2.5636秒

批次 5:
  奖励值: 146.6895
  收益率: 0.2478
  距离: 38.6425
  内存使用: 0.8278
  能量使用: 1.1506
  推理时间: 2.9728秒

批次 6:
  奖励值: 139.2687
  收益率: 0.2259
  距离: 32.6087
  内存使用: 0.7345
  能量使用: 1.1192
  推理时间: 2.7180秒

批次 7:
  奖励值: 132.7962
  收益率: 0.2214
  距离: 35.6997
  内存使用: 0.7262
  能量使用: 0.9815
  推理时间: 2.7448秒

批次 8:
  奖励值: 114.8456
  收益率: 0.1925
  距离: 29.4827
  内存使用: 0.8999
  能量使用: 0.9262
  推理时间: 2.4100秒

批次 9:
  奖励值: 149.4890
  收益率: 0.2503
  距离: 37.2701
  内存使用: 0.8268
  能量使用: 1.1250
  推理时间: 3.0292秒

批次 10:
  奖励值: 121.5439
  收益率: 0.2051
  距离: 29.7308
  内存使用: 0.6424
  能量使用: 0.9391
  推理时间: 2.4022秒

批次 11:
  奖励值: 108.4282
  收益率: 0.1855
  距离: 31.6911
  内存使用: 0.5713
  能量使用: 0.8811
  推理时间: 2.2558秒

批次 12:
  奖励值: 130.4384
  收益率: 0.2150
  距离: 31.0399
  内存使用: 0.6787
  能量使用: 1.0045
  推理时间: 2.6221秒

批次 13:
  奖励值: 146.1953
  收益率: 0.2450
  距离: 35.9376
  内存使用: 0.7033
  能量使用: 1.1685
  推理时间: 2.9423秒

批次 14:
  奖励值: 129.5063
  收益率: 0.2170
  距离: 34.3250
  内存使用: 0.6347
  能量使用: 1.0277
  推理时间: 2.6313秒

批次 15:
  奖励值: 141.0401
  收益率: 0.2339
  距离: 32.0732
  内存使用: 0.7297
  能量使用: 1.1168
  推理时间: 2.8501秒

批次 16:
  奖励值: 137.9415
  收益率: 0.2356
  距离: 37.8830
  内存使用: 0.7469
  能量使用: 1.0870
  推理时间: 2.8579秒

批次 17:
  奖励值: 133.3128
  收益率: 0.2205
  距离: 32.5467
  内存使用: 0.6455
  能量使用: 0.9821
  推理时间: 2.9352秒

批次 18:
  奖励值: 148.4371
  收益率: 0.2567
  距离: 39.8504
  内存使用: 0.8205
  能量使用: 1.0548
  推理时间: 3.0392秒

批次 19:
  奖励值: 128.4889
  收益率: 0.2157
  距离: 31.8089
  内存使用: 0.6595
  能量使用: 0.9539
  推理时间: 2.5525秒

批次 20:
  奖励值: 140.7680
  收益率: 0.2340
  距离: 31.7424
  内存使用: 0.7671
  能量使用: 1.1545
  推理时间: 2.8388秒

批次 21:
  奖励值: 138.1505
  收益率: 0.2342
  距离: 37.8611
  内存使用: 0.7587
  能量使用: 1.0792
  推理时间: 2.8142秒

批次 22:
  奖励值: 146.2284
  收益率: 0.2392
  距离: 36.2089
  内存使用: 0.8115
  能量使用: 1.1226
  推理时间: 2.9141秒

批次 23:
  奖励值: 120.6881
  收益率: 0.2007
  距离: 31.6662
  内存使用: 0.6105
  能量使用: 0.9470
  推理时间: 2.4060秒

批次 24:
  奖励值: 148.0090
  收益率: 0.2548
  距离: 38.7419
  内存使用: 0.8268
  能量使用: 1.2132
  推理时间: 2.9559秒

批次 25:
  奖励值: 136.9188
  收益率: 0.2242
  距离: 32.7627
  内存使用: 0.7368
  能量使用: 1.0629
  推理时间: 2.7151秒

批次 26:
  奖励值: 141.3917
  收益率: 0.2395
  距离: 39.8305
  内存使用: 0.8238
  能量使用: 1.1622
  推理时间: 2.8905秒

批次 27:
  奖励值: 136.9010
  收益率: 0.2305
  距离: 34.2644
  内存使用: 0.6910
  能量使用: 1.0426
  推理时间: 2.7519秒

批次 28:
  奖励值: 132.5732
  收益率: 0.2228
  距离: 34.6572
  内存使用: 0.7418
  能量使用: 1.0546
  推理时间: 2.6858秒

批次 29:
  奖励值: 140.6291
  收益率: 0.2405
  距离: 33.7968
  内存使用: 0.6850
  能量使用: 1.0598
  推理时间: 3.0013秒

批次 30:
  奖励值: 113.6291
  收益率: 0.1923
  距离: 26.3293
  内存使用: 0.8998
  能量使用: 0.9127
  推理时间: 2.3491秒

批次 31:
  奖励值: 124.3174
  收益率: 0.2127
  距离: 33.3366
  内存使用: 0.6871
  能量使用: 0.9414
  推理时间: 2.5417秒

批次 32:
  奖励值: 148.7582
  收益率: 0.2467
  距离: 39.7813
  内存使用: 0.7321
  能量使用: 1.1702
  推理时间: 2.9764秒

批次 33:
  奖励值: 147.5545
  收益率: 0.2489
  距离: 37.7235
  内存使用: 0.8014
  能量使用: 1.1661
  推理时间: 2.9748秒

批次 34:
  奖励值: 136.1583
  收益率: 0.2311
  距离: 37.4910
  内存使用: 0.7246
  能量使用: 1.0678
  推理时间: 2.7826秒

批次 35:
  奖励值: 140.7289
  收益率: 0.2327
  距离: 32.1658
  内存使用: 0.7558
  能量使用: 1.0205
  推理时间: 2.7385秒

批次 36:
  奖励值: 150.3318
  收益率: 0.2551
  距离: 39.8661
  内存使用: 0.8266
  能量使用: 1.2437
  推理时间: 3.0431秒

批次 37:
  奖励值: 140.5239
  收益率: 0.2320
  距离: 36.4638
  内存使用: 0.7576
  能量使用: 1.1306
  推理时间: 2.8513秒

批次 38:
  奖励值: 126.2251
  收益率: 0.2094
  距离: 32.6677
  内存使用: 0.6641
  能量使用: 1.0180
  推理时间: 4.0068秒

批次 39:
  奖励值: 146.5544
  收益率: 0.2450
  距离: 36.3655
  内存使用: 0.7955
  能量使用: 1.1300
  推理时间: 2.9132秒

批次 40:
  奖励值: 130.6404
  收益率: 0.2184
  距离: 33.6231
  内存使用: 0.6945
  能量使用: 1.0016
  推理时间: 2.6608秒

批次 41:
  奖励值: 118.2802
  收益率: 0.1959
  距离: 29.5894
  内存使用: 0.8999
  能量使用: 0.9503
  推理时间: 2.4416秒

批次 42:
  奖励值: 132.6734
  收益率: 0.2227
  距离: 35.1320
  内存使用: 0.7871
  能量使用: 1.1341
  推理时间: 2.6862秒

批次 43:
  奖励值: 139.3347
  收益率: 0.2391
  距离: 38.6867
  内存使用: 0.7583
  能量使用: 1.0856
  推理时间: 2.8196秒

批次 44:
  奖励值: 129.8071
  收益率: 0.2204
  距离: 33.0553
  内存使用: 0.7284
  能量使用: 1.0021
  推理时间: 2.8597秒

批次 45:
  奖励值: 129.3754
  收益率: 0.2221
  距离: 33.7485
  内存使用: 0.6188
  能量使用: 1.0818
  推理时间: 2.6677秒

批次 46:
  奖励值: 136.3072
  收益率: 0.2260
  距离: 36.5821
  内存使用: 0.7018
  能量使用: 1.1017
  推理时间: 2.9389秒

批次 47:
  奖励值: 141.9575
  收益率: 0.2419
  距离: 36.8218
  内存使用: 0.7621
  能量使用: 1.1011
  推理时间: 3.0765秒

批次 48:
  奖励值: 142.3326
  收益率: 0.2402
  距离: 38.3946
  内存使用: 0.8202
  能量使用: 1.1199
  推理时间: 2.9428秒

批次 49:
  奖励值: 116.8189
  收益率: 0.1940
  距离: 30.1804
  内存使用: 0.6279
  能量使用: 0.9539
  推理时间: 2.3924秒

批次 50:
  奖励值: 119.8610
  收益率: 0.2046
  距离: 31.8141
  内存使用: 0.8999
  能量使用: 0.9641
  推理时间: 2.5005秒

批次 51:
  奖励值: 127.9706
  收益率: 0.2164
  距离: 32.7204
  内存使用: 0.7240
  能量使用: 1.0144
  推理时间: 2.5963秒

批次 52:
  奖励值: 130.7840
  收益率: 0.2260
  距离: 35.6709
  内存使用: 0.7277
  能量使用: 1.0334
  推理时间: 2.6128秒

批次 53:
  奖励值: 131.5622
  收益率: 0.2163
  距离: 35.0229
  内存使用: 0.6425
  能量使用: 1.0101
  推理时间: 2.6355秒

批次 54:
  奖励值: 130.1132
  收益率: 0.2148
  距离: 30.9148
  内存使用: 0.6346
  能量使用: 1.0241
  推理时间: 2.6304秒

批次 55:
  奖励值: 111.2234
  收益率: 0.1847
  距离: 27.9796
  内存使用: 0.5341
  能量使用: 0.8395
  推理时间: 2.2519秒

批次 56:
  奖励值: 144.7587
  收益率: 0.2443
  距离: 35.7801
  内存使用: 0.7796
  能量使用: 1.1228
  推理时间: 2.9285秒

批次 57:
  奖励值: 126.1914
  收益率: 0.2123
  距离: 32.3795
  内存使用: 0.6784
  能量使用: 0.9593
  推理时间: 2.6125秒

批次 58:
  奖励值: 128.0965
  收益率: 0.2166
  距离: 37.1938
  内存使用: 0.7515
  能量使用: 1.0617
  推理时间: 2.6008秒

批次 59:
  奖励值: 136.4094
  收益率: 0.2254
  距离: 33.0730
  内存使用: 0.7107
  能量使用: 0.9976
  推理时间: 2.7036秒

批次 60:
  奖励值: 144.7256
  收益率: 0.2361
  距离: 32.2007
  内存使用: 0.8060
  能量使用: 1.1421
  推理时间: 2.8393秒

批次 61:
  奖励值: 121.5127
  收益率: 0.2077
  距离: 33.4819
  内存使用: 0.8991
  能量使用: 0.9936
  推理时间: 2.5300秒

批次 62:
  奖励值: 138.7881
  收益率: 0.2344
  距离: 34.7897
  内存使用: 0.7100
  能量使用: 1.1241
  推理时间: 2.7983秒

批次 63:
  奖励值: 122.9230
  收益率: 0.2059
  距离: 30.9326
  内存使用: 0.6652
  能量使用: 0.9561
  推理时间: 2.7206秒

批次 64:
  奖励值: 146.4556
  收益率: 0.2500
  距离: 39.6290
  内存使用: 0.7356
  能量使用: 1.1242
  推理时间: 2.9049秒

批次 65:
  奖励值: 151.5737
  收益率: 0.2482
  距离: 33.6609
  内存使用: 0.7809
  能量使用: 1.2108
  推理时间: 3.0328秒

批次 66:
  奖励值: 142.6023
  收益率: 0.2359
  距离: 39.8212
  内存使用: 0.7763
  能量使用: 1.1980
  推理时间: 3.1212秒

批次 67:
  奖励值: 146.4942
  收益率: 0.2456
  距离: 36.7053
  内存使用: 0.7841
  能量使用: 1.1223
  推理时间: 2.9489秒

批次 68:
  奖励值: 135.5538
  收益率: 0.2256
  距离: 35.4733
  内存使用: 0.7370
  能量使用: 1.0821
  推理时间: 2.7301秒

批次 69:
  奖励值: 143.8797
  收益率: 0.2423
  距离: 35.1464
  内存使用: 0.7306
  能量使用: 1.1634
  推理时间: 2.8003秒

批次 70:
  奖励值: 126.4880
  收益率: 0.2129
  距离: 31.4142
  内存使用: 0.6624
  能量使用: 0.9473
  推理时间: 2.5909秒

批次 71:
  奖励值: 130.1824
  收益率: 0.2206
  距离: 34.1076
  内存使用: 0.6855
  能量使用: 1.0420
  推理时间: 2.6153秒

批次 72:
  奖励值: 151.7809
  收益率: 0.2538
  距离: 40.2366
  内存使用: 0.8821
  能量使用: 1.1195
  推理时间: 3.0481秒

批次 73:
  奖励值: 123.8540
  收益率: 0.2089
  距离: 31.3054
  内存使用: 0.6175
  能量使用: 0.9690
  推理时间: 2.5331秒

批次 74:
  奖励值: 145.5396
  收益率: 0.2446
  距离: 38.1293
  内存使用: 0.8487
  能量使用: 1.1429
  推理时间: 2.8355秒

批次 75:
  奖励值: 130.7336
  收益率: 0.2193
  距离: 32.0874
  内存使用: 0.6092
  能量使用: 1.0362
  推理时间: 2.6186秒

批次 76:
  奖励值: 144.6239
  收益率: 0.2413
  距离: 38.5065
  内存使用: 0.7181
  能量使用: 1.1274
  推理时间: 2.9663秒

批次 77:
  奖励值: 121.0716
  收益率: 0.2054
  距离: 33.8423
  内存使用: 0.6294
  能量使用: 0.9850
  推理时间: 2.5068秒

批次 78:
  奖励值: 133.0961
  收益率: 0.2215
  距离: 31.6559
  内存使用: 0.6934
  能量使用: 1.0584
  推理时间: 2.6585秒

批次 79:
  奖励值: 121.2977
  收益率: 0.2074
  距离: 32.3466
  内存使用: 0.6531
  能量使用: 1.0048
  推理时间: 2.4061秒

批次 80:
  奖励值: 141.9326
  收益率: 0.2426
  距离: 35.6907
  内存使用: 0.7839
  能量使用: 1.0602
  推理时间: 3.0127秒

批次 81:
  奖励值: 120.4647
  收益率: 0.2051
  距离: 31.6622
  内存使用: 0.6365
  能量使用: 0.9719
  推理时间: 2.4304秒

批次 82:
  奖励值: 124.4783
  收益率: 0.2041
  距离: 31.6285
  内存使用: 0.6706
  能量使用: 0.9441
  推理时间: 2.4995秒

批次 83:
  奖励值: 126.1751
  收益率: 0.2075
  距离: 33.5992
  内存使用: 0.6478
  能量使用: 1.0026
  推理时间: 2.5273秒

批次 84:
  奖励值: 139.2236
  收益率: 0.2315
  距离: 35.7310
  内存使用: 0.7473
  能量使用: 1.0745
  推理时间: 2.7765秒

批次 85:
  奖励值: 128.1975
  收益率: 0.2094
  距离: 30.9212
  内存使用: 0.6405
  能量使用: 1.0434
  推理时间: 2.5506秒

批次 86:
  奖励值: 143.2867
  收益率: 0.2385
  距离: 37.9726
  内存使用: 0.8740
  能量使用: 1.1799
  推理时间: 2.8952秒

批次 87:
  奖励值: 135.2056
  收益率: 0.2211
  距离: 32.9412
  内存使用: 0.7005
  能量使用: 1.1470
  推理时间: 2.7295秒

批次 88:
  奖励值: 130.1832
  收益率: 0.2231
  距离: 33.7354
  内存使用: 0.7132
  能量使用: 0.9850
  推理时间: 2.6456秒

批次 89:
  奖励值: 134.9264
  收益率: 0.2265
  距离: 35.6803
  内存使用: 0.7667
  能量使用: 1.1003
  推理时间: 2.6807秒

批次 90:
  奖励值: 133.4671
  收益率: 0.2227
  距离: 37.4433
  内存使用: 0.6769
  能量使用: 1.1455
  推理时间: 2.6700秒

批次 91:
  奖励值: 147.2873
  收益率: 0.2479
  距离: 34.7718
  内存使用: 0.7746
  能量使用: 1.0789
  推理时间: 2.9244秒

批次 92:
  奖励值: 137.1587
  收益率: 0.2274
  距离: 32.6017
  内存使用: 0.8397
  能量使用: 1.0411
  推理时间: 2.6927秒

批次 93:
  奖励值: 144.0476
  收益率: 0.2399
  距离: 33.1662
  内存使用: 0.8063
  能量使用: 1.1322
  推理时间: 2.8121秒

批次 94:
  奖励值: 140.3827
  收益率: 0.2345
  距离: 36.5666
  内存使用: 0.8006
  能量使用: 1.0675
  推理时间: 2.8360秒

批次 95:
  奖励值: 107.0907
  收益率: 0.1803
  距离: 27.1209
  内存使用: 0.5779
  能量使用: 0.7197
  推理时间: 2.2957秒

批次 96:
  奖励值: 132.1665
  收益率: 0.2223
  距离: 33.7227
  内存使用: 0.7140
  能量使用: 1.0304
  推理时间: 2.6993秒

批次 97:
  奖励值: 145.4728
  收益率: 0.2421
  距离: 35.1093
  内存使用: 0.8222
  能量使用: 1.1229
  推理时间: 2.8320秒

批次 98:
  奖励值: 139.6014
  收益率: 0.2342
  距离: 36.0313
  内存使用: 0.7972
  能量使用: 1.1685
  推理时间: 2.7809秒

批次 99:
  奖励值: 134.3443
  收益率: 0.2295
  距离: 36.6105
  内存使用: 0.6676
  能量使用: 1.0333
  推理时间: 2.6675秒

批次 100:
  奖励值: 148.5354
  收益率: 0.2482
  距离: 36.2752
  内存使用: 0.7337
  能量使用: 1.1202
  推理时间: 2.9123秒


==================== 总结 ====================
平均收益率: 0.2258
平均能量使用: 1.0568
平均推理时间: 2.7460秒
