推理数据数量: 100
每个序列任务数量: 2000
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 132.8461
  收益率: 0.1655
  距离: 28.9177
  内存使用: 0.6231
  能量使用: 0.9670
  推理时间: 2.5046秒

批次 2:
  奖励值: 134.8391
  收益率: 0.1695
  距离: 32.6786
  内存使用: 0.6536
  能量使用: 0.9895
  推理时间: 2.5512秒

批次 3:
  奖励值: 149.2885
  收益率: 0.1847
  距离: 33.0766
  内存使用: 0.7171
  能量使用: 1.0293
  推理时间: 2.7320秒

批次 4:
  奖励值: 144.9333
  收益率: 0.1785
  距离: 35.2838
  内存使用: 0.8191
  能量使用: 1.0302
  推理时间: 2.5988秒

批次 5:
  奖励值: 146.0714
  收益率: 0.1853
  距离: 34.9542
  内存使用: 0.7106
  能量使用: 1.1924
  推理时间: 2.7115秒

批次 6:
  奖励值: 132.6327
  收益率: 0.1637
  距离: 30.0987
  内存使用: 0.6273
  能量使用: 1.0086
  推理时间: 2.4365秒

批次 7:
  奖励值: 157.0574
  收益率: 0.1943
  距离: 37.0689
  内存使用: 0.8090
  能量使用: 1.2072
  推理时间: 2.8358秒

批次 8:
  奖励值: 128.7176
  收益率: 0.1620
  距离: 34.2146
  内存使用: 0.6181
  能量使用: 0.9321
  推理时间: 2.4665秒

批次 9:
  奖励值: 132.3107
  收益率: 0.1634
  距离: 32.6330
  内存使用: 0.6401
  能量使用: 0.9053
  推理时间: 2.4688秒

批次 10:
  奖励值: 127.4390
  收益率: 0.1592
  距离: 29.9873
  内存使用: 0.6102
  能量使用: 0.9244
  推理时间: 2.3993秒

批次 11:
  奖励值: 152.6168
  收益率: 0.1948
  距离: 37.8731
  内存使用: 0.8393
  能量使用: 1.0540
  推理时间: 2.8300秒

批次 12:
  奖励值: 122.3745
  收益率: 0.1539
  距离: 30.1655
  内存使用: 0.6084
  能量使用: 0.8770
  推理时间: 2.2250秒

批次 13:
  奖励值: 135.6485
  收益率: 0.1681
  距离: 30.8676
  内存使用: 0.6390
  能量使用: 0.9962
  推理时间: 2.5034秒

批次 14:
  奖励值: 161.2289
  收益率: 0.2002
  距离: 35.0678
  内存使用: 0.8610
  能量使用: 1.2133
  推理时间: 2.9722秒

批次 15:
  奖励值: 130.4937
  收益率: 0.1617
  距离: 30.0772
  内存使用: 0.6260
  能量使用: 0.9093
  推理时间: 2.3940秒

批次 16:
  奖励值: 128.2243
  收益率: 0.1595
  距离: 32.1641
  内存使用: 0.6208
  能量使用: 0.9664
  推理时间: 2.3592秒

批次 17:
  奖励值: 139.4969
  收益率: 0.1752
  距离: 35.8378
  内存使用: 0.6085
  能量使用: 0.9598
  推理时间: 2.6466秒

批次 18:
  奖励值: 138.7758
  收益率: 0.1725
  距离: 31.3687
  内存使用: 0.7477
  能量使用: 1.1319
  推理时间: 2.5308秒

批次 19:
  奖励值: 136.3271
  收益率: 0.1694
  距离: 29.1310
  内存使用: 0.6211
  能量使用: 0.9937
  推理时间: 2.4873秒

批次 20:
  奖励值: 127.3477
  收益率: 0.1597
  距离: 28.3665
  内存使用: 0.5854
  能量使用: 0.9526
  推理时间: 2.5642秒

批次 21:
  奖励值: 117.5667
  收益率: 0.1483
  距离: 26.6770
  内存使用: 0.5127
  能量使用: 0.8768
  推理时间: 2.2475秒

批次 22:
  奖励值: 136.9080
  收益率: 0.1739
  距离: 32.3068
  内存使用: 0.6745
  能量使用: 1.0027
  推理时间: 2.5050秒

批次 23:
  奖励值: 133.1631
  收益率: 0.1651
  距离: 32.4258
  内存使用: 0.6267
  能量使用: 1.0212
  推理时间: 2.5707秒

批次 24:
  奖励值: 134.7714
  收益率: 0.1669
  距离: 32.0540
  内存使用: 0.6713
  能量使用: 0.9318
  推理时间: 2.4419秒

批次 25:
  奖励值: 131.7922
  收益率: 0.1634
  距离: 29.7973
  内存使用: 0.6187
  能量使用: 0.9995
  推理时间: 2.3748秒

批次 26:
  奖励值: 130.9576
  收益率: 0.1653
  距离: 29.6982
  内存使用: 0.6440
  能量使用: 0.9425
  推理时间: 2.4805秒

批次 27:
  奖励值: 142.8421
  收益率: 0.1775
  距离: 33.4639
  内存使用: 0.7169
  能量使用: 1.1348
  推理时间: 2.5210秒

批次 28:
  奖励值: 136.4038
  收益率: 0.1656
  距离: 28.9282
  内存使用: 0.6189
  能量使用: 1.0453
  推理时间: 2.4876秒

批次 29:
  奖励值: 156.1482
  收益率: 0.1964
  距离: 37.7843
  内存使用: 0.7730
  能量使用: 1.1462
  推理时间: 2.8073秒

批次 30:
  奖励值: 137.1438
  收益率: 0.1688
  距离: 29.8587
  内存使用: 0.6943
  能量使用: 0.9593
  推理时间: 2.5698秒

批次 31:
  奖励值: 131.8398
  收益率: 0.1662
  距离: 32.6752
  内存使用: 0.6540
  能量使用: 0.9911
  推理时间: 2.4469秒

批次 32:
  奖励值: 133.6525
  收益率: 0.1663
  距离: 31.8108
  内存使用: 0.6476
  能量使用: 1.0365
  推理时间: 2.5549秒

批次 33:
  奖励值: 140.9382
  收益率: 0.1759
  距离: 33.4123
  内存使用: 0.7009
  能量使用: 1.0103
  推理时间: 2.7229秒

批次 34:
  奖励值: 139.1552
  收益率: 0.1718
  距离: 31.7015
  内存使用: 0.7527
  能量使用: 1.0354
  推理时间: 2.5347秒

批次 35:
  奖励值: 131.4933
  收益率: 0.1606
  距离: 30.4656
  内存使用: 0.5912
  能量使用: 0.9565
  推理时间: 2.4084秒

批次 36:
  奖励值: 138.7031
  收益率: 0.1708
  距离: 31.9369
  内存使用: 0.6646
  能量使用: 0.9922
  推理时间: 2.4439秒

批次 37:
  奖励值: 136.8879
  收益率: 0.1713
  距离: 29.3467
  内存使用: 0.6714
  能量使用: 1.0060
  推理时间: 2.4225秒

批次 38:
  奖励值: 130.7148
  收益率: 0.1654
  距离: 29.2245
  内存使用: 0.6161
  能量使用: 0.9299
  推理时间: 2.3602秒

批次 39:
  奖励值: 135.8100
  收益率: 0.1712
  距离: 33.1934
  内存使用: 0.6188
  能量使用: 0.9677
  推理时间: 2.4401秒

批次 40:
  奖励值: 135.1424
  收益率: 0.1643
  距离: 30.8802
  内存使用: 0.6643
  能量使用: 0.9706
  推理时间: 2.3941秒

批次 41:
  奖励值: 133.4980
  收益率: 0.1667
  距离: 33.7434
  内存使用: 0.6371
  能量使用: 1.0234
  推理时间: 2.5177秒

批次 42:
  奖励值: 139.7461
  收益率: 0.1725
  距离: 31.5959
  内存使用: 0.6748
  能量使用: 1.0370
  推理时间: 2.5965秒

批次 43:
  奖励值: 131.2630
  收益率: 0.1632
  距离: 33.1900
  内存使用: 0.6791
  能量使用: 1.0048
  推理时间: 2.3693秒

批次 44:
  奖励值: 151.6187
  收益率: 0.1876
  距离: 33.6454
  内存使用: 0.7707
  能量使用: 1.1466
  推理时间: 2.8084秒

批次 45:
  奖励值: 141.5101
  收益率: 0.1748
  距离: 31.8404
  内存使用: 0.6749
  能量使用: 1.0645
  推理时间: 2.5535秒

批次 46:
  奖励值: 128.5257
  收益率: 0.1664
  距离: 33.1131
  内存使用: 0.8997
  能量使用: 0.9358
  推理时间: 2.8439秒

批次 47:
  奖励值: 134.6707
  收益率: 0.1645
  距离: 28.6684
  内存使用: 0.6210
  能量使用: 0.9692
  推理时间: 2.5164秒

批次 48:
  奖励值: 129.0035
  收益率: 0.1586
  距离: 27.6903
  内存使用: 0.6900
  能量使用: 1.0126
  推理时间: 2.3484秒

批次 49:
  奖励值: 141.0838
  收益率: 0.1763
  距离: 30.7543
  内存使用: 0.6721
  能量使用: 1.0058
  推理时间: 2.4856秒

批次 50:
  奖励值: 152.6308
  收益率: 0.1898
  距离: 36.8889
  内存使用: 0.7890
  能量使用: 1.0605
  推理时间: 2.8971秒

批次 51:
  奖励值: 133.3190
  收益率: 0.1681
  距离: 31.1838
  内存使用: 0.6195
  能量使用: 0.9586
  推理时间: 2.5226秒

批次 52:
  奖励值: 149.9487
  收益率: 0.1835
  距离: 34.0944
  内存使用: 0.7372
  能量使用: 1.0710
  推理时间: 2.7811秒

批次 53:
  奖励值: 130.4058
  收益率: 0.1616
  距离: 29.5734
  内存使用: 0.6924
  能量使用: 0.9226
  推理时间: 2.3933秒

批次 54:
  奖励值: 141.5865
  收益率: 0.1768
  距离: 31.3097
  内存使用: 0.6214
  能量使用: 1.0957
  推理时间: 2.6425秒

批次 55:
  奖励值: 138.3263
  收益率: 0.1756
  距离: 37.0591
  内存使用: 0.6847
  能量使用: 1.0037
  推理时间: 2.6262秒

批次 56:
  奖励值: 131.6123
  收益率: 0.1633
  距离: 28.9143
  内存使用: 0.7173
  能量使用: 0.9435
  推理时间: 2.4729秒

批次 57:
  奖励值: 143.8559
  收益率: 0.1778
  距离: 31.9254
  内存使用: 0.7234
  能量使用: 1.0394
  推理时间: 2.6418秒

批次 58:
  奖励值: 134.4801
  收益率: 0.1668
  距离: 30.5885
  内存使用: 0.6725
  能量使用: 0.9788
  推理时间: 2.5780秒

批次 59:
  奖励值: 156.3311
  收益率: 0.1949
  距离: 35.2487
  内存使用: 0.8003
  能量使用: 1.0887
  推理时间: 2.8356秒

批次 60:
  奖励值: 143.9316
  收益率: 0.1818
  距离: 32.0678
  内存使用: 0.6415
  能量使用: 1.0274
  推理时间: 2.6985秒

批次 61:
  奖励值: 128.6691
  收益率: 0.1597
  距离: 30.8849
  内存使用: 0.6279
  能量使用: 0.9489
  推理时间: 2.2965秒

批次 62:
  奖励值: 145.7303
  收益率: 0.1799
  距离: 34.9343
  内存使用: 0.6847
  能量使用: 1.1204
  推理时间: 2.6968秒

批次 63:
  奖励值: 141.5191
  收益率: 0.1770
  距离: 35.0349
  内存使用: 0.6739
  能量使用: 1.0529
  推理时间: 2.6577秒

批次 64:
  奖励值: 125.3519
  收益率: 0.1576
  距离: 29.9882
  内存使用: 0.5772
  能量使用: 0.9308
  推理时间: 2.3170秒

批次 65:
  奖励值: 146.4614
  收益率: 0.1757
  距离: 31.6920
  内存使用: 0.7185
  能量使用: 1.0666
  推理时间: 2.7714秒

批次 66:
  奖励值: 131.1070
  收益率: 0.1641
  距离: 29.1706
  内存使用: 0.6193
  能量使用: 0.9960
  推理时间: 2.4707秒

批次 67:
  奖励值: 133.7205
  收益率: 0.1658
  距离: 30.0895
  内存使用: 0.6430
  能量使用: 1.0264
  推理时间: 2.4583秒

批次 68:
  奖励值: 140.5954
  收益率: 0.1718
  距离: 28.1358
  内存使用: 0.6609
  能量使用: 1.0124
  推理时间: 2.4874秒

批次 69:
  奖励值: 120.3182
  收益率: 0.1514
  距离: 28.7569
  内存使用: 0.5929
  能量使用: 0.8139
  推理时间: 2.2408秒

批次 70:
  奖励值: 133.2442
  收益率: 0.1637
  距离: 31.3490
  内存使用: 0.6510
  能量使用: 0.9114
  推理时间: 2.4361秒

批次 71:
  奖励值: 125.6237
  收益率: 0.1563
  距离: 30.9625
  内存使用: 0.6182
  能量使用: 0.9718
  推理时间: 2.3854秒

批次 72:
  奖励值: 137.0576
  收益率: 0.1685
  距离: 31.7573
  内存使用: 0.6820
  能量使用: 1.0454
  推理时间: 2.5998秒

批次 73:
  奖励值: 152.6094
  收益率: 0.1892
  距离: 35.6791
  内存使用: 0.7827
  能量使用: 1.1192
  推理时间: 2.8573秒

批次 74:
  奖励值: 145.8844
  收益率: 0.1793
  距离: 32.6441
  内存使用: 0.6925
  能量使用: 1.0945
  推理时间: 2.6890秒

批次 75:
  奖励值: 129.0492
  收益率: 0.1602
  距离: 30.3169
  内存使用: 0.6694
  能量使用: 0.9153
  推理时间: 2.4919秒

批次 76:
  奖励值: 136.8074
  收益率: 0.1700
  距离: 31.1270
  内存使用: 0.6653
  能量使用: 0.9928
  推理时间: 2.5167秒

批次 77:
  奖励值: 140.9039
  收益率: 0.1742
  距离: 30.0241
  内存使用: 0.7380
  能量使用: 1.0300
  推理时间: 2.5315秒

批次 78:
  奖励值: 130.1169
  收益率: 0.1651
  距离: 31.2338
  内存使用: 0.6183
  能量使用: 0.9139
  推理时间: 2.4559秒

批次 79:
  奖励值: 151.5080
  收益率: 0.1926
  距离: 35.3476
  内存使用: 0.7092
  能量使用: 1.1270
  推理时间: 2.8234秒

批次 80:
  奖励值: 130.8461
  收益率: 0.1635
  距离: 31.7935
  内存使用: 0.6143
  能量使用: 0.9000
  推理时间: 2.5204秒

批次 81:
  奖励值: 138.3363
  收益率: 0.1722
  距离: 31.1797
  内存使用: 0.6535
  能量使用: 0.8942
  推理时间: 2.5242秒

批次 82:
  奖励值: 142.9878
  收益率: 0.1771
  距离: 31.2923
  内存使用: 0.6291
  能量使用: 1.1029
  推理时间: 2.5624秒

批次 83:
  奖励值: 132.4109
  收益率: 0.1650
  距离: 32.1532
  内存使用: 0.6614
  能量使用: 0.9635
  推理时间: 2.4842秒

批次 84:
  奖励值: 132.2296
  收益率: 0.1651
  距离: 31.2997
  内存使用: 0.6009
  能量使用: 0.8402
  推理时间: 2.4114秒

批次 85:
  奖励值: 137.3777
  收益率: 0.1711
  距离: 30.8141
  内存使用: 0.6371
  能量使用: 0.9933
  推理时间: 2.4389秒

批次 86:
  奖励值: 130.4233
  收益率: 0.1598
  距离: 29.8121
  内存使用: 0.6065
  能量使用: 0.9347
  推理时间: 2.4356秒

批次 87:
  奖励值: 123.6607
  收益率: 0.1547
  距离: 28.1152
  内存使用: 0.9000
  能量使用: 0.8635
  推理时间: 2.4069秒

批次 88:
  奖励值: 158.0115
  收益率: 0.1910
  距离: 33.4933
  内存使用: 0.7306
  能量使用: 1.1531
  推理时间: 2.7970秒

批次 89:
  奖励值: 152.8266
  收益率: 0.1864
  距离: 36.1031
  内存使用: 0.8476
  能量使用: 1.1961
  推理时间: 2.8502秒

批次 90:
  奖励值: 135.3764
  收益率: 0.1700
  距离: 35.2542
  内存使用: 0.6059
  能量使用: 0.9870
  推理时间: 2.5328秒

批次 91:
  奖励值: 128.6635
  收益率: 0.1577
  距离: 27.2911
  内存使用: 0.6046
  能量使用: 0.9774
  推理时间: 2.2953秒

批次 92:
  奖励值: 137.7706
  收益率: 0.1686
  距离: 32.7996
  内存使用: 0.6667
  能量使用: 0.9924
  推理时间: 2.6745秒

批次 93:
  奖励值: 130.7932
  收益率: 0.1596
  距离: 28.4243
  内存使用: 0.6626
  能量使用: 0.9267
  推理时间: 2.4267秒

批次 94:
  奖励值: 144.8865
  收益率: 0.1836
  距离: 35.6406
  内存使用: 0.7095
  能量使用: 1.0768
  推理时间: 2.6320秒

批次 95:
  奖励值: 129.1234
  收益率: 0.1613
  距离: 28.8334
  内存使用: 0.6454
  能量使用: 0.8744
  推理时间: 2.3968秒

批次 96:
  奖励值: 130.9245
  收益率: 0.1651
  距离: 32.7010
  内存使用: 0.6038
  能量使用: 1.0461
  推理时间: 2.4360秒

批次 97:
  奖励值: 141.9164
  收益率: 0.1783
  距离: 34.9063
  内存使用: 0.7505
  能量使用: 1.1104
  推理时间: 2.6277秒

批次 98:
  奖励值: 135.1288
  收益率: 0.1691
  距离: 34.8823
  内存使用: 0.6246
  能量使用: 1.0354
  推理时间: 2.5047秒

批次 99:
  奖励值: 136.8681
  收益率: 0.1717
  距离: 35.1928
  内存使用: 0.5862
  能量使用: 1.0846
  推理时间: 2.5883秒

批次 100:
  奖励值: 145.7752
  收益率: 0.1827
  距离: 34.6690
  内存使用: 0.7143
  能量使用: 1.0663
  推理时间: 2.6593秒


==================== 总结 ====================
平均收益率: 0.1708
平均能量使用: 1.0069
平均推理时间: 2.5447秒
