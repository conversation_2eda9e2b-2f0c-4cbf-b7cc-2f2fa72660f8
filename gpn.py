"""
define model
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from indrnn.indrnn import IndRNN, IndRNN_Net, IndRNNv2
from attention.attention import MultiHead_Additive_Attention, Attention

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device_num = 0
if device == 'cuda':
    torch.cuda.set_device(device_num)
    # 启用cudnn自动优化，提升计算性能
    torch.backends.cudnn.benchmark = True


class Encoder(nn.Module):
    def __init__(self, input_size, hidden_size):
        super(Encoder, self).__init__()
        self.conv = nn.Conv1d(input_size, hidden_size, kernel_size=1)
        # 添加批量归一化层提高训练稳定性和收敛速度
        self.batch_norm = nn.BatchNorm1d(hidden_size)
        # 添加GELU激活函数，性能通常优于ReLU
        self.activation = nn.GELU()
        # 添加残差连接的投影层
        self.res_proj = nn.Conv1d(input_size, hidden_size, kernel_size=1) if input_size != hidden_size else nn.Identity()

    def forward(self, input_seq):
        # input: (batch_size, input_size, seq_len)
        # output: (batch_size, hidden_size, seq_len)
        
        # 保存输入用于残差连接
        identity = input_seq
        
        # 主路径
        output = self.conv(input_seq)
        output = self.batch_norm(output)
        output = self.activation(output)
        
        # 残差连接
        if not isinstance(self.res_proj, nn.Identity):
            identity = self.res_proj(identity)
        
        # 合并主路径和残差连接
        output = output + identity
        
        return output


class LSTM(nn.Module):
    def __init__(self, n_hidden, dropout=0.1):
        super(LSTM, self).__init__()
        
        # 使用单个矩阵进行所有门计算，提高计算效率
        self.W_gates = nn.Linear(n_hidden, 4 * n_hidden)
        self.U_gates = nn.Linear(n_hidden, 4 * n_hidden)
        
        # 添加层归一化，提高训练稳定性
        self.ln_input = nn.LayerNorm(4 * n_hidden)
        self.ln_hidden = nn.LayerNorm(4 * n_hidden)
        self.ln_cell = nn.LayerNorm(n_hidden)
        self.ln_output = nn.LayerNorm(n_hidden)
        
        # 添加dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化参数
        nn.init.orthogonal_(self.W_gates.weight)
        nn.init.orthogonal_(self.U_gates.weight)
        
        # 将遗忘门偏置初始化为1，有助于长序列训练
        self.W_gates.bias.data[n_hidden:2*n_hidden].fill_(1.0)

    def forward(self, x, h, c):  # query and reference
        # 合并所有门的计算，提高计算效率
        gates_x = self.W_gates(x)
        gates_h = self.U_gates(h)
        
        # 应用层归一化
        gates_x = self.ln_input(gates_x)
        gates_h = self.ln_hidden(gates_h)
        
        # 合并输入和隐藏状态的门
        gates = gates_x + gates_h
        
        # 分离各个门
        i_gate, f_gate, c_tilde, o_gate = gates.chunk(4, 1)
        
        # 应用激活函数
        i = torch.sigmoid(i_gate)
        f = torch.sigmoid(f_gate)
        c_tilde = torch.tanh(c_tilde)
        o = torch.sigmoid(o_gate)
        
        # 更新单元状态
        c_new = f * c + i * c_tilde
        
        # 应用层归一化到单元状态
        c_new = self.ln_cell(c_new)
        
        # 更新隐藏状态
        h_new = o * torch.tanh(c_new)
        
        # 应用层归一化到输出
        h_new = self.ln_output(h_new)
        
        # 应用dropout
        h_new = self.dropout(h_new)
        
        return h_new, c_new


class GPN(nn.Module):
    def __init__(self, n_hidden, rnn, num_nodes, dropout=0.1, n_head=8, num_layers=1):
        super(GPN, self).__init__()
        self.size = 0
        self.batch_size = 0
        self.dim = n_hidden
        self.rnn = rnn
        self.dropout_rate = dropout
        
        # 添加层归一化，提高训练稳定性
        self.layer_norm1 = nn.LayerNorm(n_hidden)
        self.layer_norm2 = nn.LayerNorm(n_hidden)
        self.layer_norm3 = nn.LayerNorm(n_hidden)
        self.layer_norm_out = nn.LayerNorm(n_hidden)
        
        # 添加dropout
        self.dropout = nn.Dropout(dropout)
        
        if self.rnn == 'indrnn':
            self.indrnn = IndRNN_Net(n_hidden, n_hidden, num_nodes, num_layers, IndRNN)
            # 使用多头注意力机制提升性能
            self.pointer = MultiHead_Additive_Attention(n_hidden, n_head=n_head)
            self.drop_rnn = nn.Dropout(p=dropout)
            self.drop_hh = nn.Dropout(p=dropout)

        elif self.rnn == 'indrnnv2':
            self.indrnn = IndRNN_Net(n_hidden, n_hidden, num_nodes, num_layers, IndRNNv2)
            # 使用多头注意力机制提升性能
            self.pointer = MultiHead_Additive_Attention(n_hidden, n_head=n_head)
            self.drop_rnn = nn.Dropout(p=dropout)
            self.drop_hh = nn.Dropout(p=dropout)

        elif self.rnn == 'lstm':
            # 使用标准PyTorch LSTM实现，提高计算效率
            self.lstm0 = nn.LSTM(n_hidden, n_hidden, batch_first=True, dropout=dropout)
            # 使用多头注意力机制提升性能
            self.pointer = MultiHead_Additive_Attention(n_hidden, n_head=n_head)
            # 使用优化的LSTM实现
            self.encoder = LSTM(n_hidden, dropout=dropout)

        # 初始化参数使用正交初始化，提高训练稳定性
        h0 = torch.FloatTensor(n_hidden).to(device)
        c0 = torch.FloatTensor(n_hidden).to(device)
        alpha = torch.ones(1).to(device)

        self.h0 = nn.Parameter(h0)
        self.c0 = nn.Parameter(c0)
        self.alpha = nn.Parameter(alpha)

        # 使用Kaiming初始化
        nn.init.kaiming_uniform_(self.h0.view(1, -1))
        nn.init.kaiming_uniform_(self.c0.view(1, -1))

        # GNN系数参数
        r1 = torch.ones(1).to(device)
        r2 = torch.ones(1).to(device)
        r3 = torch.ones(1).to(device)
        self.r1 = nn.Parameter(r1)
        self.r2 = nn.Parameter(r2)
        self.r3 = nn.Parameter(r3)

        # 权重矩阵
        self.W1 = nn.Linear(n_hidden, n_hidden)
        self.W2 = nn.Linear(n_hidden, n_hidden)
        self.W3 = nn.Linear(n_hidden, n_hidden)

        # 聚合函数
        self.agg_1 = nn.Linear(n_hidden, n_hidden)
        self.agg_2 = nn.Linear(n_hidden, n_hidden)
        self.agg_3 = nn.Linear(n_hidden, n_hidden)
        
        # 添加残差连接投影层
        self.res_proj = nn.Linear(n_hidden, n_hidden)
        
        # 初始化所有线性层
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_uniform_(m.weight, a=math.sqrt(5))
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x, context, last_hh, h=None, c=None, latent=None):
        '''
        Inputs (B: batch size, length: seq_len, dim: hidden dimension)

        x: current tw(time window)  (B , hidden_size)
        X_all: all tw information (B, length, hidden_size)

        h: hidden variable (B, dim)
        c: cell gate (B, dim)
        latent: latent pointer vector from previous layer (B, length, dim)

        Outputs

        softmax: probability distribution of next city (B, size)
        h: hidden variable (B, dim)
        c: cell gate (B, dim)
        latent_u: latent pointer vector for next layer
        '''

        self.batch_size = context.size(0)
        self.seq_len = context.size(1)

        if self.rnn == 'lstm':
            first_turn = False
            if h is None or c is None:
                first_turn = True

            if first_turn:
                # (dim) -> (B, dim)
                # 确保隐藏状态在正确的设备上
                device = context.device
                h0 = self.h0.to(device).unsqueeze(0).expand(self.batch_size, self.dim)
                c0 = self.c0.to(device).unsqueeze(0).expand(self.batch_size, self.dim)

                h0 = h0.unsqueeze(0).contiguous()
                c0 = c0.unsqueeze(0).contiguous()

                # 使用batch_first=True，提高代码可读性
                # input_context: (B, length, n_hidden)
                input_context = context
                _, (h_enc, c_enc) = self.lstm0(input_context, (h0, c0))

                # let h0, c0 be the hidden variable of first turn
                # h: (B, n_hidden)
                h = h_enc.squeeze(0)
                c = c_enc.squeeze(0)

        # =============================
        # graph neural network encoder
        # =============================
        
        # 保存原始context用于残差连接
        context_orig = context.permute(0, 2, 1).contiguous().view(-1, self.dim)
        
        # (B*length, dim)
        context = context.permute(0, 2, 1)
        context = context.contiguous().view(-1, self.dim)
        
        # 第一层GNN带层归一化和残差连接
        context_res = context
        context = self.r1 * self.W1(context) + (1 - self.r1) * F.gelu(self.agg_1(context))  # 使用GELU替代ReLU
        context = self.dropout(context)  # 添加dropout
        context = self.layer_norm1(context + context_res)

        # 第二层GNN带层归一化和残差连接
        context_res = context
        context = self.r2 * self.W2(context) + (1 - self.r2) * F.gelu(self.agg_2(context))  # 使用GELU替代ReLU
        context = self.dropout(context)  # 添加dropout
        context = self.layer_norm2(context + context_res)

        # 第三层GNN带层归一化和残差连接
        context_res = context
        context = self.r3 * self.W3(context) + (1 - self.r3) * F.gelu(self.agg_3(context))  # 使用GELU替代ReLU
        context = self.dropout(context)  # 添加dropout
        context = self.layer_norm3(context + context_res)
        
        # 添加全局残差连接
        context = context + self.res_proj(context_orig)
        context = self.layer_norm_out(context)

        # =============================
        # process hidden variable
        # =============================

        if self.rnn == 'lstm':
            # LSTM encoder
            # x:(B,n_hidden)
            # h:(B,n_hidden)
            # c:(B,n_hidden)
            h, c = self.encoder(x, h, c)

            # query vector
            q = h

            # pointer
            u, _ = self.pointer(q, context)
            latent_u = u.clone()

            # u:(batch_size,seq_len)
            u = 10 * torch.tanh(u)

            if latent is not None:
                u += self.alpha * latent
            return u, h, c, latent_u

        elif self.rnn == 'indrnn' or self.rnn == 'indrnnv2':
            # static_hidden: (batch_size, hidden_size, seq_len)
            # dynamic_hidden: (batch_size, hidden_size, seq_len)
            # decoder_hidden: (batch_size, hidden_size, 1)
            # rnn_out: (batch_size, 1, hidden_size)
            # last_hh: (1, batch_size, hidden_size)
            x = x.unsqueeze(2)
            rnn_out, last_hh = self.indrnn(x.transpose(2, 1), last_hh)
            # rnn_out: (batch_size, hidden_size)
            rnn_out = self.drop_rnn(rnn_out.squeeze(1))
            probs, _ = self.pointer(rnn_out, context)
            probs = 10 * torch.tanh(probs)
            return probs, last_hh


class GPN4SMP(nn.Module):
    def __init__(self, static_size, dynamic_size, hidden_size, task, rnn, num_layers,
                 update_fn=None, mask_fn=None, num_nodes=50, dropout=0.1):
        super(GPN4SMP, self).__init__()
        if dynamic_size < 1:
            raise ValueError(':param dynamic_size: must be > 0, even if the '
                             'problem has no dynamic elements')

        self.element_size = static_size + dynamic_size
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.task = task
        self.rnn = rnn
        self.num_layers = num_layers
        self.dropout = dropout

        # encoder = GPN
        self.element_encoder = Encoder(self.element_size, hidden_size)
        
        # 使用Kaiming初始化参数
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.kaiming_uniform_(p, a=math.sqrt(5))
                
        if self.rnn == 'indrnn':
            self.decoder_low = GPN(hidden_size, 'indrnn', num_nodes, dropout=dropout, num_layers=self.num_layers)
        elif self.rnn == 'indrnnv2':
            self.decoder_low = GPN(hidden_size, 'indrnnv2', num_nodes, dropout=dropout, num_layers=self.num_layers)
        elif self.rnn == 'lstm':
            self.decoder_low = GPN(hidden_size, 'lstm', num_nodes, dropout=dropout)
            self.decoder_high = GPN(hidden_size, 'lstm', num_nodes, dropout=dropout)
        
        # 添加层归一化
        self.layer_norm = nn.LayerNorm(hidden_size)
        
        print(task, num_nodes)

    def forward(self, static, dynamic):
        # static: (batch_size, static_size, seq_len)
        # dynamic: (batch_size, dynamic_size, seq_len)
        # element: (batch_size, static_size+dynamic_size, seq_len)
        batch_size, input_size, seq_len = static.size()

        element = torch.cat((static, dynamic), dim=1)
        # mask: (batch_size, seq_len)
        mask = torch.ones(batch_size, seq_len, device=device)

        if self.task == 'single_smp':
            mask[:, 1:] = 0.
        tour_idx, tour_logp = [], []
        max_steps = seq_len if self.mask_fn is None else 10000
        
        # 缓存变量，避免重复创建
        h = None
        c = None
        last_hh = None

        for step in range(max_steps):
            # Satisfying Termination Conditions and break
            if not mask.byte().any():
                break

            if step == 0:
                # x:(batch_size,hidden_size)
                x = element[:, :, 0]
            else:
                x = ptr.unsqueeze(1).unsqueeze(2).expand(-1, self.element_size, -1)
                x = torch.gather(element, 2, x)
                x = x.squeeze(2)

            # 编码当前状态
            x = x.unsqueeze(2)
            x_hidden = self.element_encoder(x)
            x_hidden = x_hidden.squeeze(2)
            
            # 编码全局信息
            element_hidden = self.element_encoder(element)
            
            # 应用层归一化
            x_hidden = self.layer_norm(x_hidden)

            if self.rnn == 'indrnn' or self.rnn == 'indrnnv2':
                # probs: (batch_size,seq_len)
                probs, last_hh = self.decoder_low(x=x_hidden, context=element_hidden, last_hh=last_hh)
                # 使用log_softmax和mask_fill提高数值稳定性
                log_probs = F.log_softmax(probs, dim=1)
                log_probs = log_probs.masked_fill(mask == 0, -1e9)
                probs = torch.exp(log_probs)

            elif self.rnn == 'lstm':
                # probs: (batch_size,seq_len)
                probs, h, c, latent_u = self.decoder_low(x=x_hidden, context=element_hidden, h=h, c=c, last_hh=None)
                probs, h, c, _ = self.decoder_high(x=x_hidden, context=element_hidden, h=h, c=c, latent=latent_u,
                                                   last_hh=None)
                # 使用log_softmax和mask_fill提高数值稳定性
                log_probs = F.log_softmax(probs, dim=1)
                log_probs = log_probs.masked_fill(mask == 0, -1e9)
                probs = torch.exp(log_probs)

            # 选择节点
            if self.training:
                m = torch.distributions.Categorical(probs)
                ptr = m.sample()
                # 使用更高效的掩码处理方式
                valid_mask = torch.gather(mask, 1, ptr.data.unsqueeze(1)).squeeze(1)
                while not valid_mask.all():
                    invalid_idx = ~valid_mask
                    new_ptr = m.sample()
                    ptr = ptr.masked_scatter(invalid_idx, new_ptr[invalid_idx])
                    valid_mask = torch.gather(mask, 1, ptr.data.unsqueeze(1)).squeeze(1)
                logp = m.log_prob(ptr)
            else:
                # 测试时使用贪婪策略
                prob, ptr = torch.max(probs, 1)
                logp = prob.log()

            # 更新动态信息和掩码
            if self.task == 'single_smp':
                dynamic = self.update_fn(static, dynamic, ptr.data)
                element = torch.cat((static, dynamic), dim=1)
                
                # 检查是否完成
                is_done = dynamic[:, 1].sum(1).eq(0).float()
                logp = logp * (1. - is_done)
                
            # 更新掩码
            mask = self.mask_fn(dynamic).detach()

            # 保存结果
            tour_logp.append(logp.unsqueeze(1))
            tour_idx.append(ptr.data.unsqueeze(1))

        # 合并结果
        tour_idx = torch.cat(tour_idx, dim=1)
        tour_logp = torch.cat(tour_logp, dim=1)
        return tour_idx, tour_logp


class MultiHead_Additive_Attention(nn.Module):
    """MultiHead Additive Attention with Glimpse Mechanism"""

    def __init__(self, hidden_size, n_head=8, dropout=0.1, device=None):
        super(MultiHead_Additive_Attention, self).__init__()
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
        self.hidden_size = hidden_size
        self.n_head = n_head
        self.d_hidden_size = hidden_size // n_head
        self.dropout_rate = dropout
        
        # 初始化参数
        self.v = nn.Parameter(torch.zeros((1, 1, self.hidden_size), device=device))
        self.W = nn.Parameter(torch.zeros((1, self.d_hidden_size, 2 * self.d_hidden_size), device=device))
        
        # 线性变换层
        self.w_query = nn.Linear(hidden_size, n_head * self.d_hidden_size)
        self.w_context = nn.Linear(hidden_size, n_head * self.d_hidden_size)
        
        # 输出投影层
        self.out_proj = nn.Linear(hidden_size, hidden_size)
        
        # 层归一化
        self.layer_norm1 = nn.LayerNorm(hidden_size)
        self.layer_norm2 = nn.LayerNorm(hidden_size)
        
        # dropout层
        self.dropout = nn.Dropout(dropout)
        self.attn_dropout = nn.Dropout(dropout)

        # 使用Kaiming初始化
        nn.init.kaiming_uniform_(self.w_query.weight)
        nn.init.kaiming_uniform_(self.w_context.weight)
        nn.init.kaiming_uniform_(self.out_proj.weight)
        nn.init.kaiming_uniform_(self.W.view(1, -1, 2 * self.d_hidden_size))
        nn.init.kaiming_uniform_(self.v.view(1, -1, 1))
        
        # 残差连接的缩放因子
        self.scale_factor = nn.Parameter(torch.ones(1))

    def forward(self, q, context):
        # q: (batch_size, hidden_size)
        # context: (batch_size * seq_len, hidden_size)
        batch_size = q.size(0)
        seq_len = context.size(0) // batch_size
        
        # 将context重塑为三维张量
        context = context.view(batch_size, seq_len, -1).transpose(1, 2)  # (batch_size, hidden_size, seq_len)
        
        # 保存原始输入用于残差连接
        q_residual = q
        context_residual = context.transpose(1, 2).contiguous().view(batch_size * seq_len, -1)
        
        # 扩展query以匹配context的序列长度
        q_expanded = q.unsqueeze(2).expand(-1, -1, seq_len)  # (batch_size, hidden_size, seq_len)
        
        # 多头注意力处理
        n_head, d_hidden_size = self.n_head, self.d_hidden_size
        
        # 线性变换并重塑为多头形式
        q_proj = self.w_query(q_expanded.transpose(1, 2)).view(batch_size, seq_len, n_head, d_hidden_size)
        context_proj = self.w_context(context.transpose(1, 2)).view(batch_size, seq_len, n_head, d_hidden_size)
        
        # 应用dropout
        q_proj = self.dropout(q_proj)
        context_proj = self.dropout(context_proj)
        
        # 重排维度以便进行批量矩阵乘法
        q_proj = q_proj.permute(2, 0, 3, 1).contiguous().view(-1, d_hidden_size, seq_len)
        context_proj = context_proj.permute(2, 0, 3, 1).contiguous().view(-1, d_hidden_size, seq_len)
        
        # 拼接特征
        combined = torch.cat((q_proj, context_proj), dim=1)
        
        # 扩展参数以匹配批量大小
        W = self.W.expand(n_head * batch_size, -1, -1)
        v = self.v.expand(batch_size, -1, -1)
        
        # 计算注意力分数
        attns = torch.tanh(torch.bmm(W, combined)).view(n_head, batch_size, d_hidden_size, seq_len)
        attns = attns.permute(1, 0, 2, 3).contiguous().view(batch_size, -1, seq_len)
        
        # 应用注意力dropout
        attns = self.attn_dropout(attns)
        
        # 计算最终的注意力分数
        probs = torch.bmm(v, attns).squeeze(1)
        
        # 应用缩放因子
        probs = probs * self.scale_factor
        
        # 返回注意力分数和上下文
        return probs, context
