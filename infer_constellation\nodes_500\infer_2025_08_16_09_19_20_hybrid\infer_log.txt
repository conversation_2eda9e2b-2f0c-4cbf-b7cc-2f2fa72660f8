推理数据数量: 100
每个序列任务数量: 500
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 86.6402
  收益率: 0.4124
  距离: 18.3843
  内存使用: 0.3080
  能量使用: 0.6273
  推理时间: 1.7671秒

批次 2:
  奖励值: 90.0093
  收益率: 0.4363
  距离: 20.9049
  内存使用: 0.4521
  能量使用: 0.6245
  推理时间: 1.8173秒

批次 3:
  奖励值: 86.8846
  收益率: 0.4320
  距离: 22.2576
  内存使用: 0.3571
  能量使用: 0.6160
  推理时间: 1.7397秒

批次 4:
  奖励值: 80.5870
  收益率: 0.4064
  距离: 23.2049
  内存使用: 0.3685
  能量使用: 0.6526
  推理时间: 1.6794秒

批次 5:
  奖励值: 84.1338
  收益率: 0.4267
  距离: 21.6953
  内存使用: 0.3686
  能量使用: 0.6684
  推理时间: 1.7743秒

批次 6:
  奖励值: 77.7593
  收益率: 0.3974
  距离: 19.7450
  内存使用: 0.3523
  能量使用: 0.5841
  推理时间: 1.6695秒

批次 7:
  奖励值: 92.2119
  收益率: 0.4644
  距离: 23.0251
  内存使用: 0.4374
  能量使用: 0.7137
  推理时间: 1.8982秒

批次 8:
  奖励值: 85.5932
  收益率: 0.4359
  距离: 21.0040
  内存使用: 0.3640
  能量使用: 0.6456
  推理时间: 1.8128秒

批次 9:
  奖励值: 84.3653
  收益率: 0.4366
  距离: 20.1783
  内存使用: 0.3819
  能量使用: 0.7092
  推理时间: 1.7586秒

批次 10:
  奖励值: 88.8548
  收益率: 0.4376
  距离: 23.2081
  内存使用: 0.3931
  能量使用: 0.6893
  推理时间: 1.7827秒

批次 11:
  奖励值: 94.3682
  收益率: 0.4581
  距离: 23.5117
  内存使用: 0.4431
  能量使用: 0.7089
  推理时间: 1.9326秒

批次 12:
  奖励值: 85.8171
  收益率: 0.4303
  距离: 23.4524
  内存使用: 0.4300
  能量使用: 0.6835
  推理时间: 1.8019秒

批次 13:
  奖励值: 83.4621
  收益率: 0.4404
  距离: 25.0450
  内存使用: 0.3728
  能量使用: 0.6581
  推理时间: 1.7591秒

批次 14:
  奖励值: 85.7076
  收益率: 0.4286
  距离: 23.7613
  内存使用: 0.4007
  能量使用: 0.6227
  推理时间: 1.8002秒

批次 15:
  奖励值: 83.6023
  收益率: 0.4160
  距离: 21.6251
  内存使用: 0.3208
  能量使用: 0.6494
  推理时间: 1.7341秒

批次 16:
  奖励值: 83.8109
  收益率: 0.4216
  距离: 20.5661
  内存使用: 0.3545
  能量使用: 0.6577
  推理时间: 1.7040秒

批次 17:
  奖励值: 87.9146
  收益率: 0.4379
  距离: 23.2985
  内存使用: 0.4123
  能量使用: 0.6600
  推理时间: 1.8163秒

批次 18:
  奖励值: 72.9603
  收益率: 0.3751
  距离: 16.8299
  内存使用: 0.3075
  能量使用: 0.5420
  推理时间: 1.4886秒

批次 19:
  奖励值: 83.3551
  收益率: 0.4074
  距离: 21.8223
  内存使用: 0.4233
  能量使用: 0.6443
  推理时间: 1.7113秒

批次 20:
  奖励值: 83.5447
  收益率: 0.4189
  距离: 20.9200
  内存使用: 0.3728
  能量使用: 0.6179
  推理时间: 1.7102秒

批次 21:
  奖励值: 91.7339
  收益率: 0.4609
  距离: 24.1046
  内存使用: 0.4471
  能量使用: 0.7804
  推理时间: 1.9599秒

批次 22:
  奖励值: 85.0297
  收益率: 0.4404
  距离: 23.2498
  内存使用: 0.4361
  能量使用: 0.6547
  推理时间: 1.7991秒

批次 23:
  奖励值: 85.4070
  收益率: 0.4241
  距离: 22.7603
  内存使用: 0.3636
  能量使用: 0.6620
  推理时间: 1.7816秒

批次 24:
  奖励值: 82.2733
  收益率: 0.4257
  距离: 20.0034
  内存使用: 0.3322
  能量使用: 0.7178
  推理时间: 1.7113秒

批次 25:
  奖励值: 85.0596
  收益率: 0.4204
  距离: 22.0405
  内存使用: 0.3868
  能量使用: 0.6535
  推理时间: 1.7065秒

批次 26:
  奖励值: 87.5500
  收益率: 0.4354
  距离: 22.6358
  内存使用: 0.4307
  能量使用: 0.6335
  推理时间: 1.7854秒

批次 27:
  奖励值: 78.1014
  收益率: 0.3946
  距离: 21.3861
  内存使用: 0.3415
  能量使用: 0.6696
  推理时间: 1.6855秒

批次 28:
  奖励值: 81.5421
  收益率: 0.4055
  距离: 20.1588
  内存使用: 0.3042
  能量使用: 0.6527
  推理时间: 1.6733秒

批次 29:
  奖励值: 83.2204
  收益率: 0.4126
  距离: 19.0007
  内存使用: 0.3271
  能量使用: 0.6315
  推理时间: 1.6451秒

批次 30:
  奖励值: 87.5751
  收益率: 0.4393
  距离: 22.1573
  内存使用: 0.3861
  能量使用: 0.7064
  推理时间: 1.8084秒

批次 31:
  奖励值: 94.3194
  收益率: 0.4628
  距离: 20.3692
  内存使用: 0.4212
  能量使用: 0.7707
  推理时间: 1.9608秒

批次 32:
  奖励值: 86.6024
  收益率: 0.4237
  距离: 18.0108
  内存使用: 0.3413
  能量使用: 0.6432
  推理时间: 1.7338秒

批次 33:
  奖励值: 84.6979
  收益率: 0.4275
  距离: 24.8157
  内存使用: 0.2957
  能量使用: 0.6928
  推理时间: 1.7613秒

批次 34:
  奖励值: 83.5579
  收益率: 0.4234
  距离: 22.4145
  内存使用: 0.4139
  能量使用: 0.6558
  推理时间: 1.7366秒

批次 35:
  奖励值: 74.7462
  收益率: 0.3843
  距离: 19.4404
  内存使用: 0.2813
  能量使用: 0.5859
  推理时间: 1.5668秒

批次 36:
  奖励值: 84.5200
  收益率: 0.4191
  距离: 22.5691
  内存使用: 0.3321
  能量使用: 0.6588
  推理时间: 1.7412秒

批次 37:
  奖励值: 82.5866
  收益率: 0.4073
  距离: 18.0186
  内存使用: 0.3805
  能量使用: 0.6278
  推理时间: 1.7366秒

批次 38:
  奖励值: 90.1185
  收益率: 0.4493
  距离: 23.3564
  内存使用: 0.4652
  能量使用: 0.7414
  推理时间: 1.8643秒

批次 39:
  奖励值: 95.0135
  收益率: 0.4666
  距离: 23.6318
  内存使用: 0.4246
  能量使用: 0.7167
  推理时间: 1.9205秒

批次 40:
  奖励值: 76.5362
  收益率: 0.4072
  距离: 21.5983
  内存使用: 0.3102
  能量使用: 0.6192
  推理时间: 1.6402秒

批次 41:
  奖励值: 87.7340
  收益率: 0.4410
  距离: 23.0773
  内存使用: 0.3746
  能量使用: 0.6801
  推理时间: 1.8505秒

批次 42:
  奖励值: 92.4371
  收益率: 0.4526
  距离: 21.4248
  内存使用: 0.4011
  能量使用: 0.6999
  推理时间: 1.8779秒

批次 43:
  奖励值: 86.3894
  收益率: 0.4193
  距离: 19.6254
  内存使用: 0.3398
  能量使用: 0.6560
  推理时间: 1.7777秒

批次 44:
  奖励值: 93.7401
  收益率: 0.4660
  距离: 21.2498
  内存使用: 0.4235
  能量使用: 0.6466
  推理时间: 1.9213秒

批次 45:
  奖励值: 90.7043
  收益率: 0.4258
  距离: 20.0604
  内存使用: 0.4272
  能量使用: 0.7039
  推理时间: 1.7922秒

批次 46:
  奖励值: 76.1476
  收益率: 0.3847
  距离: 22.0369
  内存使用: 0.2475
  能量使用: 0.6205
  推理时间: 1.5804秒

批次 47:
  奖励值: 85.5552
  收益率: 0.4291
  距离: 18.6893
  内存使用: 0.3655
  能量使用: 0.6348
  推理时间: 1.7425秒

批次 48:
  奖励值: 81.5926
  收益率: 0.4119
  距离: 22.4612
  内存使用: 0.3350
  能量使用: 0.6187
  推理时间: 1.7106秒

批次 49:
  奖励值: 81.8719
  收益率: 0.4035
  距离: 21.3297
  内存使用: 0.3555
  能量使用: 0.5710
  推理时间: 1.6699秒

批次 50:
  奖励值: 76.8046
  收益率: 0.3730
  距离: 18.5028
  内存使用: 0.2812
  能量使用: 0.6068
  推理时间: 1.6251秒

批次 51:
  奖励值: 82.4787
  收益率: 0.4214
  距离: 21.2942
  内存使用: 0.3261
  能量使用: 0.6223
  推理时间: 1.7182秒

批次 52:
  奖励值: 82.8579
  收益率: 0.4186
  距离: 20.8611
  内存使用: 0.3516
  能量使用: 0.6714
  推理时间: 1.6946秒

批次 53:
  奖励值: 83.8109
  收益率: 0.4266
  距离: 23.1283
  内存使用: 0.3494
  能量使用: 0.7004
  推理时间: 1.7656秒

批次 54:
  奖励值: 87.8567
  收益率: 0.4268
  距离: 20.4497
  内存使用: 0.3905
  能量使用: 0.6464
  推理时间: 1.8147秒

批次 55:
  奖励值: 88.6344
  收益率: 0.4432
  距离: 22.1040
  内存使用: 0.3633
  能量使用: 0.6639
  推理时间: 1.8855秒

批次 56:
  奖励值: 85.6238
  收益率: 0.4335
  距离: 25.1233
  内存使用: 0.4034
  能量使用: 0.6257
  推理时间: 1.7804秒

批次 57:
  奖励值: 82.8853
  收益率: 0.4179
  距离: 22.6248
  内存使用: 0.3853
  能量使用: 0.6194
  推理时间: 1.7249秒

批次 58:
  奖励值: 85.0170
  收益率: 0.4183
  距离: 21.3909
  内存使用: 0.3618
  能量使用: 0.6133
  推理时间: 1.7383秒

批次 59:
  奖励值: 91.6091
  收益率: 0.4554
  距离: 20.6259
  内存使用: 0.4189
  能量使用: 0.7371
  推理时间: 1.8369秒

批次 60:
  奖励值: 83.3655
  收益率: 0.4044
  距离: 18.9268
  内存使用: 0.2941
  能量使用: 0.6481
  推理时间: 1.6687秒

批次 61:
  奖励值: 85.8443
  收益率: 0.4272
  距离: 21.4899
  内存使用: 0.3611
  能量使用: 0.6604
  推理时间: 1.7580秒

批次 62:
  奖励值: 85.8459
  收益率: 0.4473
  距离: 24.7958
  内存使用: 0.3583
  能量使用: 0.7542
  推理时间: 1.7976秒

批次 63:
  奖励值: 86.1185
  收益率: 0.4235
  距离: 26.7166
  内存使用: 0.3975
  能量使用: 0.6488
  推理时间: 1.7908秒

批次 64:
  奖励值: 80.6251
  收益率: 0.3983
  距离: 18.9308
  内存使用: 0.2435
  能量使用: 0.5755
  推理时间: 1.6351秒

批次 65:
  奖励值: 89.4360
  收益率: 0.4458
  距离: 23.1950
  内存使用: 0.4500
  能量使用: 0.6830
  推理时间: 1.8412秒

批次 66:
  奖励值: 75.2463
  收益率: 0.3778
  距离: 19.6323
  内存使用: 0.2589
  能量使用: 0.6163
  推理时间: 1.5707秒

批次 67:
  奖励值: 86.3902
  收益率: 0.4255
  距离: 21.9383
  内存使用: 0.3775
  能量使用: 0.7038
  推理时间: 1.8154秒

批次 68:
  奖励值: 87.0652
  收益率: 0.4311
  距离: 23.0237
  内存使用: 0.4015
  能量使用: 0.6443
  推理时间: 1.7626秒

批次 69:
  奖励值: 85.3288
  收益率: 0.4345
  距离: 22.0463
  内存使用: 0.4118
  能量使用: 0.6844
  推理时间: 1.8112秒

批次 70:
  奖励值: 93.2655
  收益率: 0.4632
  距离: 24.8638
  内存使用: 0.4367
  能量使用: 0.7686
  推理时间: 1.9884秒

批次 71:
  奖励值: 86.2473
  收益率: 0.4305
  距离: 24.0147
  内存使用: 0.4161
  能量使用: 0.6747
  推理时间: 1.7830秒

批次 72:
  奖励值: 79.5474
  收益率: 0.4295
  距离: 21.4890
  内存使用: 0.3532
  能量使用: 0.6632
  推理时间: 1.6910秒

批次 73:
  奖励值: 74.9270
  收益率: 0.3762
  距离: 19.4214
  内存使用: 0.3410
  能量使用: 0.5837
  推理时间: 1.5731秒

批次 74:
  奖励值: 89.3299
  收益率: 0.4440
  距离: 24.8426
  内存使用: 0.4395
  能量使用: 0.6581
  推理时间: 1.8770秒

批次 75:
  奖励值: 89.8322
  收益率: 0.4483
  距离: 24.7915
  内存使用: 0.4228
  能量使用: 0.7214
  推理时间: 1.8593秒

批次 76:
  奖励值: 89.1550
  收益率: 0.4451
  距离: 21.7685
  内存使用: 0.4224
  能量使用: 0.6992
  推理时间: 1.8087秒

批次 77:
  奖励值: 88.4770
  收益率: 0.4449
  距离: 22.9541
  内存使用: 0.3919
  能量使用: 0.7000
  推理时间: 1.8548秒

批次 78:
  奖励值: 94.7626
  收益率: 0.4530
  距离: 24.0686
  内存使用: 0.4288
  能量使用: 0.7774
  推理时间: 1.9191秒

批次 79:
  奖励值: 79.2040
  收益率: 0.4042
  距离: 20.0304
  内存使用: 0.3196
  能量使用: 0.6419
  推理时间: 1.6752秒

批次 80:
  奖励值: 83.8658
  收益率: 0.4438
  距离: 22.3779
  内存使用: 0.3956
  能量使用: 0.6950
  推理时间: 1.7364秒

批次 81:
  奖励值: 78.0673
  收益率: 0.3880
  距离: 21.7098
  内存使用: 0.3504
  能量使用: 0.6052
  推理时间: 1.6087秒

批次 82:
  奖励值: 82.7021
  收益率: 0.4117
  距离: 21.9972
  内存使用: 0.3433
  能量使用: 0.6554
  推理时间: 1.7595秒

批次 83:
  奖励值: 82.2959
  收益率: 0.4032
  距离: 19.8470
  内存使用: 0.3281
  能量使用: 0.6331
  推理时间: 1.6113秒

批次 84:
  奖励值: 86.7495
  收益率: 0.4327
  距离: 22.0859
  内存使用: 0.3485
  能量使用: 0.6665
  推理时间: 1.7610秒

批次 85:
  奖励值: 90.7954
  收益率: 0.4506
  距离: 21.5448
  内存使用: 0.3867
  能量使用: 0.6777
  推理时间: 1.8387秒

批次 86:
  奖励值: 91.4030
  收益率: 0.4512
  距离: 23.2713
  内存使用: 0.3780
  能量使用: 0.7448
  推理时间: 1.8855秒

批次 87:
  奖励值: 77.2396
  收益率: 0.3988
  距离: 19.5046
  内存使用: 0.4230
  能量使用: 0.5933
  推理时间: 1.6296秒

批次 88:
  奖励值: 81.4704
  收益率: 0.4257
  距离: 22.2857
  内存使用: 0.3417
  能量使用: 0.6319
  推理时间: 1.6697秒

批次 89:
  奖励值: 78.5736
  收益率: 0.4008
  距离: 20.5027
  内存使用: 0.3297
  能量使用: 0.6204
  推理时间: 1.6754秒

批次 90:
  奖励值: 80.4176
  收益率: 0.4036
  距离: 18.5410
  内存使用: 0.3831
  能量使用: 0.6406
  推理时间: 1.8633秒

批次 91:
  奖励值: 83.1770
  收益率: 0.4137
  距离: 20.9938
  内存使用: 0.3505
  能量使用: 0.7017
  推理时间: 1.7536秒

批次 92:
  奖励值: 80.5969
  收益率: 0.4280
  距离: 24.9595
  内存使用: 0.3910
  能量使用: 0.6556
  推理时间: 1.7761秒

批次 93:
  奖励值: 81.1435
  收益率: 0.4097
  距离: 20.5534
  内存使用: 0.2852
  能量使用: 0.6744
  推理时间: 1.6905秒

批次 94:
  奖励值: 73.5015
  收益率: 0.3936
  距离: 21.1060
  内存使用: 0.3095
  能量使用: 0.5815
  推理时间: 1.5896秒

批次 95:
  奖励值: 79.7016
  收益率: 0.4004
  距离: 22.4554
  内存使用: 0.3054
  能量使用: 0.6361
  推理时间: 1.6122秒

批次 96:
  奖励值: 80.8079
  收益率: 0.4209
  距离: 23.1610
  内存使用: 0.3557
  能量使用: 0.6675
  推理时间: 1.7427秒

批次 97:
  奖励值: 88.8658
  收益率: 0.4389
  距离: 23.1768
  内存使用: 0.4584
  能量使用: 0.6332
  推理时间: 1.7882秒

批次 98:
  奖励值: 75.2240
  收益率: 0.3971
  距离: 19.6123
  内存使用: 0.3362
  能量使用: 0.6034
  推理时间: 1.5832秒

批次 99:
  奖励值: 87.2490
  收益率: 0.4301
  距离: 21.2624
  内存使用: 0.3509
  能量使用: 0.6886
  推理时间: 1.7706秒

批次 100:
  奖励值: 85.2533
  收益率: 0.4185
  距离: 22.0471
  内存使用: 0.3568
  能量使用: 0.6895
  推理时间: 1.7587秒


==================== 总结 ====================
平均收益率: 0.4243
平均能量使用: 0.6599
平均推理时间: 1.7547秒
