推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: hybrid
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_14_16_01_24

批次 1:
  奖励值: 25.5973
  收益率: 0.6541
  距离: 7.6272
  内存使用: -0.0462
  能量使用: 0.2243
  推理时间: 0.9898秒

批次 2:
  奖励值: 21.6742
  收益率: 0.5474
  距离: 7.4164
  内存使用: -0.1242
  能量使用: 0.1595
  推理时间: 0.4318秒

批次 3:
  奖励值: 25.3555
  收益率: 0.6187
  距离: 5.9602
  内存使用: -0.0797
  能量使用: 0.2084
  推理时间: 0.4572秒

批次 4:
  奖励值: 27.9104
  收益率: 0.6883
  距离: 7.5577
  内存使用: -0.0551
  能量使用: 0.2767
  推理时间: 0.6356秒

批次 5:
  奖励值: 25.2687
  收益率: 0.6386
  距离: 5.5689
  内存使用: -0.1051
  能量使用: 0.1988
  推理时间: 0.5684秒

批次 6:
  奖励值: 29.6923
  收益率: 0.6864
  距离: 7.8755
  内存使用: -0.0846
  能量使用: 0.2244
  推理时间: 0.6457秒

批次 7:
  奖励值: 24.3035
  收益率: 0.6414
  距离: 8.8558
  内存使用: -0.0947
  能量使用: 0.2319
  推理时间: 0.4641秒

批次 8:
  奖励值: 27.3470
  收益率: 0.6688
  距离: 7.3701
  内存使用: -0.1067
  能量使用: 0.2426
  推理时间: 0.6204秒

批次 9:
  奖励值: 25.6374
  收益率: 0.6323
  距离: 7.4213
  内存使用: -0.1185
  能量使用: 0.2225
  推理时间: 0.6096秒

批次 10:
  奖励值: 26.2482
  收益率: 0.6366
  距离: 7.6078
  内存使用: 0.2424
  能量使用: 0.2534
  推理时间: 0.6322秒

批次 11:
  奖励值: 28.5909
  收益率: 0.7241
  距离: 8.2455
  内存使用: -0.0849
  能量使用: 0.2576
  推理时间: 0.7031秒

批次 12:
  奖励值: 31.2338
  收益率: 0.7092
  距离: 5.1807
  内存使用: -0.0697
  能量使用: 0.2214
  推理时间: 0.7216秒

批次 13:
  奖励值: 27.8375
  收益率: 0.6612
  距离: 8.1821
  内存使用: -0.0671
  能量使用: 0.2100
  推理时间: 0.6803秒

批次 14:
  奖励值: 27.2445
  收益率: 0.6851
  距离: 7.1028
  内存使用: -0.0551
  能量使用: 0.2633
  推理时间: 0.6653秒

批次 15:
  奖励值: 27.2813
  收益率: 0.6457
  距离: 6.9152
  内存使用: -0.0824
  能量使用: 0.2046
  推理时间: 0.6240秒

批次 16:
  奖励值: 30.8870
  收益率: 0.7178
  距离: 7.2381
  内存使用: -0.0445
  能量使用: 0.2324
  推理时间: 0.7303秒

批次 17:
  奖励值: 27.9682
  收益率: 0.6674
  距离: 6.0513
  内存使用: -0.0522
  能量使用: 0.2646
  推理时间: 0.7155秒

批次 18:
  奖励值: 21.9092
  收益率: 0.5580
  距离: 6.1373
  内存使用: -0.1324
  能量使用: 0.1768
  推理时间: 0.5527秒

批次 19:
  奖励值: 29.8342
  收益率: 0.7164
  距离: 9.3131
  内存使用: -0.0628
  能量使用: 0.2813
  推理时间: 0.8280秒

批次 20:
  奖励值: 26.6044
  收益率: 0.6719
  距离: 6.8137
  内存使用: -0.0496
  能量使用: 0.2271
  推理时间: 0.6553秒

批次 21:
  奖励值: 31.2658
  收益率: 0.7468
  距离: 7.4291
  内存使用: -0.0459
  能量使用: 0.2852
  推理时间: 0.7971秒

批次 22:
  奖励值: 27.4097
  收益率: 0.6561
  距离: 6.7814
  内存使用: -0.0122
  能量使用: 0.2114
  推理时间: 0.7008秒

批次 23:
  奖励值: 26.0783
  收益率: 0.6261
  距离: 6.3001
  内存使用: -0.0853
  能量使用: 0.2286
  推理时间: 0.6350秒

批次 24:
  奖励值: 23.6658
  收益率: 0.6110
  距离: 5.9585
  内存使用: -0.0917
  能量使用: 0.2015
  推理时间: 0.5521秒

批次 25:
  奖励值: 27.2679
  收益率: 0.6351
  距离: 6.9563
  内存使用: -0.1196
  能量使用: 0.2275
  推理时间: 0.6779秒

批次 26:
  奖励值: 24.6344
  收益率: 0.6299
  距离: 8.7742
  内存使用: -0.0726
  能量使用: 0.2296
  推理时间: 0.6985秒

批次 27:
  奖励值: 29.0890
  收益率: 0.6660
  距离: 6.6313
  内存使用: -0.0531
  能量使用: 0.2438
  推理时间: 0.7280秒

批次 28:
  奖励值: 25.5543
  收益率: 0.6371
  距离: 7.7429
  内存使用: -0.0356
  能量使用: 0.1842
  推理时间: 0.7368秒

批次 29:
  奖励值: 28.3916
  收益率: 0.7016
  距离: 6.1010
  内存使用: -0.0990
  能量使用: 0.2148
  推理时间: 0.7017秒

批次 30:
  奖励值: 27.3501
  收益率: 0.6623
  距离: 6.3227
  内存使用: -0.0563
  能量使用: 0.2334
  推理时间: 0.6879秒

批次 31:
  奖励值: 30.3183
  收益率: 0.7072
  距离: 10.1470
  内存使用: -0.0273
  能量使用: 0.2438
  推理时间: 0.7836秒

批次 32:
  奖励值: 25.6892
  收益率: 0.6354
  距离: 8.0706
  内存使用: -0.0684
  能量使用: 0.2194
  推理时间: 0.5342秒

批次 33:
  奖励值: 26.6044
  收益率: 0.6368
  距离: 6.2822
  内存使用: -0.1119
  能量使用: 0.2208
  推理时间: 0.5736秒

批次 34:
  奖励值: 25.6736
  收益率: 0.6215
  距离: 6.3666
  内存使用: -0.0936
  能量使用: 0.1798
  推理时间: 0.6873秒

批次 35:
  奖励值: 28.6445
  收益率: 0.7013
  距离: 8.6902
  内存使用: -0.0382
  能量使用: 0.2589
  推理时间: 0.8134秒

批次 36:
  奖励值: 32.1509
  收益率: 0.7206
  距离: 7.7280
  内存使用: -0.0883
  能量使用: 0.2584
  推理时间: 0.7959秒

批次 37:
  奖励值: 24.8191
  收益率: 0.6618
  距离: 5.0456
  内存使用: -0.1047
  能量使用: 0.2209
  推理时间: 0.5966秒

批次 38:
  奖励值: 24.8674
  收益率: 0.6498
  距离: 6.5965
  内存使用: -0.1354
  能量使用: 0.2041
  推理时间: 0.5269秒

批次 39:
  奖励值: 26.6783
  收益率: 0.6368
  距离: 6.1196
  内存使用: -0.0925
  能量使用: 0.2164
  推理时间: 0.5377秒

批次 40:
  奖励值: 24.1130
  收益率: 0.6022
  距离: 5.8906
  内存使用: -0.1137
  能量使用: 0.1972
  推理时间: 0.5568秒

批次 41:
  奖励值: 24.7567
  收益率: 0.6176
  距离: 4.9962
  内存使用: -0.1120
  能量使用: 0.2024
  推理时间: 0.5185秒

批次 42:
  奖励值: 30.5505
  收益率: 0.7246
  距离: 8.7051
  内存使用: -0.0372
  能量使用: 0.2311
  推理时间: 0.7425秒

批次 43:
  奖励值: 26.4218
  收益率: 0.6494
  距离: 7.0044
  内存使用: -0.0560
  能量使用: 0.2081
  推理时间: 0.6102秒

批次 44:
  奖励值: 25.7963
  收益率: 0.6192
  距离: 7.4855
  内存使用: -0.1166
  能量使用: 0.2385
  推理时间: 0.6390秒

批次 45:
  奖励值: 27.1025
  收益率: 0.6797
  距离: 8.4913
  内存使用: -0.0877
  能量使用: 0.1915
  推理时间: 0.5892秒

批次 46:
  奖励值: 27.9263
  收益率: 0.6732
  距离: 6.2560
  内存使用: -0.1207
  能量使用: 0.2120
  推理时间: 0.5614秒

批次 47:
  奖励值: 26.4207
  收益率: 0.6519
  距离: 5.8613
  内存使用: -0.1207
  能量使用: 0.2180
  推理时间: 0.5357秒

批次 48:
  奖励值: 28.0640
  收益率: 0.6851
  距离: 7.7937
  内存使用: 0.2319
  能量使用: 0.2464
  推理时间: 0.6489秒

批次 49:
  奖励值: 29.4800
  收益率: 0.6667
  距离: 6.5351
  内存使用: -0.0967
  能量使用: 0.2015
  推理时间: 0.5644秒

批次 50:
  奖励值: 25.2040
  收益率: 0.5929
  距离: 6.3160
  内存使用: -0.1234
  能量使用: 0.1993
  推理时间: 0.6313秒

批次 51:
  奖励值: 26.6502
  收益率: 0.6500
  距离: 6.3577
  内存使用: -0.0843
  能量使用: 0.2262
  推理时间: 0.6329秒

批次 52:
  奖励值: 24.6375
  收益率: 0.5996
  距离: 6.0419
  内存使用: -0.0873
  能量使用: 0.1704
  推理时间: 0.4670秒

批次 53:
  奖励值: 31.3902
  收益率: 0.7474
  距离: 8.5677
  内存使用: -0.0109
  能量使用: 0.2628
  推理时间: 0.7966秒

批次 54:
  奖励值: 26.2559
  收益率: 0.6441
  距离: 6.3682
  内存使用: -0.0824
  能量使用: 0.2025
  推理时间: 0.8271秒

批次 55:
  奖励值: 28.7273
  收益率: 0.6793
  距离: 6.7716
  内存使用: -0.0630
  能量使用: 0.2368
  推理时间: 0.7973秒

批次 56:
  奖励值: 26.1616
  收益率: 0.6430
  距离: 5.5334
  内存使用: -0.0679
  能量使用: 0.2114
  推理时间: 0.6942秒

批次 57:
  奖励值: 23.7705
  收益率: 0.6231
  距离: 8.5253
  内存使用: -0.0853
  能量使用: 0.2190
  推理时间: 0.8203秒

批次 58:
  奖励值: 25.3744
  收益率: 0.6548
  距离: 7.9111
  内存使用: -0.1075
  能量使用: 0.2475
  推理时间: 0.7150秒

批次 59:
  奖励值: 32.0958
  收益率: 0.7352
  距离: 10.0155
  内存使用: -0.0493
  能量使用: 0.2422
  推理时间: 0.7323秒

批次 60:
  奖励值: 24.3143
  收益率: 0.6004
  距离: 6.8576
  内存使用: -0.0753
  能量使用: 0.1892
  推理时间: 0.5530秒

批次 61:
  奖励值: 25.5226
  收益率: 0.6528
  距离: 8.6099
  内存使用: -0.0849
  能量使用: 0.2298
  推理时间: 0.6284秒

批次 62:
  奖励值: 24.1215
  收益率: 0.6340
  距离: 6.0991
  内存使用: -0.1258
  能量使用: 0.1838
  推理时间: 0.5504秒

批次 63:
  奖励值: 26.5684
  收益率: 0.6878
  距离: 7.4694
  内存使用: -0.0408
  能量使用: 0.2345
  推理时间: 0.6129秒

批次 64:
  奖励值: 22.6761
  收益率: 0.6019
  距离: 5.9308
  内存使用: -0.0907
  能量使用: 0.2077
  推理时间: 0.7114秒

批次 65:
  奖励值: 30.3315
  收益率: 0.7096
  距离: 8.5894
  内存使用: -0.0679
  能量使用: 0.2155
  推理时间: 0.7872秒

批次 66:
  奖励值: 25.5029
  收益率: 0.6308
  距离: 8.6328
  内存使用: -0.0759
  能量使用: 0.2374
  推理时间: 0.6704秒

批次 67:
  奖励值: 26.4218
  收益率: 0.6452
  距离: 6.9696
  内存使用: -0.0571
  能量使用: 0.2438
  推理时间: 0.6366秒

批次 68:
  奖励值: 24.6329
  收益率: 0.6138
  距离: 5.6442
  内存使用: -0.1109
  能量使用: 0.2009
  推理时间: 0.6731秒

批次 69:
  奖励值: 26.0823
  收益率: 0.6513
  距离: 7.1132
  内存使用: -0.0926
  能量使用: 0.2149
  推理时间: 0.8258秒

批次 70:
  奖励值: 27.5094
  收益率: 0.6687
  距离: 9.8270
  内存使用: -0.0774
  能量使用: 0.2316
  推理时间: 0.6902秒

批次 71:
  奖励值: 26.8368
  收益率: 0.6481
  距离: 6.6158
  内存使用: -0.1135
  能量使用: 0.2242
  推理时间: 0.6273秒

批次 72:
  奖励值: 24.1780
  收益率: 0.6057
  距离: 6.5650
  内存使用: -0.1244
  能量使用: 0.2034
  推理时间: 0.6027秒

批次 73:
  奖励值: 28.5465
  收益率: 0.6501
  距离: 8.1282
  内存使用: -0.0798
  能量使用: 0.2586
  推理时间: 0.6289秒

批次 74:
  奖励值: 27.9810
  收益率: 0.6496
  距离: 7.3017
  内存使用: -0.0986
  能量使用: 0.2350
  推理时间: 0.6707秒

批次 75:
  奖励值: 27.3073
  收益率: 0.6783
  距离: 7.2196
  内存使用: -0.0664
  能量使用: 0.2323
  推理时间: 0.7175秒

批次 76:
  奖励值: 23.9006
  收益率: 0.6374
  距离: 7.2952
  内存使用: -0.1031
  能量使用: 0.2067
  推理时间: 0.5571秒

批次 77:
  奖励值: 28.9637
  收益率: 0.6667
  距离: 7.1334
  内存使用: -0.0822
  能量使用: 0.2215
  推理时间: 0.6981秒

批次 78:
  奖励值: 25.8048
  收益率: 0.6520
  距离: 7.4748
  内存使用: -0.1010
  能量使用: 0.2166
  推理时间: 0.6284秒

批次 79:
  奖励值: 23.8238
  收益率: 0.6417
  距离: 7.0550
  内存使用: -0.0927
  能量使用: 0.1901
  推理时间: 0.6876秒

批次 80:
  奖励值: 25.2135
  收益率: 0.6467
  距离: 7.6461
  内存使用: -0.0775
  能量使用: 0.2044
  推理时间: 0.6008秒

批次 81:
  奖励值: 20.3754
  收益率: 0.5985
  距离: 7.1784
  内存使用: -0.1158
  能量使用: 0.1865
  推理时间: 0.5942秒

批次 82:
  奖励值: 30.2578
  收益率: 0.6923
  距离: 7.6352
  内存使用: -0.0262
  能量使用: 0.2754
  推理时间: 0.7505秒

批次 83:
  奖励值: 28.3569
  收益率: 0.6586
  距离: 8.2624
  内存使用: -0.0501
  能量使用: 0.2740
  推理时间: 0.7289秒

批次 84:
  奖励值: 28.0645
  收益率: 0.6653
  距离: 7.3293
  内存使用: -0.0687
  能量使用: 0.2103
  推理时间: 0.7209秒

批次 85:
  奖励值: 25.0468
  收益率: 0.5921
  距离: 6.4548
  内存使用: -0.1349
  能量使用: 0.1864
  推理时间: 0.6296秒

批次 86:
  奖励值: 24.5439
  收益率: 0.6305
  距离: 7.7885
  内存使用: -0.0873
  能量使用: 0.2110
  推理时间: 0.6445秒

批次 87:
  奖励值: 25.0396
  收益率: 0.6341
  距离: 6.9737
  内存使用: -0.1010
  能量使用: 0.2481
  推理时间: 0.6981秒

批次 88:
  奖励值: 30.8005
  收益率: 0.7050
  距离: 5.6510
  内存使用: -0.0536
  能量使用: 0.2016
  推理时间: 0.7778秒

批次 89:
  奖励值: 22.5211
  收益率: 0.5781
  距离: 6.7224
  内存使用: -0.1150
  能量使用: 0.1504
  推理时间: 0.6357秒

批次 90:
  奖励值: 24.8906
  收益率: 0.6537
  距离: 7.0851
  内存使用: -0.0809
  能量使用: 0.2147
  推理时间: 0.6826秒

批次 91:
  奖励值: 26.4062
  收益率: 0.6537
  距离: 7.4400
  内存使用: -0.0727
  能量使用: 0.2202
  推理时间: 0.7264秒

批次 92:
  奖励值: 28.6164
  收益率: 0.6715
  距离: 8.0173
  内存使用: -0.0590
  能量使用: 0.2088
  推理时间: 0.7627秒

批次 93:
  奖励值: 30.6399
  收益率: 0.6826
  距离: 6.9187
  内存使用: -0.0484
  能量使用: 0.2499
  推理时间: 0.7793秒

批次 94:
  奖励值: 23.7591
  收益率: 0.6076
  距离: 6.5873
  内存使用: -0.1062
  能量使用: 0.2006
  推理时间: 0.6534秒

批次 95:
  奖励值: 28.2452
  收益率: 0.6626
  距离: 8.1533
  内存使用: -0.0659
  能量使用: 0.2222
  推理时间: 0.7298秒

批次 96:
  奖励值: 27.9622
  收益率: 0.6767
  距离: 6.7829
  内存使用: -0.0944
  能量使用: 0.1871
  推理时间: 0.7180秒

批次 97:
  奖励值: 23.6424
  收益率: 0.5799
  距离: 5.6199
  内存使用: -0.0935
  能量使用: 0.1887
  推理时间: 0.6096秒

批次 98:
  奖励值: 31.8632
  收益率: 0.7220
  距离: 8.2439
  内存使用: -0.0625
  能量使用: 0.2922
  推理时间: 0.7753秒

批次 99:
  奖励值: 23.1617
  收益率: 0.6141
  距离: 5.7907
  内存使用: -0.1158
  能量使用: 0.2016
  推理时间: 0.6525秒

批次 100:
  奖励值: 30.7693
  收益率: 0.7276
  距离: 9.9207
  内存使用: -0.0809
  能量使用: 0.2215
  推理时间: 0.8081秒


==================== 总结 ====================
平均收益率: 0.6543
平均能量使用: 0.2216
平均推理时间: 0.6661秒
