推理数据数量: 100
每个序列任务数量: 750
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 97.9421
  收益率: 0.3250
  距离: 23.8174
  内存使用: 0.7777
  能量使用: 0.7207
  推理时间: 2.2763秒

批次 2:
  奖励值: 109.2765
  收益率: 0.3638
  距离: 27.4274
  内存使用: 0.5896
  能量使用: 0.8300
  推理时间: 2.4458秒

批次 3:
  奖励值: 104.7969
  收益率: 0.3550
  距离: 27.4343
  内存使用: 0.8829
  能量使用: 0.8890
  推理时间: 2.3057秒

批次 4:
  奖励值: 97.0176
  收益率: 0.3333
  距离: 27.0235
  内存使用: 0.4324
  能量使用: 0.7089
  推理时间: 2.1951秒

批次 5:
  奖励值: 107.2837
  收益率: 0.3692
  距离: 28.0527
  内存使用: 0.5031
  能量使用: 0.8768
  推理时间: 2.4379秒

批次 6:
  奖励值: 99.3333
  收益率: 0.3426
  距离: 26.6628
  内存使用: 0.8256
  能量使用: 0.8449
  推理时间: 2.2826秒

批次 7:
  奖励值: 108.2567
  收益率: 0.3648
  距离: 29.0128
  内存使用: 0.5963
  能量使用: 0.8776
  推理时间: 2.4305秒

批次 8:
  奖励值: 116.7661
  收益率: 0.3883
  距离: 29.1020
  内存使用: 0.6430
  能量使用: 0.9228
  推理时间: 2.5532秒

批次 9:
  奖励值: 100.8836
  收益率: 0.3488
  距离: 22.5305
  内存使用: 0.4446
  能量使用: 0.8575
  推理时间: 2.1236秒

批次 10:
  奖励值: 99.5163
  收益率: 0.3272
  距离: 27.2008
  内存使用: 0.5468
  能量使用: 0.8199
  推理时间: 2.2200秒

批次 11:
  奖励值: 105.8154
  收益率: 0.3611
  距离: 23.5937
  内存使用: 0.5809
  能量使用: 0.7946
  推理时间: 2.3507秒

批次 12:
  奖励值: 102.1800
  收益率: 0.3438
  距离: 29.6411
  内存使用: 0.5415
  能量使用: 0.8573
  推理时间: 2.4082秒

批次 13:
  奖励值: 112.4769
  收益率: 0.3652
  距离: 26.6136
  内存使用: 0.6011
  能量使用: 0.8314
  推理时间: 2.4606秒

批次 14:
  奖励值: 94.1211
  收益率: 0.3228
  距离: 24.0003
  内存使用: 0.7419
  能量使用: 0.8154
  推理时间: 2.1261秒

批次 15:
  奖励值: 104.1888
  收益率: 0.3500
  距离: 27.4804
  内存使用: 0.4578
  能量使用: 0.8840
  推理时间: 2.2297秒

批次 16:
  奖励值: 110.8534
  收益率: 0.3672
  距离: 29.8805
  内存使用: 0.5488
  能量使用: 0.8639
  推理时间: 2.3902秒

批次 17:
  奖励值: 106.3719
  收益率: 0.3407
  距离: 25.3743
  内存使用: 0.8520
  能量使用: 0.8299
  推理时间: 2.3162秒

批次 18:
  奖励值: 104.8146
  收益率: 0.3527
  距离: 26.0335
  内存使用: 0.7359
  能量使用: 0.8015
  推理时间: 2.2692秒

批次 19:
  奖励值: 109.3050
  收益率: 0.3559
  距离: 28.2322
  内存使用: 0.5456
  能量使用: 0.8121
  推理时间: 2.3512秒

批次 20:
  奖励值: 108.5409
  收益率: 0.3650
  距离: 29.8776
  内存使用: 0.5266
  能量使用: 0.9140
  推理时间: 2.4556秒

批次 21:
  奖励值: 109.4087
  收益率: 0.3604
  距离: 31.9052
  内存使用: 0.5403
  能量使用: 0.9371
  推理时间: 2.4485秒

批次 22:
  奖励值: 111.8255
  收益率: 0.3770
  距离: 30.6830
  内存使用: 0.5655
  能量使用: 0.9004
  推理时间: 2.4719秒

批次 23:
  奖励值: 98.0796
  收益率: 0.3251
  距离: 24.1421
  内存使用: 0.5458
  能量使用: 0.7530
  推理时间: 2.1501秒

批次 24:
  奖励值: 100.2546
  收益率: 0.3386
  距离: 26.5843
  内存使用: 0.5422
  能量使用: 0.7641
  推理时间: 2.2014秒

批次 25:
  奖励值: 101.9551
  收益率: 0.3297
  距离: 25.3739
  内存使用: 0.5401
  能量使用: 0.7759
  推理时间: 2.2332秒

批次 26:
  奖励值: 103.0917
  收益率: 0.3495
  距离: 26.9909
  内存使用: 0.5057
  能量使用: 0.9201
  推理时间: 2.2762秒

批次 27:
  奖励值: 113.5448
  收益率: 0.3660
  距离: 27.9983
  内存使用: 0.5697
  能量使用: 0.9425
  推理时间: 2.4851秒

批次 28:
  奖励值: 98.3476
  收益率: 0.3247
  距离: 25.9226
  内存使用: 0.4275
  能量使用: 0.7546
  推理时间: 2.1186秒

批次 29:
  奖励值: 102.6748
  收益率: 0.3445
  距离: 27.0536
  内存使用: 0.4571
  能量使用: 0.7398
  推理时间: 2.2553秒

批次 30:
  奖励值: 106.5925
  收益率: 0.3471
  距离: 29.9570
  内存使用: 0.5369
  能量使用: 0.9211
  推理时间: 2.3521秒

批次 31:
  奖励值: 111.4002
  收益率: 0.3690
  距离: 31.0942
  内存使用: 0.6113
  能量使用: 0.8941
  推理时间: 2.4750秒

批次 32:
  奖励值: 109.8961
  收益率: 0.3532
  距离: 25.4386
  内存使用: 0.5130
  能量使用: 0.8562
  推理时间: 2.2928秒

批次 33:
  奖励值: 101.0488
  收益率: 0.3371
  距离: 26.0370
  内存使用: 0.4771
  能量使用: 0.7882
  推理时间: 2.2074秒

批次 34:
  奖励值: 94.5634
  收益率: 0.3284
  距离: 26.9207
  内存使用: 0.7839
  能量使用: 0.7684
  推理时间: 2.1466秒

批次 35:
  奖励值: 111.5929
  收益率: 0.3747
  距离: 30.2837
  内存使用: 0.5895
  能量使用: 0.9409
  推理时间: 2.4419秒

批次 36:
  奖励值: 95.4561
  收益率: 0.3206
  距离: 23.7950
  内存使用: 0.7430
  能量使用: 0.7497
  推理时间: 2.1480秒

批次 37:
  奖励值: 98.6906
  收益率: 0.3343
  距离: 25.3261
  内存使用: 0.5208
  能量使用: 0.7718
  推理时间: 2.1978秒

批次 38:
  奖励值: 101.9157
  收益率: 0.3357
  距离: 25.1061
  内存使用: 0.4482
  能量使用: 0.8143
  推理时间: 2.2097秒

批次 39:
  奖励值: 101.7762
  收益率: 0.3377
  距离: 25.3719
  内存使用: 0.5145
  能量使用: 0.7613
  推理时间: 2.2349秒

批次 40:
  奖励值: 112.1105
  收益率: 0.3715
  距离: 25.6449
  内存使用: 0.5312
  能量使用: 0.8029
  推理时间: 2.4750秒

批次 41:
  奖励值: 113.4625
  收益率: 0.3787
  距离: 24.9948
  内存使用: 0.6176
  能量使用: 0.8626
  推理时间: 2.5593秒

批次 42:
  奖励值: 116.4690
  收益率: 0.3691
  距离: 28.7256
  内存使用: 0.6416
  能量使用: 0.8948
  推理时间: 2.4378秒

批次 43:
  奖励值: 98.8523
  收益率: 0.3334
  距离: 28.4888
  内存使用: 0.8182
  能量使用: 0.7884
  推理时间: 2.3577秒

批次 44:
  奖励值: 105.4693
  收益率: 0.3469
  距离: 27.1029
  内存使用: 0.5102
  能量使用: 0.8482
  推理时间: 2.3038秒

批次 45:
  奖励值: 106.4980
  收益率: 0.3630
  距离: 28.8134
  内存使用: 0.4997
  能量使用: 0.8909
  推理时间: 2.4159秒

批次 46:
  奖励值: 103.0108
  收益率: 0.3370
  距离: 25.4606
  内存使用: 0.5200
  能量使用: 0.7978
  推理时间: 2.3086秒

批次 47:
  奖励值: 107.5478
  收益率: 0.3547
  距离: 26.7752
  内存使用: 0.5250
  能量使用: 0.8042
  推理时间: 3.5722秒

批次 48:
  奖励值: 96.1635
  收益率: 0.3254
  距离: 25.6472
  内存使用: 0.4455
  能量使用: 0.7804
  推理时间: 2.2130秒

批次 49:
  奖励值: 105.6000
  收益率: 0.3548
  距离: 29.6771
  内存使用: 0.5438
  能量使用: 0.7792
  推理时间: 2.4062秒

批次 50:
  奖励值: 109.4137
  收益率: 0.3549
  距离: 28.3770
  内存使用: 0.5582
  能量使用: 0.8374
  推理时间: 2.4574秒

批次 51:
  奖励值: 96.9592
  收益率: 0.3341
  距离: 21.6887
  内存使用: 0.7575
  能量使用: 0.8355
  推理时间: 2.2471秒

批次 52:
  奖励值: 102.9612
  收益率: 0.3430
  距离: 28.6678
  内存使用: 0.5119
  能量使用: 0.8979
  推理时间: 2.2551秒

批次 53:
  奖励值: 103.5966
  收益率: 0.3499
  距离: 26.7111
  内存使用: 0.4788
  能量使用: 0.8170
  推理时间: 2.2642秒

批次 54:
  奖励值: 99.4473
  收益率: 0.3365
  距离: 25.8052
  内存使用: 0.4985
  能量使用: 0.8017
  推理时间: 2.1570秒

批次 55:
  奖励值: 95.1021
  收益率: 0.3350
  距离: 30.2291
  内存使用: 0.7593
  能量使用: 0.8075
  推理时间: 2.2154秒

批次 56:
  奖励值: 103.6721
  收益率: 0.3546
  距离: 29.1441
  内存使用: 0.5257
  能量使用: 0.7860
  推理时间: 2.4614秒

批次 57:
  奖励值: 111.0117
  收益率: 0.3629
  距离: 25.3291
  内存使用: 0.4955
  能量使用: 0.7520
  推理时间: 2.3872秒

批次 58:
  奖励值: 108.8380
  收益率: 0.3557
  距离: 27.7907
  内存使用: 0.8708
  能量使用: 0.8626
  推理时间: 2.5310秒

批次 59:
  奖励值: 109.1057
  收益率: 0.3608
  距离: 28.5298
  内存使用: 0.5450
  能量使用: 0.9137
  推理时间: 2.4032秒

批次 60:
  奖励值: 99.9228
  收益率: 0.3458
  距离: 24.7712
  内存使用: 0.4239
  能量使用: 0.7593
  推理时间: 2.2023秒

批次 61:
  奖励值: 105.5278
  收益率: 0.3537
  距离: 27.9031
  内存使用: 0.7892
  能量使用: 0.8520
  推理时间: 2.1792秒

批次 62:
  奖励值: 101.8630
  收益率: 0.3424
  距离: 25.7160
  内存使用: 0.5122
  能量使用: 0.8458
  推理时间: 2.2211秒

批次 63:
  奖励值: 94.3373
  收益率: 0.3217
  距离: 24.7032
  内存使用: 0.4608
  能量使用: 0.7615
  推理时间: 2.1372秒

批次 64:
  奖励值: 98.5338
  收益率: 0.3250
  距离: 23.1472
  内存使用: 0.8152
  能量使用: 0.7700
  推理时间: 2.1395秒

批次 65:
  奖励值: 105.3103
  收益率: 0.3678
  距离: 29.8736
  内存使用: 0.4721
  能量使用: 0.8337
  推理时间: 2.2794秒

批次 66:
  奖励值: 99.3365
  收益率: 0.3407
  距离: 28.2533
  内存使用: 0.5073
  能量使用: 0.7665
  推理时间: 2.1862秒

批次 67:
  奖励值: 106.3533
  收益率: 0.3606
  距离: 26.8356
  内存使用: 0.4375
  能量使用: 0.8203
  推理时间: 2.3171秒

批次 68:
  奖励值: 108.1023
  收益率: 0.3583
  距离: 28.4175
  内存使用: 0.5531
  能量使用: 0.8248
  推理时间: 2.3731秒

批次 69:
  奖励值: 97.4698
  收益率: 0.3180
  距离: 23.4345
  内存使用: 0.8278
  能量使用: 0.7980
  推理时间: 2.1638秒

批次 70:
  奖励值: 98.9619
  收益率: 0.3330
  距离: 25.5548
  内存使用: 0.4553
  能量使用: 0.8660
  推理时间: 2.1806秒

批次 71:
  奖励值: 100.8878
  收益率: 0.3423
  距离: 27.2712
  内存使用: 0.5015
  能量使用: 0.8517
  推理时间: 2.2284秒

批次 72:
  奖励值: 115.5819
  收益率: 0.3730
  距离: 25.0142
  内存使用: 0.5861
  能量使用: 0.8358
  推理时间: 2.4587秒

批次 73:
  奖励值: 111.0190
  收益率: 0.3694
  距离: 28.5775
  内存使用: 0.5758
  能量使用: 0.8087
  推理时间: 2.4190秒

批次 74:
  奖励值: 98.0555
  收益率: 0.3305
  距离: 24.2579
  内存使用: 0.4577
  能量使用: 0.7735
  推理时间: 2.1736秒

批次 75:
  奖励值: 92.7674
  收益率: 0.3175
  距离: 25.4419
  内存使用: 0.4705
  能量使用: 0.7528
  推理时间: 2.0596秒

批次 76:
  奖励值: 102.4595
  收益率: 0.3417
  距离: 24.8152
  内存使用: 0.5029
  能量使用: 0.7631
  推理时间: 2.2427秒

批次 77:
  奖励值: 108.5273
  收益率: 0.3499
  距离: 24.7882
  内存使用: 0.4934
  能量使用: 0.8638
  推理时间: 2.3312秒

批次 78:
  奖励值: 106.0015
  收益率: 0.3491
  距离: 29.4327
  内存使用: 0.4748
  能量使用: 0.8895
  推理时间: 2.3107秒

批次 79:
  奖励值: 117.0885
  收益率: 0.3833
  距离: 30.2571
  内存使用: 0.5737
  能量使用: 0.9922
  推理时间: 2.5595秒

批次 80:
  奖励值: 98.1716
  收益率: 0.3323
  距离: 25.9427
  内存使用: 0.5062
  能量使用: 0.8078
  推理时间: 2.1677秒

批次 81:
  奖励值: 103.2215
  收益率: 0.3573
  距离: 26.8302
  内存使用: 0.5121
  能量使用: 0.8146
  推理时间: 2.2246秒

批次 82:
  奖励值: 98.6401
  收益率: 0.3327
  距离: 24.8093
  内存使用: 0.4767
  能量使用: 0.8339
  推理时间: 2.1512秒

批次 83:
  奖励值: 102.1574
  收益率: 0.3518
  距离: 26.7188
  内存使用: 0.5429
  能量使用: 0.8234
  推理时间: 2.4336秒

批次 84:
  奖励值: 106.3869
  收益率: 0.3510
  距离: 28.1806
  内存使用: 0.5574
  能量使用: 0.8882
  推理时间: 2.3484秒

批次 85:
  奖励值: 99.3743
  收益率: 0.3344
  距离: 24.4025
  内存使用: 0.4940
  能量使用: 0.7549
  推理时间: 2.2414秒

批次 86:
  奖励值: 101.5698
  收益率: 0.3389
  距离: 26.0293
  内存使用: 0.4507
  能量使用: 0.7804
  推理时间: 2.0565秒

批次 87:
  奖励值: 111.8639
  收益率: 0.3735
  距离: 29.1775
  内存使用: 0.5670
  能量使用: 0.9277
  推理时间: 2.5332秒

批次 88:
  奖励值: 112.7531
  收益率: 0.3723
  距离: 27.3117
  内存使用: 0.5104
  能量使用: 0.8717
  推理时间: 2.4541秒

批次 89:
  奖励值: 102.1554
  收益率: 0.3458
  距离: 26.0308
  内存使用: 0.4681
  能量使用: 0.7904
  推理时间: 2.3189秒

批次 90:
  奖励值: 104.2708
  收益率: 0.3540
  距离: 29.5163
  内存使用: 0.5186
  能量使用: 0.8235
  推理时间: 2.2965秒

批次 91:
  奖励值: 95.4701
  收益率: 0.3089
  距离: 21.5605
  内存使用: 0.5425
  能量使用: 0.7567
  推理时间: 2.1867秒

批次 92:
  奖励值: 108.3495
  收益率: 0.3451
  距离: 25.6808
  内存使用: 0.5585
  能量使用: 0.8617
  推理时间: 2.3259秒

批次 93:
  奖励值: 106.1337
  收益率: 0.3418
  距离: 26.9577
  内存使用: 0.5060
  能量使用: 0.8689
  推理时间: 2.2861秒

批次 94:
  奖励值: 105.0388
  收益率: 0.3655
  距离: 25.7459
  内存使用: 0.5349
  能量使用: 0.8416
  推理时间: 2.4070秒

批次 95:
  奖励值: 103.7185
  收益率: 0.3518
  距离: 26.2309
  内存使用: 0.5602
  能量使用: 0.7718
  推理时间: 2.3401秒

批次 96:
  奖励值: 99.2622
  收益率: 0.3319
  距离: 22.9851
  内存使用: 0.4919
  能量使用: 0.7985
  推理时间: 2.2062秒

批次 97:
  奖励值: 106.6200
  收益率: 0.3612
  距离: 25.8170
  内存使用: 0.5483
  能量使用: 0.7947
  推理时间: 2.3369秒

批次 98:
  奖励值: 101.9426
  收益率: 0.3392
  距离: 25.0510
  内存使用: 0.4933
  能量使用: 0.7705
  推理时间: 2.2276秒

批次 99:
  奖励值: 105.7470
  收益率: 0.3457
  距离: 28.1433
  内存使用: 0.5658
  能量使用: 0.7969
  推理时间: 2.2136秒

批次 100:
  奖励值: 103.3243
  收益率: 0.3395
  距离: 29.0209
  内存使用: 0.5186
  能量使用: 0.8118
  推理时间: 2.2212秒


==================== 总结 ====================
平均收益率: 0.3482
平均能量使用: 0.8269
平均推理时间: 2.3154秒
