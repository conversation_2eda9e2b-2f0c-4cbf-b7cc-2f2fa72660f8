推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 67.8336
  收益率: 0.4172
  距离: 19.3916
  内存使用: 0.5870
  能量使用: 0.5543
  推理时间: 1.5845秒

批次 2:
  奖励值: 73.2675
  收益率: 0.4665
  距离: 20.9562
  内存使用: 0.3096
  能量使用: 0.5992
  推理时间: 1.7164秒

批次 3:
  奖励值: 66.7274
  收益率: 0.4417
  距离: 18.7456
  内存使用: 0.2773
  能量使用: 0.5224
  推理时间: 1.5285秒

批次 4:
  奖励值: 72.2604
  收益率: 0.4570
  距离: 19.2273
  内存使用: 0.2949
  能量使用: 0.5569
  推理时间: 1.6477秒

批次 5:
  奖励值: 74.3232
  收益率: 0.4628
  距离: 18.4800
  内存使用: 0.2838
  能量使用: 0.5898
  推理时间: 1.8132秒

批次 6:
  奖励值: 77.2106
  收益率: 0.4587
  距离: 19.0737
  内存使用: 0.2957
  能量使用: 0.6094
  推理时间: 1.6900秒

批次 7:
  奖励值: 71.8408
  收益率: 0.4596
  距离: 19.4975
  内存使用: 0.3130
  能量使用: 0.5079
  推理时间: 1.6201秒

批次 8:
  奖励值: 73.1929
  收益率: 0.4488
  距离: 19.0927
  内存使用: 0.3023
  能量使用: 0.6191
  推理时间: 1.6469秒

批次 9:
  奖励值: 67.2134
  收益率: 0.4273
  距离: 18.1913
  内存使用: 0.2351
  能量使用: 0.5469
  推理时间: 1.5834秒

批次 10:
  奖励值: 73.7261
  收益率: 0.4565
  距离: 18.6605
  内存使用: 0.3357
  能量使用: 0.5516
  推理时间: 1.6409秒

批次 11:
  奖励值: 85.4686
  收益率: 0.5077
  距离: 20.1831
  内存使用: 0.4135
  能量使用: 0.6661
  推理时间: 1.8558秒

批次 12:
  奖励值: 70.9718
  收益率: 0.4462
  距离: 20.6023
  内存使用: 0.2488
  能量使用: 0.6053
  推理时间: 1.6765秒

批次 13:
  奖励值: 68.9092
  收益率: 0.4375
  距离: 20.1828
  内存使用: 0.2643
  能量使用: 0.5344
  推理时间: 1.6219秒

批次 14:
  奖励值: 70.1210
  收益率: 0.4272
  距离: 14.9909
  内存使用: 0.2137
  能量使用: 0.5534
  推理时间: 1.5648秒

批次 15:
  奖励值: 74.7263
  收益率: 0.4782
  距离: 21.0098
  内存使用: 0.3044
  能量使用: 0.6332
  推理时间: 1.6899秒

批次 16:
  奖励值: 62.6138
  收益率: 0.4084
  距离: 15.9741
  内存使用: 0.4952
  能量使用: 0.5030
  推理时间: 1.4503秒

批次 17:
  奖励值: 81.3198
  收益率: 0.5022
  距离: 22.2197
  内存使用: 0.3248
  能量使用: 0.6160
  推理时间: 1.8746秒

批次 18:
  奖励值: 67.1598
  收益率: 0.4425
  距离: 17.9514
  内存使用: 0.2284
  能量使用: 0.5006
  推理时间: 1.4962秒

批次 19:
  奖励值: 75.8876
  收益率: 0.4615
  距离: 18.7124
  内存使用: 0.3222
  能量使用: 0.5902
  推理时间: 1.6694秒

批次 20:
  奖励值: 77.0545
  收益率: 0.5084
  距离: 20.8757
  内存使用: 0.3434
  能量使用: 0.6719
  推理时间: 1.7074秒

批次 21:
  奖励值: 73.7331
  收益率: 0.4733
  距离: 20.1890
  内存使用: 0.3253
  能量使用: 0.6195
  推理时间: 1.7044秒

批次 22:
  奖励值: 76.6952
  收益率: 0.4688
  距离: 18.6838
  内存使用: 0.3104
  能量使用: 0.6154
  推理时间: 1.6806秒

批次 23:
  奖励值: 76.2647
  收益率: 0.4676
  距离: 20.9190
  内存使用: 0.3208
  能量使用: 0.6308
  推理时间: 1.6719秒

批次 24:
  奖励值: 77.5690
  收益率: 0.4799
  距离: 18.0661
  内存使用: 0.3382
  能量使用: 0.6577
  推理时间: 1.7547秒

批次 25:
  奖励值: 70.1081
  收益率: 0.4383
  距离: 18.2103
  内存使用: 0.2158
  能量使用: 0.5576
  推理时间: 1.6156秒

批次 26:
  奖励值: 72.5598
  收益率: 0.4550
  距离: 18.8377
  内存使用: 0.2943
  能量使用: 0.5485
  推理时间: 1.6342秒

批次 27:
  奖励值: 78.6519
  收益率: 0.4751
  距离: 19.6012
  内存使用: 0.3032
  能量使用: 0.5919
  推理时间: 1.7477秒

批次 28:
  奖励值: 71.3804
  收益率: 0.4651
  距离: 19.1116
  内存使用: 0.2743
  能量使用: 0.6533
  推理时间: 1.5884秒

批次 29:
  奖励值: 69.4202
  收益率: 0.4420
  距离: 18.7559
  内存使用: 0.2747
  能量使用: 0.5291
  推理时间: 1.5864秒

批次 30:
  奖励值: 73.8778
  收益率: 0.4452
  距离: 19.5617
  内存使用: 0.2785
  能量使用: 0.6042
  推理时间: 1.6603秒

批次 31:
  奖励值: 77.3463
  收益率: 0.4903
  距离: 20.8902
  内存使用: 0.3738
  能量使用: 0.6433
  推理时间: 1.7212秒

批次 32:
  奖励值: 75.9470
  收益率: 0.4592
  距离: 17.0458
  内存使用: 0.2888
  能量使用: 0.5716
  推理时间: 1.6830秒

批次 33:
  奖励值: 66.3813
  收益率: 0.4202
  距离: 17.0941
  内存使用: 0.1990
  能量使用: 0.5442
  推理时间: 1.4819秒

批次 34:
  奖励值: 70.2513
  收益率: 0.4387
  距离: 17.4746
  内存使用: 0.2995
  能量使用: 0.5234
  推理时间: 1.5547秒

批次 35:
  奖励值: 81.9045
  收益率: 0.5096
  距离: 21.6510
  内存使用: 0.4455
  能量使用: 0.6944
  推理时间: 1.9189秒

批次 36:
  奖励值: 77.1806
  收益率: 0.4790
  距离: 17.9217
  内存使用: 0.2952
  能量使用: 0.6220
  推理时间: 1.7590秒

批次 37:
  奖励值: 70.5635
  收益率: 0.4522
  距离: 16.0562
  内存使用: 0.2589
  能量使用: 0.5580
  推理时间: 1.6578秒

批次 38:
  奖励值: 73.1689
  收益率: 0.4553
  距离: 18.9613
  内存使用: 0.2630
  能量使用: 0.6379
  推理时间: 1.7133秒

批次 39:
  奖励值: 71.1661
  收益率: 0.4556
  距离: 18.6660
  内存使用: 0.6072
  能量使用: 0.5945
  推理时间: 1.6845秒

批次 40:
  奖励值: 64.3797
  收益率: 0.4203
  距离: 19.2292
  内存使用: 0.2747
  能量使用: 0.5367
  推理时间: 1.5363秒

批次 41:
  奖励值: 69.1155
  收益率: 0.4485
  距离: 19.5638
  内存使用: 0.2069
  能量使用: 0.5983
  推理时间: 1.5882秒

批次 42:
  奖励值: 77.3570
  收益率: 0.4632
  距离: 19.3975
  内存使用: 0.2748
  能量使用: 0.6138
  推理时间: 1.7308秒

批次 43:
  奖励值: 77.5002
  收益率: 0.4667
  距离: 19.2822
  内存使用: 0.3245
  能量使用: 0.5930
  推理时间: 1.7392秒

批次 44:
  奖励值: 79.2843
  收益率: 0.4972
  距离: 19.0040
  内存使用: 0.3370
  能量使用: 0.6905
  推理时间: 1.8161秒

批次 45:
  奖励值: 72.3788
  收益率: 0.4518
  距离: 16.3588
  内存使用: 0.2526
  能量使用: 0.6311
  推理时间: 1.6163秒

批次 46:
  奖励值: 80.2482
  收益率: 0.5022
  距离: 22.3814
  内存使用: 0.4129
  能量使用: 0.7094
  推理时间: 1.8664秒

批次 47:
  奖励值: 66.8316
  收益率: 0.4208
  距离: 17.0108
  内存使用: 0.2283
  能量使用: 0.4978
  推理时间: 1.4934秒

批次 48:
  奖励值: 76.0438
  收益率: 0.4723
  距离: 20.9799
  内存使用: 0.3186
  能量使用: 0.6140
  推理时间: 1.7346秒

批次 49:
  奖励值: 69.5383
  收益率: 0.4585
  距离: 21.2866
  内存使用: 0.2860
  能量使用: 0.5508
  推理时间: 1.5887秒

批次 50:
  奖励值: 75.7805
  收益率: 0.4576
  距离: 17.8893
  内存使用: 0.3501
  能量使用: 0.5997
  推理时间: 1.7107秒

批次 51:
  奖励值: 74.4957
  收益率: 0.4620
  距离: 17.8967
  内存使用: 0.3082
  能量使用: 0.6037
  推理时间: 1.7116秒

批次 52:
  奖励值: 73.4439
  收益率: 0.4809
  距离: 20.6733
  内存使用: 0.2837
  能量使用: 0.5552
  推理时间: 1.7198秒

批次 53:
  奖励值: 71.0361
  收益率: 0.4608
  距离: 19.2783
  内存使用: 0.2902
  能量使用: 0.5594
  推理时间: 1.5885秒

批次 54:
  奖励值: 74.8963
  收益率: 0.4575
  距离: 19.2619
  内存使用: 0.3394
  能量使用: 0.6061
  推理时间: 1.6602秒

批次 55:
  奖励值: 83.8154
  收益率: 0.4976
  距离: 20.0713
  内存使用: 0.3947
  能量使用: 0.7032
  推理时间: 1.8790秒

批次 56:
  奖励值: 78.0097
  收益率: 0.4911
  距离: 18.7868
  内存使用: 0.3111
  能量使用: 0.6828
  推理时间: 1.8135秒

批次 57:
  奖励值: 74.7248
  收益率: 0.4670
  距离: 19.4442
  内存使用: 0.2915
  能量使用: 0.6146
  推理时间: 1.7068秒

批次 58:
  奖励值: 76.5398
  收益率: 0.4952
  距离: 19.9593
  内存使用: 0.3344
  能量使用: 0.6266
  推理时间: 1.6746秒

批次 59:
  奖励值: 75.1489
  收益率: 0.4832
  距离: 20.9786
  内存使用: 0.3172
  能量使用: 0.6064
  推理时间: 1.7453秒

批次 60:
  奖励值: 80.3419
  收益率: 0.5030
  距离: 20.5536
  内存使用: 0.3153
  能量使用: 0.6473
  推理时间: 1.7702秒

批次 61:
  奖励值: 72.4534
  收益率: 0.4452
  距离: 18.2699
  内存使用: 0.2916
  能量使用: 0.5317
  推理时间: 1.5975秒

批次 62:
  奖励值: 80.9070
  收益率: 0.4927
  距离: 20.3979
  内存使用: 0.3620
  能量使用: 0.6262
  推理时间: 1.8301秒

批次 63:
  奖励值: 82.0280
  收益率: 0.5005
  距离: 23.6672
  内存使用: 0.3994
  能量使用: 0.6774
  推理时间: 1.8676秒

批次 64:
  奖励值: 71.1257
  收益率: 0.4575
  距离: 17.5751
  内存使用: 0.2378
  能量使用: 0.5358
  推理时间: 1.5806秒

批次 65:
  奖励值: 70.3068
  收益率: 0.4472
  距离: 18.8141
  内存使用: 0.2196
  能量使用: 0.5527
  推理时间: 1.5584秒

批次 66:
  奖励值: 70.3781
  收益率: 0.4238
  距离: 17.0481
  内存使用: 0.2516
  能量使用: 0.5441
  推理时间: 1.5629秒

批次 67:
  奖励值: 83.2938
  收益率: 0.4909
  距离: 20.6620
  内存使用: 0.3074
  能量使用: 0.6430
  推理时间: 1.8057秒

批次 68:
  奖励值: 74.1331
  收益率: 0.4486
  距离: 18.4747
  内存使用: 0.2857
  能量使用: 0.5734
  推理时间: 1.6186秒

批次 69:
  奖励值: 65.8261
  收益率: 0.4253
  距离: 16.7460
  内存使用: 0.2201
  能量使用: 0.5819
  推理时间: 1.4870秒

批次 70:
  奖励值: 74.6938
  收益率: 0.4777
  距离: 20.8685
  内存使用: 0.3414
  能量使用: 0.6026
  推理时间: 1.7456秒

批次 71:
  奖励值: 73.5311
  收益率: 0.4498
  距离: 17.8556
  内存使用: 0.3093
  能量使用: 0.5729
  推理时间: 1.6236秒

批次 72:
  奖励值: 79.8143
  收益率: 0.4737
  距离: 20.9847
  内存使用: 0.3241
  能量使用: 0.6626
  推理时间: 1.7785秒

批次 73:
  奖励值: 72.6737
  收益率: 0.4494
  距离: 19.9651
  内存使用: 0.2759
  能量使用: 0.6116
  推理时间: 1.6479秒

批次 74:
  奖励值: 64.3144
  收益率: 0.4227
  距离: 17.5582
  内存使用: 0.2128
  能量使用: 0.6003
  推理时间: 1.5118秒

批次 75:
  奖励值: 72.3993
  收益率: 0.4348
  距离: 16.9999
  内存使用: 0.2674
  能量使用: 0.5341
  推理时间: 1.6118秒

批次 76:
  奖励值: 73.8110
  收益率: 0.4562
  距离: 19.3071
  内存使用: 0.2759
  能量使用: 0.5951
  推理时间: 1.6683秒

批次 77:
  奖励值: 73.1220
  收益率: 0.4474
  距离: 17.9888
  内存使用: 0.2163
  能量使用: 0.5510
  推理时间: 1.6466秒

批次 78:
  奖励值: 73.1250
  收益率: 0.4539
  距离: 17.5669
  内存使用: 0.2263
  能量使用: 0.5568
  推理时间: 1.6519秒

批次 79:
  奖励值: 73.7545
  收益率: 0.4519
  距离: 19.5688
  内存使用: 0.3372
  能量使用: 0.5850
  推理时间: 1.7081秒

批次 80:
  奖励值: 79.8284
  收益率: 0.5019
  距离: 20.9865
  内存使用: 0.3710
  能量使用: 0.5857
  推理时间: 1.7813秒

批次 81:
  奖励值: 75.5069
  收益率: 0.4683
  距离: 19.2127
  内存使用: 0.3868
  能量使用: 0.5868
  推理时间: 1.7391秒

批次 82:
  奖励值: 74.2966
  收益率: 0.4722
  距离: 18.9767
  内存使用: 0.3024
  能量使用: 0.5277
  推理时间: 1.6948秒

批次 83:
  奖励值: 78.2532
  收益率: 0.4880
  距离: 21.1680
  内存使用: 0.3173
  能量使用: 0.6084
  推理时间: 1.7706秒

批次 84:
  奖励值: 65.9148
  收益率: 0.4344
  距离: 20.3604
  内存使用: 0.2483
  能量使用: 0.5616
  推理时间: 1.5958秒

批次 85:
  奖励值: 80.0138
  收益率: 0.4992
  距离: 20.3998
  内存使用: 0.3334
  能量使用: 0.6393
  推理时间: 1.8263秒

批次 86:
  奖励值: 75.3610
  收益率: 0.4826
  距离: 20.1561
  内存使用: 0.2911
  能量使用: 0.6308
  推理时间: 1.7463秒

批次 87:
  奖励值: 75.4274
  收益率: 0.4604
  距离: 17.0362
  内存使用: 0.2971
  能量使用: 0.6118
  推理时间: 1.6713秒

批次 88:
  奖励值: 63.4202
  收益率: 0.3980
  距离: 18.3822
  内存使用: 0.2425
  能量使用: 0.5348
  推理时间: 1.4829秒

批次 89:
  奖励值: 74.3047
  收益率: 0.4532
  距离: 18.1751
  内存使用: 0.2757
  能量使用: 0.5399
  推理时间: 1.6314秒

批次 90:
  奖励值: 73.8293
  收益率: 0.4531
  距离: 17.6365
  内存使用: 0.2815
  能量使用: 0.6234
  推理时间: 1.8086秒

批次 91:
  奖励值: 73.2093
  收益率: 0.4814
  距离: 22.9437
  内存使用: 0.3775
  能量使用: 0.6602
  推理时间: 1.7674秒

批次 92:
  奖励值: 75.7275
  收益率: 0.4890
  距离: 20.7850
  内存使用: 0.3507
  能量使用: 0.6092
  推理时间: 1.6737秒

批次 93:
  奖励值: 80.5966
  收益率: 0.4986
  距离: 19.7154
  内存使用: 0.3148
  能量使用: 0.5766
  推理时间: 1.7974秒

批次 94:
  奖励值: 79.3977
  收益率: 0.4853
  距离: 22.3656
  内存使用: 0.3692
  能量使用: 0.6698
  推理时间: 1.7899秒

批次 95:
  奖励值: 82.0802
  收益率: 0.5035
  距离: 21.1260
  内存使用: 0.3169
  能量使用: 0.5967
  推理时间: 1.8302秒

批次 96:
  奖励值: 74.8405
  收益率: 0.4793
  距离: 21.0242
  内存使用: 0.3541
  能量使用: 0.5408
  推理时间: 1.6942秒

批次 97:
  奖励值: 71.4541
  收益率: 0.4382
  距离: 18.8565
  内存使用: 0.6307
  能量使用: 0.6046
  推理时间: 1.5984秒

批次 98:
  奖励值: 72.2905
  收益率: 0.4565
  距离: 19.3463
  内存使用: 0.2916
  能量使用: 0.5812
  推理时间: 1.6218秒

批次 99:
  奖励值: 72.1195
  收益率: 0.4695
  距离: 19.4411
  内存使用: 0.3226
  能量使用: 0.5974
  推理时间: 1.7318秒

批次 100:
  奖励值: 78.7525
  收益率: 0.4848
  距离: 19.7055
  内存使用: 0.3188
  能量使用: 0.6708
  推理时间: 1.7285秒


==================== 总结 ====================
平均收益率: 0.4629
平均能量使用: 0.5947
平均推理时间: 1.6807秒
