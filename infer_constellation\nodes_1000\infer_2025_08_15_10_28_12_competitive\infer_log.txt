推理数据数量: 100
每个序列任务数量: 1000
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 111.2054
  收益率: 0.2881
  距离: 28.8443
  内存使用: 0.6060
  能量使用: 0.9389
  推理时间: 2.2012秒

批次 2:
  奖励值: 121.0118
  收益率: 0.3012
  距离: 30.2417
  内存使用: 0.6383
  能量使用: 0.8964
  推理时间: 2.5226秒

批次 3:
  奖励值: 120.4546
  收益率: 0.2973
  距离: 30.0053
  内存使用: 0.5699
  能量使用: 0.9156
  推理时间: 2.5535秒

批次 4:
  奖励值: 107.7503
  收益率: 0.2677
  距离: 27.3249
  内存使用: 0.8788
  能量使用: 0.8957
  推理时间: 2.3746秒

批次 5:
  奖励值: 100.1629
  收益率: 0.2519
  距离: 24.8121
  内存使用: 0.8298
  能量使用: 0.8324
  推理时间: 2.2219秒

批次 6:
  奖励值: 113.2919
  收益率: 0.2855
  距离: 28.8111
  内存使用: 0.5755
  能量使用: 0.8297
  推理时间: 2.5533秒

批次 7:
  奖励值: 110.8902
  收益率: 0.2867
  距离: 30.9105
  内存使用: 0.8836
  能量使用: 0.8255
  推理时间: 2.5562秒

批次 8:
  奖励值: 108.4560
  收益率: 0.2742
  距离: 27.7002
  内存使用: 0.5800
  能量使用: 0.8078
  推理时间: 2.3369秒

批次 9:
  奖励值: 110.9591
  收益率: 0.2797
  距离: 28.7047
  内存使用: 0.5616
  能量使用: 0.8154
  推理时间: 2.3013秒

批次 10:
  奖励值: 118.6455
  收益率: 0.2929
  距离: 29.0498
  内存使用: 0.5648
  能量使用: 0.8943
  推理时间: 2.4180秒

批次 11:
  奖励值: 108.5763
  收益率: 0.2623
  距离: 24.3568
  内存使用: 0.5279
  能量使用: 0.7628
  推理时间: 2.3961秒

批次 12:
  奖励值: 125.4969
  收益率: 0.3215
  距离: 33.5818
  内存使用: 0.6639
  能量使用: 0.9603
  推理时间: 2.5106秒

批次 13:
  奖励值: 112.5306
  收益率: 0.2828
  距离: 28.0706
  内存使用: 0.6295
  能量使用: 0.8387
  推理时间: 2.2674秒

批次 14:
  奖励值: 115.5781
  收益率: 0.2911
  距离: 27.7441
  内存使用: 0.6206
  能量使用: 0.8791
  推理时间: 2.6000秒

批次 15:
  奖励值: 111.8443
  收益率: 0.2836
  距离: 27.9179
  内存使用: 0.5660
  能量使用: 0.8276
  推理时间: 2.2808秒

批次 16:
  奖励值: 116.1495
  收益率: 0.2965
  距离: 32.7840
  内存使用: 0.6563
  能量使用: 0.8606
  推理时间: 2.6342秒

批次 17:
  奖励值: 110.7438
  收益率: 0.2814
  距离: 30.1632
  内存使用: 0.4902
  能量使用: 0.8590
  推理时间: 2.3686秒

批次 18:
  奖励值: 118.1805
  收益率: 0.3010
  距离: 29.8701
  内存使用: 0.6141
  能量使用: 0.9548
  推理时间: 2.8323秒

批次 19:
  奖励值: 111.2192
  收益率: 0.2809
  距离: 29.3921
  内存使用: 0.8995
  能量使用: 0.8699
  推理时间: 2.3512秒

批次 20:
  奖励值: 127.4223
  收益率: 0.3106
  距离: 27.5281
  内存使用: 0.6766
  能量使用: 0.9506
  推理时间: 2.4260秒

批次 21:
  奖励值: 124.1481
  收益率: 0.2962
  距离: 30.0690
  内存使用: 0.6121
  能量使用: 1.0227
  推理时间: 2.6069秒

批次 22:
  奖励值: 122.6155
  收益率: 0.3012
  距离: 27.6157
  内存使用: 0.6043
  能量使用: 0.9490
  推理时间: 2.5750秒

批次 23:
  奖励值: 115.6349
  收益率: 0.2911
  距离: 27.2634
  内存使用: 0.5597
  能量使用: 0.9071
  推理时间: 2.4610秒

批次 24:
  奖励值: 109.6209
  收益率: 0.2714
  距离: 26.0515
  内存使用: 0.8735
  能量使用: 0.8331
  推理时间: 2.3378秒

批次 25:
  奖励值: 120.6270
  收益率: 0.2893
  距离: 27.9035
  内存使用: 0.6033
  能量使用: 0.8391
  推理时间: 2.6251秒

批次 26:
  奖励值: 122.7986
  收益率: 0.3097
  距离: 29.9667
  内存使用: 0.6477
  能量使用: 0.9883
  推理时间: 2.5421秒

批次 27:
  奖励值: 115.0120
  收益率: 0.2935
  距离: 28.4807
  内存使用: 0.6027
  能量使用: 0.8925
  推理时间: 2.4536秒

批次 28:
  奖励值: 116.6781
  收益率: 0.2899
  距离: 27.8519
  内存使用: 0.5445
  能量使用: 0.8473
  推理时间: 2.3841秒

批次 29:
  奖励值: 122.3646
  收益率: 0.3085
  距离: 33.1004
  内存使用: 0.6178
  能量使用: 0.9526
  推理时间: 2.9142秒

批次 30:
  奖励值: 121.2761
  收益率: 0.2950
  距离: 30.6464
  内存使用: 0.6319
  能量使用: 0.9694
  推理时间: 2.4894秒

批次 31:
  奖励值: 112.9289
  收益率: 0.2842
  距离: 27.8334
  内存使用: 0.5191
  能量使用: 0.7897
  推理时间: 2.1711秒

批次 32:
  奖励值: 106.6986
  收益率: 0.2654
  距离: 25.8319
  内存使用: 0.5168
  能量使用: 0.8540
  推理时间: 2.2340秒

批次 33:
  奖励值: 115.4104
  收益率: 0.2893
  距离: 30.3992
  内存使用: 0.8745
  能量使用: 0.9055
  推理时间: 2.5741秒

批次 34:
  奖励值: 110.8278
  收益率: 0.2767
  距离: 29.0086
  内存使用: 0.8838
  能量使用: 0.8521
  推理时间: 2.4648秒

批次 35:
  奖励值: 131.7153
  收益率: 0.3279
  距离: 30.8650
  内存使用: 0.6249
  能量使用: 1.0795
  推理时间: 2.6166秒

批次 36:
  奖励值: 106.9764
  收益率: 0.2656
  距离: 23.8401
  内存使用: 0.7885
  能量使用: 0.8185
  推理时间: 2.0751秒

批次 37:
  奖励值: 113.3260
  收益率: 0.2865
  距离: 29.2384
  内存使用: 0.5944
  能量使用: 0.9152
  推理时间: 2.4311秒

批次 38:
  奖励值: 119.7536
  收益率: 0.3045
  距离: 31.3256
  内存使用: 0.6364
  能量使用: 0.9308
  推理时间: 2.5644秒

批次 39:
  奖励值: 106.8232
  收益率: 0.2725
  距离: 23.9425
  内存使用: 0.5710
  能量使用: 0.8400
  推理时间: 2.2839秒

批次 40:
  奖励值: 113.7480
  收益率: 0.2823
  距离: 27.0275
  内存使用: 0.5812
  能量使用: 0.8953
  推理时间: 2.3514秒

批次 41:
  奖励值: 110.5857
  收益率: 0.2759
  距离: 26.0763
  内存使用: 0.8331
  能量使用: 0.9192
  推理时间: 2.4490秒

批次 42:
  奖励值: 120.3407
  收益率: 0.2958
  距离: 28.1842
  内存使用: 0.5704
  能量使用: 0.9639
  推理时间: 2.4546秒

批次 43:
  奖励值: 110.0799
  收益率: 0.2821
  距离: 27.8260
  内存使用: 0.5973
  能量使用: 0.8168
  推理时间: 2.3145秒

批次 44:
  奖励值: 107.2201
  收益率: 0.2750
  距离: 28.4878
  内存使用: 0.8504
  能量使用: 0.8216
  推理时间: 2.2118秒

批次 45:
  奖励值: 122.4809
  收益率: 0.3084
  距离: 31.3586
  内存使用: 0.5841
  能量使用: 0.8956
  推理时间: 2.6337秒

批次 46:
  奖励值: 114.8072
  收益率: 0.2905
  距离: 28.2271
  内存使用: 0.5986
  能量使用: 0.9599
  推理时间: 2.3977秒

批次 47:
  奖励值: 109.7638
  收益率: 0.2750
  距离: 28.1475
  内存使用: 0.8119
  能量使用: 0.9129
  推理时间: 2.1593秒

批次 48:
  奖励值: 106.8210
  收益率: 0.2781
  距离: 27.8028
  内存使用: 0.8941
  能量使用: 0.8611
  推理时间: 2.3606秒

批次 49:
  奖励值: 105.4469
  收益率: 0.2667
  距离: 29.2520
  内存使用: 0.8686
  能量使用: 0.7856
  推理时间: 2.4863秒

批次 50:
  奖励值: 131.8686
  收益率: 0.3232
  距离: 33.2795
  内存使用: 0.6876
  能量使用: 1.0957
  推理时间: 2.8016秒

批次 51:
  奖励值: 129.6293
  收益率: 0.3144
  距离: 27.3697
  内存使用: 0.6493
  能量使用: 0.9223
  推理时间: 2.6351秒

批次 52:
  奖励值: 124.9677
  收益率: 0.3078
  距离: 29.3869
  内存使用: 0.6974
  能量使用: 0.9801
  推理时间: 2.5870秒

批次 53:
  奖励值: 118.1905
  收益率: 0.3025
  距离: 27.3522
  内存使用: 0.5747
  能量使用: 0.8921
  推理时间: 2.4923秒

批次 54:
  奖励值: 115.2669
  收益率: 0.2876
  距离: 29.0611
  内存使用: 0.6433
  能量使用: 0.9617
  推理时间: 2.4291秒

批次 55:
  奖励值: 111.0782
  收益率: 0.2765
  距离: 26.6638
  内存使用: 0.8985
  能量使用: 0.8812
  推理时间: 2.3728秒

批次 56:
  奖励值: 111.6459
  收益率: 0.2781
  距离: 28.8798
  内存使用: 0.5513
  能量使用: 0.8771
  推理时间: 2.3772秒

批次 57:
  奖励值: 115.2501
  收益率: 0.2975
  距离: 27.6939
  内存使用: 0.6227
  能量使用: 0.9832
  推理时间: 2.4981秒

批次 58:
  奖励值: 118.4228
  收益率: 0.2939
  距离: 27.9201
  内存使用: 0.8972
  能量使用: 0.9371
  推理时间: 2.5456秒

批次 59:
  奖励值: 111.4048
  收益率: 0.2813
  距离: 27.7930
  内存使用: 0.8731
  能量使用: 0.9243
  推理时间: 2.3594秒

批次 60:
  奖励值: 101.3585
  收益率: 0.2607
  距离: 28.4820
  内存使用: 0.8034
  能量使用: 0.7976
  推理时间: 2.2402秒

批次 61:
  奖励值: 107.4645
  收益率: 0.2692
  距离: 25.6308
  内存使用: 0.6181
  能量使用: 0.8221
  推理时间: 2.3046秒

批次 62:
  奖励值: 106.9403
  收益率: 0.2687
  距离: 26.7062
  内存使用: 0.5922
  能量使用: 0.8210
  推理时间: 2.3135秒

批次 63:
  奖励值: 115.8317
  收益率: 0.2836
  距离: 27.6818
  内存使用: 0.6191
  能量使用: 0.8357
  推理时间: 2.4141秒

批次 64:
  奖励值: 119.0522
  收益率: 0.3019
  距离: 31.6138
  内存使用: 0.5554
  能量使用: 0.9262
  推理时间: 2.5898秒

批次 65:
  奖励值: 119.8290
  收益率: 0.2981
  距离: 29.4065
  内存使用: 0.5835
  能量使用: 0.9518
  推理时间: 2.5164秒

批次 66:
  奖励值: 108.6295
  收益率: 0.2717
  距离: 26.8945
  内存使用: 0.5979
  能量使用: 0.8487
  推理时间: 2.3041秒

批次 67:
  奖励值: 118.7386
  收益率: 0.2952
  距离: 27.3300
  内存使用: 0.6239
  能量使用: 0.9689
  推理时间: 2.5036秒

批次 68:
  奖励值: 112.9585
  收益率: 0.2849
  距离: 28.4293
  内存使用: 0.5716
  能量使用: 0.8822
  推理时间: 2.4044秒

批次 69:
  奖励值: 100.8366
  收益率: 0.2550
  距离: 27.5940
  内存使用: 0.5238
  能量使用: 0.8090
  推理时间: 2.2014秒

批次 70:
  奖励值: 123.5947
  收益率: 0.3122
  距离: 33.2403
  内存使用: 0.6062
  能量使用: 0.9641
  推理时间: 2.6161秒

批次 71:
  奖励值: 117.0358
  收益率: 0.2917
  距离: 30.0324
  内存使用: 0.5649
  能量使用: 0.9311
  推理时间: 2.4548秒

批次 72:
  奖励值: 106.6633
  收益率: 0.2678
  距离: 25.9018
  内存使用: 0.5095
  能量使用: 0.8620
  推理时间: 2.3096秒

批次 73:
  奖励值: 125.4414
  收益率: 0.3200
  距离: 29.5270
  内存使用: 0.6038
  能量使用: 0.9864
  推理时间: 2.6655秒

批次 74:
  奖励值: 120.4217
  收益率: 0.2918
  距离: 30.4069
  内存使用: 0.6444
  能量使用: 0.9052
  推理时间: 2.5442秒

批次 75:
  奖励值: 112.8210
  收益率: 0.2699
  距离: 27.9596
  内存使用: 0.5785
  能量使用: 0.8200
  推理时间: 2.3842秒

批次 76:
  奖励值: 116.3441
  收益率: 0.2940
  距离: 27.9691
  内存使用: 0.8880
  能量使用: 0.8547
  推理时间: 2.4865秒

批次 77:
  奖励值: 118.0899
  收益率: 0.2897
  距离: 31.3423
  内存使用: 0.6175
  能量使用: 0.8603
  推理时间: 2.5705秒

批次 78:
  奖励值: 115.1458
  收益率: 0.2908
  距离: 28.6021
  内存使用: 0.5752
  能量使用: 0.9311
  推理时间: 2.4088秒

批次 79:
  奖励值: 100.6627
  收益率: 0.2538
  距离: 27.0846
  内存使用: 0.7677
  能量使用: 0.8223
  推理时间: 2.2378秒

批次 80:
  奖励值: 112.8644
  收益率: 0.2806
  距离: 28.9898
  内存使用: 0.8202
  能量使用: 0.8610
  推理时间: 2.2686秒

批次 81:
  奖励值: 117.6403
  收益率: 0.2900
  距离: 27.5666
  内存使用: 0.8768
  能量使用: 0.8760
  推理时间: 2.5120秒

批次 82:
  奖励值: 109.4550
  收益率: 0.2796
  距离: 28.3804
  内存使用: 0.5724
  能量使用: 0.9372
  推理时间: 2.3545秒

批次 83:
  奖励值: 123.5792
  收益率: 0.3108
  距离: 31.4810
  内存使用: 0.6306
  能量使用: 0.9301
  推理时间: 2.7425秒

批次 84:
  奖励值: 117.2800
  收益率: 0.2972
  距离: 28.5761
  内存使用: 0.5618
  能量使用: 0.9020
  推理时间: 2.3934秒

批次 85:
  奖励值: 112.1660
  收益率: 0.2829
  距离: 29.7195
  内存使用: 0.8968
  能量使用: 0.8517
  推理时间: 2.4569秒

批次 86:
  奖励值: 113.1285
  收益率: 0.2961
  距离: 30.6599
  内存使用: 0.5616
  能量使用: 0.9214
  推理时间: 2.3047秒

批次 87:
  奖励值: 119.9150
  收益率: 0.3091
  距离: 28.5830
  内存使用: 0.6006
  能量使用: 0.9864
  推理时间: 2.3456秒

批次 88:
  奖励值: 116.9189
  收益率: 0.2948
  距离: 27.4383
  内存使用: 0.6422
  能量使用: 0.8817
  推理时间: 2.5803秒

批次 89:
  奖励值: 112.7785
  收益率: 0.2849
  距离: 31.9581
  内存使用: 0.6069
  能量使用: 0.8780
  推理时间: 2.5888秒

批次 90:
  奖励值: 113.0497
  收益率: 0.2866
  距离: 32.2866
  内存使用: 0.6147
  能量使用: 0.9992
  推理时间: 2.4663秒

批次 91:
  奖励值: 121.3174
  收益率: 0.3058
  距离: 29.3636
  内存使用: 0.6433
  能量使用: 0.9583
  推理时间: 2.6435秒

批次 92:
  奖励值: 101.0524
  收益率: 0.2564
  距离: 27.2396
  内存使用: 0.8220
  能量使用: 0.8337
  推理时间: 2.2589秒

批次 93:
  奖励值: 116.6759
  收益率: 0.2851
  距离: 26.8969
  内存使用: 0.8992
  能量使用: 0.8520
  推理时间: 3.1928秒

批次 94:
  奖励值: 109.3686
  收益率: 0.2710
  距离: 28.1268
  内存使用: 0.8980
  能量使用: 0.8380
  推理时间: 2.3373秒

批次 95:
  奖励值: 105.4623
  收益率: 0.2613
  距离: 25.9181
  内存使用: 0.5298
  能量使用: 0.8273
  推理时间: 2.2983秒

批次 96:
  奖励值: 121.4524
  收益率: 0.3031
  距离: 28.1139
  内存使用: 0.8704
  能量使用: 0.9110
  推理时间: 2.7612秒

批次 97:
  奖励值: 103.5533
  收益率: 0.2603
  距离: 28.1021
  内存使用: 0.5212
  能量使用: 0.8701
  推理时间: 2.2709秒

批次 98:
  奖励值: 115.5830
  收益率: 0.2971
  距离: 30.7019
  内存使用: 0.6036
  能量使用: 0.9284
  推理时间: 2.4304秒

批次 99:
  奖励值: 107.5806
  收益率: 0.2685
  距离: 26.7951
  内存使用: 0.8297
  能量使用: 0.8141
  推理时间: 2.1936秒

批次 100:
  奖励值: 112.3483
  收益率: 0.2874
  距离: 29.7863
  内存使用: 0.5932
  能量使用: 0.9240
  推理时间: 2.3989秒


==================== 总结 ====================
平均收益率: 0.2873
平均能量使用: 0.8927
平均推理时间: 2.4470秒
