推理数据数量: 100
每个序列任务数量: 400
星座卫星数量: 3
星座模式: cooperative
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_13_15_04_48

批次 1:
  奖励值: 89.5188
  收益率: 0.5518
  距离: 26.3463
  内存使用: 0.4518
  能量使用: 0.7644
  推理时间: 2.1167秒

批次 2:
  奖励值: 81.8947
  收益率: 0.5219
  距离: 23.5211
  内存使用: 0.3546
  能量使用: 0.7348
  推理时间: 1.9561秒

批次 3:
  奖励值: 75.2943
  收益率: 0.5035
  距离: 22.8033
  内存使用: 0.3642
  能量使用: 0.6440
  推理时间: 1.7912秒

批次 4:
  奖励值: 82.5753
  收益率: 0.5297
  距离: 24.6255
  内存使用: 0.3736
  能量使用: 0.6504
  推理时间: 1.9645秒

批次 5:
  奖励值: 73.7385
  收益率: 0.4590
  距离: 18.1448
  内存使用: 0.3269
  能量使用: 0.6512
  推理时间: 3.2225秒

批次 6:
  奖励值: 98.5117
  收益率: 0.5950
  距离: 27.8204
  内存使用: 0.4808
  能量使用: 0.8753
  推理时间: 2.1792秒

批次 7:
  奖励值: 77.8998
  收益率: 0.5017
  距离: 22.2428
  内存使用: 0.3867
  能量使用: 0.5709
  推理时间: 1.7582秒

批次 8:
  奖励值: 90.7868
  收益率: 0.5572
  距离: 23.7917
  内存使用: 0.4610
  能量使用: 0.7736
  推理时间: 2.0748秒

批次 9:
  奖励值: 88.9221
  收益率: 0.5560
  距离: 20.5438
  内存使用: 0.4692
  能量使用: 0.7429
  推理时间: 2.0286秒

批次 10:
  奖励值: 79.8588
  收益率: 0.5008
  距离: 22.4407
  内存使用: 0.4060
  能量使用: 0.6357
  推理时间: 1.8745秒

批次 11:
  奖励值: 91.7926
  收益率: 0.5637
  距离: 28.5433
  内存使用: 0.5215
  能量使用: 0.7500
  推理时间: 2.3058秒

批次 12:
  奖励值: 83.0757
  收益率: 0.5254
  距离: 25.1951
  内存使用: 0.3446
  能量使用: 0.7088
  推理时间: 2.0790秒

批次 13:
  奖励值: 82.4112
  收益率: 0.5251
  距离: 24.7145
  内存使用: 0.3897
  能量使用: 0.6735
  推理时间: 2.0095秒

批次 14:
  奖励值: 89.0200
  收益率: 0.5581
  距离: 24.6540
  内存使用: 0.3987
  能量使用: 0.7073
  推理时间: 2.0618秒

批次 15:
  奖励值: 85.4699
  收益率: 0.5525
  距离: 25.9121
  内存使用: 0.3994
  能量使用: 0.7487
  推理时间: 2.0234秒

批次 16:
  奖励值: 80.0637
  收益率: 0.5290
  距离: 23.0565
  内存使用: 0.3302
  能量使用: 0.6860
  推理时间: 1.8803秒

批次 17:
  奖励值: 88.3345
  收益率: 0.5454
  距离: 24.0355
  内存使用: 0.3860
  能量使用: 0.7095
  推理时间: 2.0807秒

批次 18:
  奖励值: 82.0973
  收益率: 0.5437
  距离: 22.7505
  内存使用: 0.3581
  能量使用: 0.6969
  推理时间: 1.9220秒

批次 19:
  奖励值: 85.6431
  收益率: 0.5358
  距离: 26.5690
  内存使用: 0.4231
  能量使用: 0.7217
  推理时间: 2.0159秒

批次 20:
  奖励值: 86.4469
  收益率: 0.5667
  距离: 22.1042
  内存使用: 0.4456
  能量使用: 0.7563
  推理时间: 2.0364秒

批次 21:
  奖励值: 75.3931
  收益率: 0.4896
  距离: 22.6390
  内存使用: 0.3585
  能量使用: 0.6163
  推理时间: 1.7654秒

批次 22:
  奖励值: 91.9058
  收益率: 0.5583
  距离: 21.0291
  内存使用: 0.4318
  能量使用: 0.7276
  推理时间: 2.0346秒

批次 23:
  奖励值: 91.2380
  收益率: 0.5625
  距离: 26.1064
  内存使用: 0.4363
  能量使用: 0.7812
  推理时间: 2.1249秒

批次 24:
  奖励值: 86.9591
  收益率: 0.5333
  距离: 18.6046
  内存使用: 0.3855
  能量使用: 0.6917
  推理时间: 1.9011秒

批次 25:
  奖励值: 84.1579
  收益率: 0.5341
  距离: 24.6415
  内存使用: 0.3563
  能量使用: 0.6865
  推理时间: 1.9465秒

批次 26:
  奖励值: 80.4017
  收益率: 0.5108
  距离: 23.1530
  内存使用: 0.3799
  能量使用: 0.6637
  推理时间: 1.9489秒

批次 27:
  奖励值: 89.7338
  收益率: 0.5559
  距离: 27.3329
  内存使用: 0.4617
  能量使用: 0.7378
  推理时间: 2.0214秒

批次 28:
  奖励值: 66.8511
  收益率: 0.4400
  距离: 19.4074
  内存使用: 0.2568
  能量使用: 0.6336
  推理时间: 1.4728秒

批次 29:
  奖励值: 80.8271
  收益率: 0.5262
  距离: 25.9307
  内存使用: 0.3557
  能量使用: 0.6594
  推理时间: 1.7858秒

批次 30:
  奖励值: 97.8818
  收益率: 0.5903
  距离: 25.9111
  内存使用: 0.5013
  能量使用: 0.8240
  推理时间: 2.1453秒

批次 31:
  奖励值: 88.3379
  收益率: 0.5536
  距离: 21.5394
  内存使用: 0.4770
  能量使用: 0.7078
  推理时间: 2.1422秒

批次 32:
  奖励值: 90.5239
  收益率: 0.5571
  距离: 23.7752
  内存使用: 0.4455
  能量使用: 0.7315
  推理时间: 2.1114秒

批次 33:
  奖励值: 81.6341
  收益率: 0.5273
  距离: 24.6973
  内存使用: 0.3514
  能量使用: 0.6830
  推理时间: 1.9974秒

批次 34:
  奖励值: 84.7443
  收益率: 0.5304
  距离: 21.3794
  内存使用: 0.4592
  能量使用: 0.6728
  推理时间: 1.9754秒

批次 35:
  奖励值: 88.2786
  收益率: 0.5500
  距离: 23.6519
  内存使用: 0.4596
  能量使用: 0.7313
  推理时间: 2.1067秒

批次 36:
  奖励值: 84.8886
  收益率: 0.5337
  距离: 22.1656
  内存使用: 0.3506
  能量使用: 0.7066
  推理时间: 2.0573秒

批次 37:
  奖励值: 84.4428
  收益率: 0.5592
  距离: 25.3154
  内存使用: 0.4500
  能量使用: 0.7489
  推理时间: 2.1176秒

批次 38:
  奖励值: 88.4009
  收益率: 0.5540
  距离: 24.2107
  内存使用: 0.4583
  能量使用: 0.7291
  推理时间: 2.1231秒

批次 39:
  奖励值: 76.4760
  收益率: 0.4989
  距离: 23.6406
  内存使用: 0.3459
  能量使用: 0.6615
  推理时间: 1.8049秒

批次 40:
  奖励值: 79.6631
  收益率: 0.5147
  距离: 21.8423
  内存使用: 0.3856
  能量使用: 0.6458
  推理时间: 1.9349秒

批次 41:
  奖励值: 80.5492
  收益率: 0.5322
  距离: 26.1034
  内存使用: 0.3222
  能量使用: 0.6759
  推理时间: 1.9734秒

批次 42:
  奖励值: 88.1687
  收益率: 0.5304
  距离: 22.9376
  内存使用: 0.4317
  能量使用: 0.6934
  推理时间: 2.0713秒

批次 43:
  奖励值: 92.7009
  收益率: 0.5589
  距离: 23.2307
  内存使用: 0.4646
  能量使用: 0.7029
  推理时间: 2.1732秒

批次 44:
  奖励值: 92.9887
  收益率: 0.5924
  距离: 25.5152
  内存使用: 0.4926
  能量使用: 0.8149
  推理时间: 2.3652秒

批次 45:
  奖励值: 82.4939
  收益率: 0.5215
  距离: 20.9101
  内存使用: 0.3462
  能量使用: 0.7560
  推理时间: 2.0354秒

批次 46:
  奖励值: 91.9028
  收益率: 0.5852
  距离: 29.2783
  内存使用: 0.4978
  能量使用: 0.8183
  推理时间: 2.3216秒

批次 47:
  奖励值: 80.2196
  收益率: 0.5092
  距离: 21.7319
  内存使用: 0.3711
  能量使用: 0.6578
  推理时间: 1.9654秒

批次 48:
  奖励值: 89.2977
  收益率: 0.5575
  距离: 25.6306
  内存使用: 0.4429
  能量使用: 0.7312
  推理时间: 2.1756秒

批次 49:
  奖励值: 78.8163
  收益率: 0.5171
  距离: 23.1561
  内存使用: 0.3540
  能量使用: 0.6574
  推理时间: 1.8917秒

批次 50:
  奖励值: 95.7157
  收益率: 0.5838
  距离: 24.6993
  内存使用: 0.5082
  能量使用: 0.7611
  推理时间: 2.2257秒

批次 51:
  奖励值: 90.7891
  收益率: 0.5683
  距离: 23.6460
  内存使用: 0.4383
  能量使用: 0.7375
  推理时间: 2.1146秒

批次 52:
  奖励值: 85.7941
  收益率: 0.5665
  距离: 25.6648
  内存使用: 0.4167
  能量使用: 0.7304
  推理时间: 2.1780秒

批次 53:
  奖励值: 90.4800
  收益率: 0.5858
  距离: 24.0916
  内存使用: 0.4480
  能量使用: 0.7004
  推理时间: 2.1328秒

批次 54:
  奖励值: 82.8374
  收益率: 0.5097
  距离: 22.5953
  内存使用: 0.4404
  能量使用: 0.6895
  推理时间: 1.8390秒

批次 55:
  奖励值: 92.4971
  收益率: 0.5577
  距离: 25.2469
  内存使用: 0.5163
  能量使用: 0.8427
  推理时间: 2.2869秒

批次 56:
  奖励值: 82.0723
  收益率: 0.5207
  距离: 21.2008
  内存使用: 0.3475
  能量使用: 0.7071
  推理时间: 1.9618秒

批次 57:
  奖励值: 84.4517
  收益率: 0.5358
  距离: 24.8205
  内存使用: 0.3685
  能量使用: 0.7076
  推理时间: 2.0276秒

批次 58:
  奖励值: 84.8933
  收益率: 0.5424
  距离: 19.7758
  内存使用: 0.4043
  能量使用: 0.6333
  推理时间: 1.9034秒

批次 59:
  奖励值: 81.7646
  收益率: 0.5342
  距离: 25.8073
  内存使用: 0.3956
  能量使用: 0.6679
  推理时间: 1.9813秒

批次 60:
  奖励值: 87.4978
  收益率: 0.5472
  距离: 22.0763
  内存使用: 0.3690
  能量使用: 0.7591
  推理时间: 2.0023秒

批次 61:
  奖励值: 89.5433
  收益率: 0.5483
  距离: 21.7136
  内存使用: 0.4592
  能量使用: 0.7407
  推理时间: 2.0729秒

批次 62:
  奖励值: 93.0020
  收益率: 0.5713
  距离: 25.1231
  内存使用: 0.5221
  能量使用: 0.7508
  推理时间: 2.2064秒

批次 63:
  奖励值: 92.1988
  收益率: 0.5557
  距离: 23.9851
  内存使用: 0.4727
  能量使用: 0.7447
  推理时间: 2.1560秒

批次 64:
  奖励值: 85.9876
  收益率: 0.5658
  距离: 25.5301
  内存使用: 0.4246
  能量使用: 0.6701
  推理时间: 2.0245秒

批次 65:
  奖励值: 82.7447
  收益率: 0.5366
  距离: 25.6501
  内存使用: 0.3499
  能量使用: 0.7107
  推理时间: 1.9232秒

批次 66:
  奖励值: 94.2899
  收益率: 0.5676
  距离: 22.6467
  内存使用: 0.4436
  能量使用: 0.7300
  推理时间: 2.1734秒

批次 67:
  奖励值: 97.4207
  收益率: 0.5863
  距离: 28.6936
  内存使用: 0.4671
  能量使用: 0.7979
  推理时间: 2.3493秒

批次 68:
  奖励值: 88.4593
  收益率: 0.5396
  距离: 23.5579
  内存使用: 0.4140
  能量使用: 0.7095
  推理时间: 1.9326秒

批次 69:
  奖励值: 83.0338
  收益率: 0.5433
  距离: 23.3106
  内存使用: 0.4044
  能量使用: 0.8175
  推理时间: 2.0905秒

批次 70:
  奖励值: 83.7017
  收益率: 0.5368
  距离: 23.8366
  内存使用: 0.4363
  能量使用: 0.7238
  推理时间: 1.9984秒

批次 71:
  奖励值: 88.4670
  收益率: 0.5410
  距离: 21.2841
  内存使用: 0.4359
  能量使用: 0.7461
  推理时间: 2.0284秒

批次 72:
  奖励值: 92.2756
  收益率: 0.5493
  距离: 24.8254
  内存使用: 0.4433
  能量使用: 0.7802
  推理时间: 2.2066秒

批次 73:
  奖励值: 77.8665
  收益率: 0.4786
  距离: 20.3147
  内存使用: 0.3155
  能量使用: 0.6368
  推理时间: 1.7771秒

批次 74:
  奖励值: 72.3688
  收益率: 0.4859
  距离: 23.2422
  内存使用: 0.3422
  能量使用: 0.6780
  推理时间: 1.8752秒

批次 75:
  奖励值: 91.7641
  收益率: 0.5679
  距离: 27.6769
  内存使用: 0.4499
  能量使用: 0.7450
  推理时间: 2.0715秒

批次 76:
  奖励值: 85.8765
  收益率: 0.5432
  距离: 26.9316
  内存使用: 0.4189
  能量使用: 0.6966
  推理时间: 1.9277秒

批次 77:
  奖励值: 88.9177
  收益率: 0.5439
  距离: 21.6978
  内存使用: 0.3601
  能量使用: 0.7068
  推理时间: 1.8949秒

批次 78:
  奖励值: 85.3215
  收益率: 0.5361
  距离: 22.6880
  内存使用: 0.4029
  能量使用: 0.6661
  推理时间: 1.9353秒

批次 79:
  奖励值: 85.7373
  收益率: 0.5293
  距离: 24.1547
  内存使用: 0.4288
  能量使用: 0.7420
  推理时间: 2.0772秒

批次 80:
  奖励值: 91.0183
  收益率: 0.5766
  距离: 25.3222
  内存使用: 0.4983
  能量使用: 0.7430
  推理时间: 2.1181秒

批次 81:
  奖励值: 90.3220
  收益率: 0.5711
  距离: 26.9057
  内存使用: 0.5178
  能量使用: 0.7326
  推理时间: 2.1342秒

批次 82:
  奖励值: 84.4878
  收益率: 0.5474
  距离: 25.1337
  内存使用: 0.4240
  能量使用: 0.6667
  推理时间: 2.0209秒

批次 83:
  奖励值: 86.5991
  收益率: 0.5437
  距离: 24.6837
  内存使用: 0.3959
  能量使用: 0.7221
  推理时间: 2.0390秒

批次 84:
  奖励值: 85.4619
  收益率: 0.5588
  距离: 24.7352
  内存使用: 0.3909
  能量使用: 0.7501
  推理时间: 2.0465秒

批次 85:
  奖励值: 92.2171
  收益率: 0.5795
  距离: 24.9617
  内存使用: 0.4722
  能量使用: 0.7319
  推理时间: 2.1540秒

批次 86:
  奖励值: 85.6857
  收益率: 0.5494
  距离: 23.0644
  内存使用: 0.4080
  能量使用: 0.7563
  推理时间: 2.0113秒

批次 87:
  奖励值: 92.5386
  收益率: 0.5712
  距离: 23.0712
  内存使用: 0.4730
  能量使用: 0.7785
  推理时间: 2.1173秒

批次 88:
  奖励值: 82.7216
  收益率: 0.5142
  距离: 22.0786
  内存使用: 0.3732
  能量使用: 0.7051
  推理时间: 1.9375秒

批次 89:
  奖励值: 91.9865
  收益率: 0.5727
  距离: 26.6442
  内存使用: 0.4786
  能量使用: 0.7042
  推理时间: 2.1969秒

批次 90:
  奖励值: 82.7865
  收益率: 0.5234
  距离: 25.3309
  内存使用: 0.3798
  能量使用: 0.7163
  推理时间: 1.9970秒

批次 91:
  奖励值: 78.2555
  收益率: 0.5158
  距离: 24.9550
  内存使用: 0.4027
  能量使用: 0.7313
  推理时间: 1.8964秒

批次 92:
  奖励值: 81.8798
  收益率: 0.5302
  距离: 22.9424
  内存使用: 0.4094
  能量使用: 0.6885
  推理时间: 1.8289秒

批次 93:
  奖励值: 88.1366
  收益率: 0.5529
  距离: 24.2595
  内存使用: 0.4032
  能量使用: 0.6640
  推理时间: 2.0084秒

批次 94:
  奖励值: 89.0401
  收益率: 0.5376
  距离: 22.5052
  内存使用: 0.4542
  能量使用: 0.7605
  推理时间: 2.0470秒

批次 95:
  奖励值: 84.3358
  收益率: 0.5176
  距离: 21.6747
  内存使用: 0.3794
  能量使用: 0.6744
  推理时间: 1.9317秒

批次 96:
  奖励值: 81.8300
  收益率: 0.5279
  距离: 24.3155
  内存使用: 0.4007
  能量使用: 0.6239
  推理时间: 1.9061秒

批次 97:
  奖励值: 88.7580
  收益率: 0.5349
  距离: 20.3939
  内存使用: 0.3976
  能量使用: 0.6924
  推理时间: 1.9361秒

批次 98:
  奖励值: 87.9677
  收益率: 0.5657
  距离: 27.0458
  内存使用: 0.4741
  能量使用: 0.7447
  推理时间: 2.0541秒

批次 99:
  奖励值: 80.1144
  收益率: 0.5225
  距离: 21.8476
  内存使用: 0.4501
  能量使用: 0.6734
  推理时间: 1.8836秒

批次 100:
  奖励值: 93.2873
  收益率: 0.5805
  距离: 25.5140
  内存使用: 0.5121
  能量使用: 0.7993
  推理时间: 2.0897秒


==================== 总结 ====================
平均收益率: 0.5418
平均能量使用: 0.7154
平均推理时间: 2.0360秒
